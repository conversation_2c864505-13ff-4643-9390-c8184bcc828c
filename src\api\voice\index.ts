import Queue from './queue';
import Play from './play';
import type { CardDataType } from '@/types/api';

export default {
    Queue,
    Play,
    add: (aiDataCard: CardDataType, stream?: any) => {
        Queue.enqueue(aiDataCard);
        return Play;
    },
    init: (aiDataCard: CardDataType, stream?: any) => {
        Play.stop();
        Queue.clear();
        Queue.enqueue(aiDataCard);
        return Play;
    },
    stop: async () => {
        await Play.stop();
        Queue.clear();
    },
    // init:async (aiDataCard, stream)=>{
    //     await Play.stop();
    //     Queue.clear();
    //     Queue.enqueue(aiDataCard, stream);
    //     return Play;
    // }
};