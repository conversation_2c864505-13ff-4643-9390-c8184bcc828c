import store from "./store";
import utils from "../utils";
import action from "./action";
import requests from "./requests";
import voice from "./voice";
import type { App } from "vue";
/**
 * 初始化app
 * @param {string} message
 * @returns
 */
async function init() {
  console.log('init');
}

export default {
  store,
  utils,
  action,
  requests,
  voice,
  install: (app: App, options?: any) => {
    app.provide('store', store);
  },
  init
};
