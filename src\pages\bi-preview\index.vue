<template>
  <div class="preview-container" :style="computedStyle">
    <LayoutHeader>
      <DataHeader :title="title"></DataHeader>
    </LayoutHeader>
    <div class="preview-chart-container">
      <ComiExternalBiCardDialog
        :data="previewData"
        ref="biCardRef"
        :card-options="biCardOptions"
        @update:card-options="cardOptionsChange"
      ></ComiExternalBiCardDialog>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getLocalStorage, getQueryString, setPagePadding } from '@/utils/common';
import { ref, computed, provide,onMounted, onBeforeUnmount } from 'vue';
import LayoutHeader from '@/components/layout/layout-header.vue';
import DataHeader from '@/components/data-header/preview-header.vue';
import "@seeyon/seeyon-comi-plugins-library/dist/seeyon-comi-plugins-library.css";
import { paddingType } from '@/types/api';
const contentHeight = ref('100%');
provide('contentHeight', contentHeight);

const previewType = ref('');
const computedStyle = ref<paddingType>({})
// 获取缓存的预览数据
const type = getQueryString('type');
previewType.value = type;
const layoutHeaderHeight = getQueryString('layoutHeaderHeight');

const biCardOptions = ref({
  status: false,
  lookSql: false,
  canLookSql: true,
  typeSource: 'appDialog'
})

contentHeight.value = `${(window.innerHeight - (Number(layoutHeaderHeight) + 64))}`; // 64为头部高度44+设置距离底部高度
const previewData = getLocalStorage(type);

let title = ref('');

const cardOptionsChange = (key: any, value: any) => {
  if(key === 'title'){
    title.value = value;
  }
}

onMounted(() => {
  ZYJSBridge.setEventListener('setPageSize', setPageSize);
});
function setPageSize(data: any) {
  computedStyle.value = setPagePadding(data);
  contentHeight.value = `${(window.innerHeight - (Number(computedStyle.value?.paddingTop) + 64))}`;
}
onBeforeUnmount(()=>{
  ZYJSBridge.removeEventListener('setPageSize', setPageSize);
})
</script>

<style lang="scss" scoped>
.preview-container {
  box-sizing: border-box;
  height: 100%;
  width: 100%;
  padding: 12px;
}
.data-header{
  margin-bottom: 12px;
}
</style>