// 助手信息
export type AssistantInfoType = {
  id: string;
  code: string;
  name: string;
  introduce: string;
  iconUrl: string;
  prologue?: string;
  prologuePreQuestions?: ProloguePreQuestionsItem[];
  [key: string]: any;
}

export type ChartTableType = {
  column_names: string[];
  data: string[][];
  title: string;
  type: string;
}

// 操作按钮
export type OperateItemType = {
  id: string;
  icon: string;
  type?: string;
  name: string
}

// 开场白问题类型
export type ProloguePreQuestionsItem = {
  id: string;
  name: string;
  assistantId: string;
}