<template>
  <div class="ai-state-loading" :class="{'inline': inline}" :style="iconStyle"></div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'AiStateLoading'
});

const props = defineProps({
  inline: {
    type: Boolean,
    default: false,
  },
  iconStyle: {
    type: Object,
    default: () => ({}),
  }
})
</script>

<style lang="scss" scoped>
.ai-state-loading {
  width: 16px;
  height: 16px;
  background-image: url('../../../assets/images/send-loading.png');
  background-size: contain;
  animation: spin 1s linear infinite;
  &.inline {
    display: inline-block;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
