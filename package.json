{"name": "h5-app", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"@microsoft/fetch-event-source": "^2.0.1", "@originjs/vite-plugin-federation": "^1.4.1", "@seeyon/seeyon-comi-plugins-library": "^1.0.84", "core-js": "^3.8.3", "echarts": "^5.5.1", "markdown-it": "~14.1.0", "remove-markdown": "^0.6.0", "vite-plugin-top-level-await": "^1.5.0", "vue": "^3.2.13"}, "devDependencies": {"@types/node": "^22.13.15", "@vitejs/plugin-vue": "^5.0.0", "autoprefixer": "^10.4.21", "axios": "^1.7.9", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.21.1", "sass": "^1.86.0", "typescript": "^5.0.4", "unplugin-auto-import": "^19.1.2", "vite": "5.4.18", "vite-plugin-compression": "^0.5.1", "vite-plugin-html": "^3.2.2", "vue-tsc": "^2.2.10"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}