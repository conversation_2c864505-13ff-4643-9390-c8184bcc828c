<template>
  <div class="knowledge-drawer">
    <AiDrawer :visible="visible" @closeDrawer="closeDrawer" @afterClose="drawerLeave" :drawerClass="drawerClass">
      <template #header>
        <div class="header-wrapper"  @touchstart="touchStart">
          <div class="close"></div>
        </div>
      </template>
      <template #content>
        <div class="knowledge-content" ref="knowledgeContent">
          <div class="knowledge-title">
            <div class="subject">参考知识源</div>
            <!-- <div class="get-schemes">生成方案</div> -->
          </div>
          <div class="knowledge-type" 
            ref="knowledgeType" 
            @scroll="handleScroll">
            <div class="type-item" 
              ref="typeItem"
              v-for="(item, index) in knowledgeData" 
              :key="item.id" 
              :class="{'selected': currentSelectId === item.id}" 
              @click="changeType(item, index)">
              <div class="type-name">{{ item.label }}</div>
              <div class="type-dot">.</div>
              <div class="type-number">{{ item.number }}</div>
            </div>
          </div>
          <div class="knowledge-list" ref="knowledgeListDom">
            <div class="list-item" 
                 v-for="list in knowledgeList()" 
                 :key="list.id"
                 @click="handleKnowledgeClick(list)">
              <div class="list-header">
                <div class="list-subject clamp-overflow">
                  <span class="iconfont icon-class" :class="list.iconClass"></span>
                  <span>{{list.title}}</span>
                </div>
                <div class="list-same">
                  <div class="list-attachment" v-if="list.accessoryName">
                    <!-- <div class="attachment"> -->
                      <span class="iconfont ai-icon-fujian2"></span>
                    <!-- </div> -->
                  </div>
                  <span>
                    <span>相似性:</span>
                    <span style="margin-left: 4px;">{{list.similarity}}</span>
                  </span>
                  <!-- <span>{{list.similarity}}%</span> -->
                </div>
              </div>
              <div class="list-content clamp-overflow">{{ list.content }}</div>
              <!-- <div class="list-attachment">
                <div class="attachment">
                  <span class="iconfont ai-icon-fujian2"></span>
                  <div class="name text-overflow">11111哈哈哈哈哈啊哈哈嘿嘿嘿呵呵</div>
                </div>
                <div class="number">等9121个附件</div>
              </div> -->
              <div class="list-line"></div>
              <div class="list-bottom">
                <div class="bottom-item">
                  <span class="gap-right">来源:</span>
                  <span class="text-result text-overflow">
                    {{list.appTypeDsc}}
                  </span>
                </div>
                <div class="split"></div>
                <div class="bottom-item">
                  <span class="gap-right">发起人:</span>
                  <span class="text-result  text-overflow">{{list.author}}</span>
                </div>
                <div class="split"></div>
                <div class="bottom-item">
                  <span class="gap-right">创建时间:</span>
                  <span>{{list.createDate}}</span>
                </div>
              </div>
            </div>
          </div>
          <div v-if="isShowBlur && showLeftBlur" class="left-blur"></div>
          <div v-if="isShowBlur && showRightBlur" class="right-blur"></div>
        </div>
      </template>
      <template #footer>
        <!-- <LayoutFooter
          :buttonText="buttonText"
          :type="buttonType"
          :disabled="disabled"
          @buttonAction="submitAction"
        ></LayoutFooter> -->
      </template>
    </AiDrawer>
  </div>
</template>

<script setup lang="ts">
import {ref, onMounted } from 'vue';
import API from "@/api/index";
import { getQueryString } from '@/utils/common';
import AiDrawer from '@/components/ui/ai-drawer/index.vue';
import { KnowledgeDataItem } from '@/types/api';
import {handleKnowledgeClick} from '@/utils/handle-knowledge';

const props = defineProps({
  knowledgeData: {
    type: Array<KnowledgeDataItem>,
    default: () => [],
  },
});
console.log(props.knowledgeData, "drawerreeeee")
// MockData 
  // const data ={
  //   "code": "0",
  //   "message": "success",
  //   "data": {
  //     "3": [
  //         {
  //             "title": "劳动节放假通知",
  //             "content": "劳动节的放假时间为5月1日至五月5日，特此公告",
  //             "author": "cs",
  //             "entityId": "8931257230309936016",
  //             "createDate": "1745482862000",
  //             "accessoryName": "",
  //             "type": "文档",
  //             "similarity": 79.652715,
  //             "appType": 3
  //         }
  //     ],
  //     "7": [
  //         {
  //             "title": "劳动节放假通知",
  //             "content": "劳动节的放假时间为5月1日至五月5日，特此公告",
  //             "author": "cs",
  //             "entityId": "-4353654998942790085",
  //             "createDate": "1745479402000",
  //             "accessoryName": "",
  //             "type": "公告",
  //             "similarity": 80.106884,
  //             "appType": 7
  //         },
  //         {
  //             "title": "劳动节放假时间通知",
  //             "content": "劳动节的放假时间为5.1-5.5， 特此公告",
  //             "author": "cs",
  //             "entityId": "-3631740021110125357",
  //             "createDate": "1745479166000",
  //             "accessoryName": "",
  //             "type": "公告",
  //             "similarity": 79.74354,
  //             "appType": 7
  //         }
  //     ]
  //   }
  // }
const drawerClass = 'knowledge-class';

const _$emit = defineEmits(['confirm', 'close', 'update:showKnowledge']);
let visible = ref(true);
const layoutHeaderHeight = (API.store.state.systemStyle.top || getQueryString('layoutHeaderHeight')) + 44 + 'px';

const currentSelectId = ref('all');
let positionY = 0;
const isShowBlur = ref(false);
const showLeftBlur = ref(false);
const showRightBlur = ref(false);
const knowledgeContent = ref<HTMLDivElement | null> (null);
// 在script setup顶部
const knowledgeType = ref<HTMLDivElement | null>(null);
const typeItem = ref<HTMLDivElement[]>([]);
const knowledgeListDom = ref<HTMLDivElement | null>(null);
onMounted(()=>{
  const clientWidth = knowledgeType?.value?.clientWidth || 0;
  const scrollWidth = knowledgeType?.value?.scrollWidth || 0;
  if(scrollWidth > clientWidth) {
    isShowBlur.value = true;
    showRightBlur.value = true;
  }
  const drawerContainer = document.querySelector('.ai-drawer-container') as HTMLElement;
  drawerContainer.style.height = `${drawerContainer.clientHeight}px`;
});
function knowledgeList() {
  const showList = props.knowledgeData.filter((item)=> item.id === currentSelectId.value);

  return showList[0]?.knowledgeArr || [];
}

function touchStart(e: TouchEvent) {
  // const touchs  = e.touches[0];
  // positionY = touchs.clientY;
  closeDrawer();
}
// function touchMove(e: TouchEvent) {
//   // const touchs  = e.touches[0];
//   // if(touchs.clientY - positionY > 4) {
//   //   closeDrawer();
//   // }
// }

function setBlur(showLeftValue: boolean, showRightValue: boolean) {
  showLeftBlur.value = showLeftValue;
  showRightBlur.value = showRightValue;
}
function handleScroll(e: Event) {
  const knowledgeTypeDom = knowledgeType?.value;
  const scrollLeft = knowledgeTypeDom?.scrollLeft || 0;
  const clientWidth = knowledgeTypeDom?.clientWidth || 0;
  const caculateScroll = (knowledgeTypeDom?.scrollWidth || 0) - (scrollLeft || 0);
  if(scrollLeft === 0) {
    setBlur(false, true);
  }else if((caculateScroll - clientWidth) <= 1) {
    setBlur(true, false);
  }else if(scrollLeft > 0 && caculateScroll > clientWidth) {
    setBlur(true, true);
  }
}
function closeDrawer() {
  visible.value = false;
  // _$emit('close');
  // _$emit('update:isShowDrawer', false);
}
function drawerLeave() { 
  _$emit('close');
  _$emit('update:showKnowledge', false);
}
function changeType(item: any, index: number) {
  const scrollDom = knowledgeListDom?.value;
  scrollDom && (scrollDom.scrollTop = 0);
  typeItem?.value[index]?.scrollIntoView({
    behavior: 'smooth',
    block: 'center',
    inline: 'center',
  });
  currentSelectId.value = item.id;
}

</script>

<style lang="scss" scoped>
.knowledge-drawer {
  display: flex;
  .header-wrapper {
    height: 22px;
    line-height: 22px;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .close {
    width: 48px;
    height: 6px;
    border-radius: 3px;
    background: rgb(217, 217, 217);
  }
  .knowledge-title {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
    .subject {
      font-size: 16px;
      font-size: var(--theme-font-size2, 16px);
      line-height: 24px;
      line-height: var(--theme-line-height2, 24px);
      font-weight: 600;
      color: rgba(0, 0, 0, 1);
      color: var(--theme-font-color0, rgba(0, 0, 0, 1));
    }
    .get-schemes {
      // width: 72px;
      height: 24px;
      box-sizing: border-box;
      padding: 2px 12px;
      border: 1px solid #4379FF;
      font-size: 12px;
      border-radius: 12px;
      color: #4379FF;
    }
  }
  .knowledge-content {
    position: relative;
    display: flex;
    flex-direction: column;
    height: 100%;
    min-height: 100px;
    .left-blur {
      width: 32px;
      height: 33px;
      position: absolute;
      background: linear-gradient(90deg, #F7F7F7 0%, rgba(247, 247, 247, 0) 100%);
      left: -1px;
      top: 36px;
    }
    .right-blur {
      width: 32px;
      height: 33px;
      position: absolute;
      background: linear-gradient(270deg, #F7F7F7 0%, rgba(247, 247, 247, 0) 100%);
      right: -2px;
      top: 36px;
    }
  }
  .knowledge-type {
    width: 100%;
    overflow: auto;
    height: 32px;
    display: flex;
    flex-wrap: nowrap;
    min-height: 32px;
    .type-item {
      display: flex;
      padding: 5px 8px;
      margin-left: 8px;
      background: #fff;
      border-radius: 8px;
      flex-shrink: 0;
      line-height: 22px;
      color: rgba(0, 0, 0, 1);
      color: var(--theme-font-color0, rgba(0, 0, 0, 1));
      font-size: 14px;
      font-size: var(--theme-font-size1, 14px);
      align-items: center;
      &:first-child {
        margin-left: 0;
      }

      .type-dot {
        margin: 0 2px;
        font-size: 20px;
        transform: translateY(-5px);
      }
    }
    .selected {
      background: #4379FF;
      color: #fff;
    }
  }
  .knowledge-list {
    margin-top: 12px;
    flex: 1;
    overflow-y: auto;
    .list-item {
      background: #fff;
      padding: 8px 12px;
      border-radius: 8px;
      margin-bottom: 12px;
      &:last-child {
        margin-bottom: 0;
      }
    }
    .list-header {
      display: flex;
      line-height: 24px;
    }
    .list-subject {
      flex: 1;
      margin-right: 16px;
      color: #25262C;
      font-size: 16px;
      font-size: var(--theme-font-size2, 16px);
      font-weight: 600;
      max-height: 48px;
    }
    .clamp-overflow {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2; // 控制显示行数
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .text-overflow {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    } 
    .list-same {
      display: flex;
      height: 24px;
      font-size: 12px;
      font-size: var(--theme-font-size0, 12px);
      line-height: 24px;
      line-height: var(--theme-line-height2, 24px);
      color: rgba(0, 0, 0, 0.4);
      color: var(--theme-font-color3, rgba(0, 0, 0, 0.4));
      align-items: center;
    }
    .icon-class {
      // line-height: 16px;
      font-weight: 400;
      margin-right: 6px;
      color: #4379FF;
    }
    .list-content {
      max-height: 44px;
      font-size: 14px;
      font-size: var(--theme-font-size1, 14px);
      line-height: 22px;
      line-height: var(--theme-line-height1, 22px);
      color: rgba(0, 0, 0, 0.6);
      color: var(--theme-font-color2, rgba(0, 0, 0, 0.6));
    }
    .list-attachment {
      // display: flex;
      height: 18px;
      line-height: 18px;
      width: 20px;
      color: rgba(0, 0, 0, 0.6);
      color: var(--theme-font-color2, rgba(0, 0, 0, 0.6));
      font-size: 12px;
      font-size: var(--theme-font-size0, 12px);
      // align-items: center;
      background: rgba(67, 121, 255, 0.1);
      border-radius: 4px;
      margin-right: 4px;
      // .attachment {
      //   flex: 1;
      //   display: flex;
      //   padding: 0 8px;
      //   align-items: center;
      //   height: 100%;
      //   min-width: 20px;
      //   max-width: max-content;
        .ai-icon-fujian2 {
          color: #4379FF;
          font-size: 14px;
          margin-left: 3px;
        }
        .name {
          flex: 1
        }
      // }
    }
    .list-line {
      height: 1px;
      margin: 0 16px;
      background-color: #D8DADF;
      margin: 8px 0;
      transform: scaleY(0.5);
    }
    .list-bottom {
      display: flex;
      align-items: center;
      font-size: 12px;
      font-size: var(--theme-font-size0, 12px);
      color: rgba(0,0,0,0.4);
      color: var(--theme-font-color3, rgba(0, 0, 0, 0.4));
      line-height: 20px;
      line-height: var(--theme-line-height0, 20px);
      .gap-right {
        margin-right: 4px;
      }
      .bottom-item {
        display: flex;
      }
    }
    .text-result {
      max-width: 50px;
    }
    .split {
      height: 12px;
      width: 1px;
      transform: scaleX(0.5);
      background-color: #D8DADF;
      margin: 0 8px;
    }
  }
}
</style>

<style lang="scss"> 
.knowledge-class.ai-drawer {
  .ai-drawer-container {
    box-sizing: border-box;
    padding: 0px;
    max-height: calc(100% - v-bind(layoutHeaderHeight));
    background-color: #f7f7f7;
    .header {
      padding: 0 16px;
      margin-bottom: 4px;
      justify-content: center;
    }
    .content {
      display: flex;
      flex-direction: column;
      height: 100%;
      overflow: hidden;
      padding: 0 16px;
    }
  }
}
::-webkit-scrollbar {
  display: none;
}
</style>