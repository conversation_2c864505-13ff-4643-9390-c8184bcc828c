const internalVersion = 2;

function jsbridge() {
  this.desktopBridge = undefined;
  this.cache = {};
  this.events = new Map(); //事件回调的map

  this.readyActions = [];
  this.ready = false;

  setTimeout(() => {
    //最多等待10秒
    this.ready = true;
    this.readyActions.forEach(action => {
      action();
    });
    this.readyActions = [];
  }, 10000);

  const pieceParamMap = new Map();

  const tryMerge = (id, response, pieceParam) => {
    if (!pieceParam) {
      return response;
    } else {
      response = typeof response === 'string' ? JSON.parse(response) : response;
      if (typeof pieceParam === 'string') {
        pieceParam = JSON.parse(pieceParam);
      }
      let pieceMap = pieceParamMap.get(id);
      if (pieceParam.total === ((pieceMap && pieceMap.size) || 0) + 1) {
        let fix = '';
        for (let i = 0; i < pieceParam.total; i++) {
          if (i === pieceParam.index) {
            fix = fix + response.str;
          } else {
            fix = fix + pieceMap.get(i);
          }
        }
        pieceParamMap.delete(id);
        return fix;
      } else {
        pieceMap = pieceMap || new Map();
        pieceMap.set(pieceParam.index, response.str);
        pieceParamMap.set(id, pieceMap);
        return null;
      }
    }
  };

  const nativeCallback = (id, response, keepCallBack, pieceParam) => {
    // console.log('nativeCallback', id, response);
    const cache = this.cache[id] || {},
      cb = cache.cb;
    if (!cache || !cb) {
      //往子iframe传递
      if (window.frames) {
        let index = 0;
        while (window.frames[index]) {
          window.frames[index].ZYJSBridgeNativeToJS &&
            window.frames[index].ZYJSBridgeNativeToJS(id, response, keepCallBack, pieceParam);
          index++;
        }
      }
      return console.warn('callback not found callbackId: "' + id + '"');
    }

    let fixedResponse = tryMerge(id, response, pieceParam);

    if (!fixedResponse) return;

    fixedResponse = typeof fixedResponse === 'string' ? JSON.parse(fixedResponse) : fixedResponse;
    // console.log('native-callback', response);
    if (fixedResponse.code.toString() === '200') {
      cb(fixedResponse.data, true);
    } else {
      cb(fixedResponse.message, false);
    }
    if (!keepCallBack && keepCallBack !== 'true') {
      delete this.cache[id];
    }
  };

  const onEvent = (eventName, data) => {
    // console.log('native-event', eventName, data);
    const action = this.events.get(eventName);
    action &&
      action(data, result => {
        exec({
          plugin: 'EventPlugin',
          action: 'event',
          params: { eventName: eventName + '_m5jsreplay', data: result },
        });
      });

    //往子iframe传递
    if (window.frames) {
      let index = 0;
      while (window.frames[index]) {
        window.frames[index].ZYEventBridge && window.frames[index].ZYEventBridge(eventName, data);
        index++;
      }
    }
  };

  if (
    window.ZYBridge ||
    (window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.ZYJSBridge)
  ) {
    //移动端 插件回调
    window.ZYJSBridgeNativeToJS = nativeCallback;
    //移动端 事件回调
    window.ZYEventBridge = onEvent;
    this.ready = true;
    this.readyActions.forEach(action => {
      action();
    });
    this.readyActions = [];
  } else {
    window.addEventListener('message', ({ source, origin, data }) => {
      const { key, params } = data;
      if (key === 'jsBridgeEvent') {
        //桌面端 事件回调
        const { eventName, data } = params;
        if (eventName === 'm5-jsbridge-origin') {
          this.desktopBridge = { source, origin, webViewId: data };
          this.ready = true;
          this.readyActions.forEach(action => {
            action();
          });
          this.readyActions = [];
          window.postMessage({ id: 'jsBrigdeInited' });
        } else {
          onEvent(eventName, data);
        }
      } else if (key === 'jsBridgeCallBack') {
        //桌面端 插件回调
        const { id, data, keepCallBack } = params;
        nativeCallback(id, data, keepCallBack);
      }
    });
  }

  this.doOnReady = action => {
    if (this.ready) action();
    else this.readyActions.push(action);
  };
}

jsbridge.prototype = {
  exec: function (request) {
    request.internalVersion = internalVersion;
    let action = request.action,
      params = request.params,
      callback = request.callback,
      plugin = request.plugin;
    let id = (request.id = Math.floor(Math.random() * 10000000000) + '');
    this.cache[id] = {
      action: action,
      plugin: plugin,
      params: params,
      cb: callback || function () {},
    };
    this.doOnReady(() => {
      if (this.desktopBridge) {
        //桌面端
        this.desktopBridge.source &&
          this.desktopBridge.origin &&
          this.desktopBridge.source.postMessage(
            {
              id,
              plugin,
              action,
              params,
              webViewId: this.desktopBridge.webViewId,
              internalVersion,
            },
            this.desktopBridge.origin
          );
      } else if (
        window.webkit &&
        window.webkit.messageHandlers &&
        window.webkit.messageHandlers.ZYJSBridge
      ) {
        //ios
        window.webkit.messageHandlers.ZYJSBridge.postMessage(JSON.stringify(request));
      } else if (window.ZYBridge) {
        //android
        window.ZYBridge.exec(JSON.stringify(request));
      } else if (window.electron) {
        //m5桌面端壳直接集成插件
        window.postMessage({ id, plugin, action, params, webViewId: 'inshell', internalVersion });
      } else {
        callback && callback('no bridge found,check your context', false);
      }
    });
  },
  syncExec: function (request) {
    if (window.ZYBridge) {
      //android
      return window.ZYBridge.syncExec(JSON.stringify(request));
    } else if (
      window.webkit &&
      window.webkit.messageHandlers &&
      window.webkit.messageHandlers.ZYJSBridge &&
      window.prompt
    ) {
      //ios
      let response = window.prompt('ZYJSBridge', JSON.stringify(request));
      try {
        //返回的是string
        response = JSON.parse(response);
      } catch (error) {}
      return response;
    } else {
      throw 'platform not support sync functions yet';
    }
  },
  setEventListener: function (eventName, callback) {
    this.events.set(eventName, callback);
  },
  removeEventListener: function (eventName) {
    this.events.delete(eventName);
  },
};

if (!window.ZYJSBridge) window.ZYJSBridge = new jsbridge();

function exec(options) {
  let { plugin, action, params, success, error } = options;
  success = success || function () {};
  error = error || function () {};

  return new Promise((resolve, reject) => {
    if (!/m5shell/i.test(window.navigator.userAgent)) {
      resolve({});
    }
    window.ZYJSBridge.exec({
      plugin,
      action,
      params,
      callback: (response, status) => {
        if (status) {
          success(response);
          resolve(response);
        } else {
          error(response);
          reject(response);
        }
      },
    });
  });
}

function syncExec(options) {
  return window.ZYJSBridge.syncExec(options);
}

const shellEvent = {
  sendCrossWebView: (eventName, data) =>
    exec({
      plugin: 'EventPlugin',
      action: 'event',
      params: { eventName, data, cross: true },
    }),
  send: (eventName, data, success, error) =>
    exec({
      plugin: 'EventPlugin',
      action: 'event',
      params: { eventName, data },
      success,
      error,
    }),
  on: (eventName, callback) => {
    window.ZYJSBridge.setEventListener(eventName, callback);
  },
  clear: eventName => {
    window.ZYJSBridge.removeEventListener(eventName);
  },
};

function replaceElement(obj, str, reStr) {
  for (let x in obj) {
    if (typeof obj[x] === 'string') {
      obj[x] = obj[x].replace(str, reStr);
    } else if (typeof obj[x] === 'object') {
      replaceElement(obj[x], str, reStr);
    }
  }
}

function openUrl(action, origin, userId, params) {
  const url =
    window.location.origin +
    customizationUrl() +
    `/m5-cloud/forward?action=${action}&origin=${origin}&userId=${userId}&params=${JSON.stringify(
      params
    ).replaceAll('  ', '')}&lan=${
      localStorage.getItem('I18nLocale')
        ? localStorage.getItem('I18nLocale').replace('-', '_')
        : navigator.language.replace('-', '_')
    }`;
  const id = 'm5ShareOpenId';
  const a = document.createElement('a');
  a.setAttribute('href', url);
  a.setAttribute('target', '_blank');
  a.setAttribute('id', id);
  // 防止反复添加
  if (!document.getElementById(id)) {
    document.body.appendChild(a);
  }
  a.click();

  // window.open(`m5://?action=${action}&origin=${origin}&params=${JSON.stringify(params).replaceAll('  ', '')}`)
}

const getCookie = name => {
  return document.cookie.split('; ').reduce((r, v) => {
    const parts = v.split('=');
    return parts[0] === name ? decodeURIComponent(parts[1]) : r;
  }, '');
};

function openFromBrowser(action, params, success, error) {
  params = params || {};
  const origin = window.origin;
  let userId = '';
  try {
    userId = JSON.parse(localStorage.getItem('currentUser')).userId;
  } catch (e) {}
  const pureParams = { ...params };
  delete pureParams.paramsUpload;
  if (JSON.stringify(pureParams).length > 500) {
    // if (!params.paramsUpload && origin.indexOf('seeyon') < 0) {
    //   error('参数超长，请缩减参数或提供参数上传url地址');
    //   return;
    // }
    if (!params.paramsUpload) {
      //m5默认上传参数
      params.paramsUpload = {
        uploadRequest: {
          type: 'post',
          urlModal:
            origin.replace(
              window.location.protocol + '//',
              window.location.protocol + '//service-'
            ) + '/m5/graphql',
          paramModal: JSON.stringify({
            query: `mutation m5MessageCreateShareContentPost ($body: m5MessageCreateShareContentPostInputBody){
                                                m5MessageCreateShareContentPost(body: $body){
                                                    code
                                                    status
                                                    message
                                                    data {
                                                        content
                                                    }
                                                }
                                            }`,
            variables: {
              body: {
                content: '${param}',
              },
            },
          }),
          toString: true,
          urlPath: '/data/m5MessageCreateShareContentPost/data/content',
        },
        downloadRequest: {
          type: 'post',
          urlModal:
            origin.replace(
              window.location.protocol + '//',
              window.location.protocol + '//service-'
            ) + '/m5/graphql',
          paramModal: JSON.stringify({
            query: `query m5MessageSelectShareContentShareGet ($path: m5MessageSelectShareContentShareGetInputPath){
                                                m5MessageSelectShareContentShareGet(path: $path){
                                                    code
                                                    status
                                                    message
                                                    data {
                                                    content
                                                    }
                                                }
                                            }`,
            variables: {
              path: {
                shareId: '${path}',
              },
            },
          }),
          paramPath: '/data/m5MessageSelectShareContentShareGet/data/content',
        },
      };
    }
    const updateModal = params.paramsUpload.uploadRequest;
    const downloadModal = params.paramsUpload.downloadRequest;
    let requestUrl = updateModal.urlModal;
    let requestParam = updateModal.paramModal;
    let headers = updateModal.headers || {
      'Content-type': 'application/json',
      'x-xsrf-token': getCookie('XSRF-TOKEN'),
    };
    const paramsJ = JSON.parse(requestParam);
    if (requestUrl.indexOf('${param}') >= 0) {
      requestUrl = requestUrl.replace('${param}', JSON.stringify(pureParams));
    } else if (requestParam.indexOf('${param}') >= 0) {
      replaceElement(
        paramsJ,
        '${param}',
        updateModal.toString
          ? `${JSON.stringify(pureParams).replaceAll('  ', '')}`
          : JSON.stringify(pureParams)
      );
    } else {
      error('no way to upload params');
      return;
    }

    let paramsStr = JSON.stringify(paramsJ);
    fetch(
      requestUrl,
      Object.assign(
        {
          method: updateModal.type,
          headers: headers,
          mode: 'cors',
          credentials: 'include',
        },
        updateModal.type == 'post' ? { body: paramsStr } : {}
      )
    )
      .then(response => response.json())
      .then(json => {
        let path = json;
        if (updateModal.urlPath) {
          const router = updateModal.urlPath.split('/');
          router.forEach(r => {
            r && (path = path[r]);
          });
        }
        if (typeof path !== 'string') {
          error('download path path error');
          return;
        }
        downloadModal.paramUrl = path;
        openUrl(action, origin, userId, downloadModal);
        success();
      })
      .catch(e => {
        // console.log(e);
        error('upload params failed');
      });

    // let http = new XMLHttpRequest();
    // let paramsStr = JSON.stringify(paramsJ);
    // http.open(updateModal.type, requestUrl, true);
    // for (const x in headers) {
    //   http.setRequestHeader(x, headers[x]);
    // }
    // http.onreadystatechange = function () {
    //   if (http.readyState === 4) {
    //     if (http.status === 200) {
    //       let path = http.responseText;
    //       if (updateModal.urlPath) {
    //         path = JSON.parse(path);
    //         const router = updateModal.urlPath.split('/');
    //         router.forEach((r) => {
    //           r && (path = path[r]);
    //         });
    //       }
    //       if (typeof path !== 'string') {
    //         error('download path path error');
    //         return;
    //       }
    //       downloadModal.paramUrl = path;
    //       window.open(
    //         `m5://?action=${action}&origin=${origin}&paramsDownload=${JSON.stringify(
    //           downloadModal,
    //         ).replaceAll('  ', '')}`,
    //       );
    //     } else {
    //       error('upload params failed');
    //     }
    //   }
    // };
    // if (updateModal.type === 'post') http.send(paramsStr);
    // else http.send();
  } else {
    openUrl(action, origin, userId, pureParams);
    success();
  }
}

const isM5 = () => navigator.userAgent.indexOf('m5shell') >= 0;
const isWin = () => navigator.userAgent.indexOf('m5shell/pc/win') >= 0;
const isLinux = () => navigator.userAgent.indexOf('m5shell/pc/linux') >= 0;
const isMac = () => navigator.userAgent.indexOf('m5shell/pc/mac') >= 0;
const isM5Pc = () => navigator.userAgent.indexOf('m5shell/pc') >= 0;
const isM5Mobile = () => isM5() && !isM5Pc();
const isAndroid = () => navigator.userAgent.indexOf('m5shell/android') >= 0;
const isIos = () => navigator.userAgent.indexOf('m5shell/ios') >= 0;
const nativeSdk = {
  canUse: str => {
    const methods = str.split('.');
    let method = nativeSdk;
    methods.forEach(s => {
      method = method[s];
    });
    if (!method || method === nativeSdk) {
      return false;
    }
    const declearStr = method.toString();

    if (declearStr.indexOf('exec(') < 0 && declearStr.indexOf('syncExec(') < 0) {
      return true;
    }

    let pluginIndex = declearStr.indexOf('plugin:') + 7;
    let pluginLeft = declearStr.substring(pluginIndex, declearStr.length);
    const pluginFirstDot = pluginLeft.indexOf(',');
    const plugin = pluginLeft
      .substring(0, pluginFirstDot)
      .replaceAll("'", '')
      .replaceAll('"', '')
      .replaceAll(' ', '');

    let actionIndex = declearStr.indexOf('action:') + 7;
    let actionLeft = declearStr.substring(actionIndex, declearStr.length);
    const actionFirstDot = actionLeft.indexOf(',');
    const action = actionLeft
      .substring(0, actionFirstDot)
      .replaceAll("'", '')
      .replaceAll('"', '')
      .replaceAll(' ', '');

    const ret = syncExec({
      plugin: 'CoreBridge',
      action: 'canUse',
      params: {
        plugin,
        action,
        sync: declearStr.indexOf('syncExec(') > 0,
      },
    });
    return ret === true || ret === 'true';
  },
  shellEvent,
  device: {
    isM5,
    isWin,
    isLinux,
    isMac,
    isM5Pc,
    isM5Mobile,
    isAndroid,
    isIos,
    /**
     * @method getSystemInfo
     * @description 获取系统信息
     * @callback
     * uuid 设备唯一标识（可能会变）
     * version  系统版本
     * platform  平台类型
     * model 手机品牌
     * manufacturer 手机型号
     * isVirtual 是否是模拟器
     * statusBarHeight:状态栏高度
     * titleBarHeight:原生标题栏高度
     * safeAreaBottomHeight:底部安全高度
     * openFrom:页面开启来源
     * language：当前语言
     */
    getSystemInfo: (params, success, error) =>
      exec({ plugin: 'DevicePlugin', action: 'getDeviceInfo', params, success, error }),

    //getSystemInfo的同步方法
    getSystemInfoSync: () => syncExec({ plugin: 'DevicePlugin', action: 'getDeviceInfo' }),

    /**
     * @method setClipboard
     * @description 设置信息到剪贴板
     * @params
     * value 要设置到剪贴板的内容
     */
    setClipboard: (params, success, error) =>
      exec({ plugin: 'ClipboardPlugin', action: 'setClipboard', params, success, error }),

    /**
     * @method getClipboard
     * @description 获取剪贴板内容
     */
    getClipboard: (params, success, error) =>
      exec({ plugin: 'ClipboardPlugin', action: 'getClipboard', params, success, error }),

    /**
     * @method getNetworkType
     * @description 获取网路信息
     * @callback
     * networkType : 网络类型，可能值：wifi cellular none
     * serverStatus ：与服务器连接状态：可能值connect disconnect
     */
    getNetworkType: (params, success, error) =>
      exec({ plugin: 'NetworkPlugin', action: 'getNetworkStatusInfo', params, success, error }),

    /**
     * @method getWifiInfo
     * @description 获取wifi信息
     * @params
     * mode 类型，//'1'单次回调，仅回调一次（默认值）'2' 持续回调，当wifi变更时会再次回调
     * @callback
     * ssid  wifi的ssid
     * mac  wifi的mac地址
     */
    getWifiInfo: (params, success, error) =>
      exec({ plugin: 'NetworkPlugin', action: 'getWifiInfo', params, success, error }),

    /**
     * @method vibrate
     * @description 调用设备震动
     * @params
     * time  震动持续时长，单位毫秒，默认1000
     * strength 震动强度1-255，默认系统默认强度
     */
    vibrate: (params, success, error) =>
      exec({ plugin: 'DevicePlugin', action: 'vibrate', params, success, error }),

    /**
     * @method message
     * @description 发短信
     * @param {
     * phonenumber 要发送的手机号
     * message 要发送的信息
     * } params
     */
    message: (params, success, error) =>
      exec({ plugin: 'MessagePlugin', action: 'sendMessage', params, success, error }),

    /**
     * @method call
     * @description 打电话
     * @param {
     * phonenumber 要拨打的手机号
     * } params
     */
    call: (params, success, error) =>
      exec({ plugin: 'PhonePlugin', action: 'callPhone', params, success, error }),

    /**
     * @method email
     * @description 发邮件
     * @param {
     * subject 邮件标题
     * bodystr 邮件内容
     * receiver 接受人，如**********
     * } params
     */
    email: (params, success, error) => {
      if (isM5Mobile()) {
        return exec({ plugin: 'EmailPlugin', action: 'sendEmail', params, success, error });
      } else {
        const { receiver, bodystr, subject } = params;
        if (!receiver) {
          return;
        }
        const link = `mailto:${receiver}?subject=${subject}&body=${bodystr}`;
        const a = document.createElement('a');
        a.href = encodeURI(link);
        a.target = 'hidden-iframe';
        const iframe = document.createElement('iframe');
        iframe.name = 'hidden-iframe';
        iframe.style = 'visibility:hidden;position:absolute;width:0px;height:0px';
        a.click();
        success && success();
      }
    },

    /**
     * @method savePhoneNumber
     * @description 保存手机号等信息到手机通讯录
     * @param {
     * name   姓名
     * mobilePhone 手机号
     * email 邮箱
     * imageData 头像
     * officePhone 工作电话
     * } params
     */
    savePhoneNumber: (params, success, error) =>
      exec({ plugin: 'PhonePlugin', action: 'savePhoneNumber', params, success, error }),
  },
  //媒体
  media: {
    /**
     * @method takePicture
     * @params {
     * crop 是否要对结果图片进行裁剪
     * }
     * @description 拍照
     * @callback {
     * path:String
     * filename:String
     * size:numner
     * mimeType:string
     * } param
     */
    takePicture: (params, success, error) =>
      exec({ plugin: 'MediaPlugin', action: 'takePicture', params, success, error }),

    /**
     *
     * @method chooseFile
     * @description 选择文件
     * @param {
     * maxSelectNum 最大选择张数，默认1
     * maxSize 每个文件最大大小限制，单位MB，默认无限制
     * } params
     * @param {*} success
     * @param {*} error
     * @returns
     */
    chooseFile: (params, success, error) =>
      exec({ plugin: 'MediaPlugin', action: 'selectFile', params, success, error }),

    /**
     * @method chooseMedia
     * @description 选择图片/视频
     * @param {
     * maxSelectNum 最大选择张数，默认1
     * type 可选择的文件类型，可选值：'image','video','both' 默认'image'
     * maxSize 每个文件最大大小限制，单位MB，默认无限制
     * showCamera 是否显示拍照按钮，仅maxSelectNum为1时生效
     * crop 选择完后是否要对结果图片进行裁剪，crop为true时，将强制type为image，maxSelectNum为1
     * } param
     */
    chooseMedia: (params, success, error) =>
      exec({ plugin: 'MediaPlugin', action: 'selectMedia', params, success, error }),

    /**
     * @method saveImageToGallery
     * @description 保存图片到系统相册
     * @param {
     * path:String
     * } param
     */
    saveImageToGallery: (params, success, error) =>
      exec({ plugin: 'MediaPlugin', action: 'saveImageToGallery', params, success, error }),

    /**
     * @method saveVideoToGallery
     * @description 保存图片到系统相册
     * @param {
     * path:String
     * } param
     */
    saveVideoToGallery: (params, success, error) =>
      exec({ plugin: 'MediaPlugin', action: 'saveVideoToGallery', params, success, error }),
    /**
     * 图片预览组件，支持图片/gif/视频
     * @param {
     * items:[
     * {
     * path:string  //图片路径，支持file://,http://,https://协议，以及A9文件storageKey
     * originPath?:string  //原图路径，支持file://,http://,https://协议，以及A9文件storageKey
     * filename:string//文件名
     * }
     * ],
     * showIndex?:number //初始显示的图片位置，默认0
     * autoShowOrigin?:boolean //有原图时自动展示原图，默认false
     *
     * } params
     * @param {*} success
     * @param {*} error
     * @returns
     */
    viewPictures: (params, success, error) =>
      exec({ plugin: 'MediaPlugin', action: 'viewPictures', params, success, error }),

    audio: {
      /**
       * @method startRecord
       * @description 开始录音
       */
      startRecord: (params, success, error) =>
        exec({ plugin: 'AudioPlugin', action: 'startRecord', params, success, error }),
      /**
       * @method stopRecord
       * @description 停止录音
       * @callback
       *  filePath 录音文件本地地址
       *  fileSize 录音文件大小，单位byte
       *  type 录音文件类型
       */
      stopRecord: (params, success, error) =>
        exec({ plugin: 'AudioPlugin', action: 'stopRecord', params, success, error }),
      /**
       * @method playAudio
       * @description 播放音频文件
       * @param {
       * path 音频文件路径，diaplay为false时，仅支持本地文件
       * display 是否显示音频播放界面，默认false，有界面时，无法通过接口操纵播放
       * }
       * @callback
       * duration 当前音频文件的总长度，milliseconds类型
       */
      playAudio: (params, success, error) =>
        exec({ plugin: 'AudioPlugin', action: 'playAudio', params, success, error }),
      /**
       * @method stopAudio
       * @description 停止播放当前音频文件
       */
      stopAudio: (params, success, error) =>
        exec({ plugin: 'AudioPlugin', action: 'stopAudio', params, success, error }),
      /**
       * @method pauseAudio
       * @description 暂停播放当前音频文件
       */
      pauseAudio: (params, success, error) =>
        exec({ plugin: 'AudioPlugin', action: 'pauseAudio', params, success, error }),
      /**
       * @method resumeAudio
       * @description 恢复播放当前音频文件
       */
      resumeAudio: (params, success, error) =>
        exec({ plugin: 'AudioPlugin', action: 'resumeAudio', params, success, error }),
      /**
       * @method seekTo
       * @description 设置当前音频文件从指定位置开始播放
       * @param {
       * seek 位置 milliseconds类型
       * }
       */
      seekTo: (params, success, error) =>
        exec({ plugin: 'AudioPlugin', action: 'seekTo', params, success, error }),
    },
    video: {
      /**
       * @method startRecord
       * @description 录制视频文件
       * @param {
       * maxRecordSeconds 视频文件最大长度限制，默认15秒
       * }
       * @callback
       * path 录制的视频文件的本地路径
       */
      startRecord: (params, success, error) =>
        exec({ plugin: 'VideoPlugin', action: 'record', params, success, error }),
      /**
       * @method playVideo
       * @description 播放视频文件
       * @param {
       * path 视频文件路径
       * }
       * @callback
       */
      playVideo: (params, success, error) =>
        exec({ plugin: 'VideoPlugin', action: 'play', params, success, error }),
    },
  },

  scan: {
    /**
     * @method encode
     * @description 生成二维码
     * @param {
     * data:String 要生成二维码的数据
     * }
     * @callback
     * image:string //base64编码后的二维码图片数据
     */
    encode: (params, success, error) =>
      exec({ plugin: 'ScanPlugin', action: 'encode', params, success, error }),
    /**
     * @method scan
     * @description 扫码，得到结果后关闭扫码界面
     * @param {
     * nativeHandle 如果原生壳可以处理扫描结果，扫描结果交由原生壳处理，默认true
     * }
     * @callback
     * text  扫码结果
     * format 结果的编码类型
     */
    scan: (params, success, error) =>
      exec({ plugin: 'ScanPlugin', action: 'scan', params, success, error }),
  },
  attachment: {
    /**
     * @method readAttachment
     * @description 查看附件
     * @param {
     * url:String //文件路径，支持http或file协议
     * filename:String //文件名
     * headers:{} //请求头
     * lastModified:String //文件最后修改日期，将用于判断是否需要下载
     * isSaveToLocal:Boolean //是否保存记录到数据库，默认true
     * wpsParams:{//使用wps打开的参数，如果不传将不使用wps打开
     *  android:{//android端的参数
     *   androidWpsKey://String //WPS 的key信息
     *   isReviseMode:Boolean //是否编辑模式，默认false
     *   isClearTrace:Boolean //是否清除痕迹，默认false
     *   isShowReviewingPaneRightDefault:Boolean //是否显示编辑面板，默认false
     *   isScreenshotForbid:Boolean //是否可截屏，默认false
     *   isReadOnly:Boolean //是否强制只读，默认false
     *   userName:String //编辑用户的名字，不传默认取当前用户名
     *   waterMark:String //水印文字,传了将使用水印
     *  },
     *  ios:{//ios端的参数
     *  }
     * }
     * }
     */
    readAttachment: (params, success, error) =>
      exec({ plugin: 'AttachmentPlugin', action: 'readAttachment', params, success, error }),
  },
  map: {
    /**
     * @method location
     * @description 定位(仅经纬度)
     * @param {
     * mode 模式,'1'单次定位,'2'连续定位
     * }
     * @callback
     * id:当前定位的id
     * latitude:纬度
     * longitude:经度
     * }
     */
    location: (params, success, error) =>
      exec({ plugin: 'AMapLocationPlugin', action: 'getLocation', params, success, error }),
    /**
     * @method locationInfo
     * @description 定位（详细信息）
     * @param {
     * mode 模式,'1'单次定位,'2'连续定位
     * }
     * @callback
     * id:当前定位的id
     * latitude:纬度
     * longitude:经度
     * lbsAddr:详细地址
     * lbsCountry：国家
     * lbsProvince：省
     * lbsCity：城市
     * lbsTown：镇
     * lbsStreet：街道
     * provider：定位信息提供源，gaode google
     * }
     */
    locationInfo: (params, success, error) =>
      exec({ plugin: 'AMapLocationPlugin', action: 'getLocationInfo', params, success, error }),

    /**
     * @method stopLocation
     * @description 停止定位
     * @param {
     * id 定位id
     * }
     * @callback
     * latitude:纬度
     * longitude:经度
     * lbsAddr:详细地址
     * lbsCountry：国家
     * lbsProvince：省
     * lbsCity：城市
     * lbsTown：镇
     * lbsStreet：街道
     * provider：定位信息提供源，gaode google
     * }
     */
    stopLocation: (params, success, error) =>
      exec({ plugin: 'AMapLocationPlugin', action: 'stopLocation', params, success, error }),
    /**
     * @method capturelocation
     * @description 拍照定位
     * @param {
     * userName 用户名，用于界面显示，默认当前用户
     * location 位置信息，如果有值将不再重新定位
     * uploadPicUrl 生成的照片上传地址
     * serverDateUrl 同步服务器日志信息接口，如果不传将使用手机本地时间
     * }
     * @callback
     * localSource:生成的照片的本地地址
     * createDate:照片的生成时间
     * filename:照片名
     * }
     */
    captureLocation: (params, success, error) =>
      exec({ plugin: 'CaptureLocationPlugin', action: 'takePicture', params, success, error }),
  },
  chat: {
    /**
     * @method start
     * @description 开启单人聊天
     * @param {
     * userId:要开启聊天的对象的用户id
     * userName 对象的名字，非必传，如果不传将尝试根据userId获取
     * } params
     */
    start: (params, success, error) => {
      if (isM5()) {
        //在壳中
        return exec({ plugin: 'ChatPlugin', action: 'startChat', params, success, error });
      } else {
        openFromBrowser('ChatPlugin.startChat', params, success, error);
      }
    },
    /**
     * @method startGroup
     * @description 开启群聊
     * @param {
     * groupId:要开启聊天的对象的用户id
     * groupName 对象的名字，非必传，如果不传将尝试根据groupId获取
     * } params
     */
    startGroup: (params, success, error) =>
      exec({ plugin: 'ChatPlugin', action: 'startGroupChat', params, success, error }),
    /**
     * @method showChooseToStartChat
     * @description 打开选人界面创建聊天
     */
    showChooseToStartChat: (params, success, error) => {
      if (typeof params === 'function') {
        return exec({
          plugin: 'ChatPlugin',
          action: 'showChooseToStartChat',
          params: {},
          success: params,
          error: success,
        });
      } else {
        return exec({
          plugin: 'ChatPlugin',
          action: 'showChooseToStartChat',
          params: {},
          success,
          error,
        });
      }
    },
    /**
     * @method showGroupChatList
     * @description 打开群聊列表界面
     */
    showGroupChatList: (params, success, error) => {
      if (typeof params === 'function') {
        return exec({
          plugin: 'ChatPlugin',
          action: 'openGroupChatList',
          params: {},
          success: params,
          error: success,
        });
      } else {
        return exec({
          plugin: 'ChatPlugin',
          action: 'openGroupChatList',
          params: {},
          success,
          error,
        });
      }
    },
    /**
     *
     * @method showGroupChatList
     * @description 打开会话列表界面
     * @param {*} success
     * @param {*} error
     * @returns
     */
    showConversationList: (params, success, error) => {
      if (typeof params === 'function') {
        return exec({
          plugin: 'ChatPlugin',
          action: 'loadMessageApp',
          params: {},
          success: params,
          error: success,
        });
      } else {
        return exec({ plugin: 'ChatPlugin', action: 'loadMessageApp', params: {}, success, error });
      }
    },

    /**
     *
     * @param {
     * type "conversation" | 'message'//搜索类型
     * keyword 关键字
     * pageSize 分页大小
     * pageIndex 分页索引
     * } params
     * @param {*} success
     * @param {*} error
     * @returns
     */
    search: (params, success, error) =>
      exec({ plugin: 'ChatPlugin', action: 'search', params, success, error }),

    /**
     *
     * @param {
     * message:完整消息体
     * } params
     * @param {*} success
     * @param {*} error
     * @returns
     */
    jumpToMessage: (params, success, error) =>
      exec({ plugin: 'ChatPlugin', action: 'jumpToMessage', params, success, error }),
  },
  lock: {
    /**
     * @method gestureState
     * @description 获取手势密码状态，总开关open:true/false，显示轨迹gesturePath:false/true ,登录login:false/true
     */
    getGestureStates: (params, success, error) =>
      exec({ plugin: 'GestureLockPlugin', action: 'getGestureStates', params, success, error }),
    /**
     * @method gestureState
     * @description 获取手势密码状态，登录login:false/true
     */
    setGestureStates: (params, success, error) =>
      exec({ plugin: 'GestureLockPlugin', action: 'setGestureStates', params, success, error }),
    /**
     * @method setGestureLock
     * @description 设置手势密码
     */
    setGestureLock: (params, success, error) =>
      exec({ plugin: 'GestureLockPlugin', action: 'setGestureLock', params, success, error }),

    /**
     * @method verifyGestureLock
     * @description 验证手势密码
     * @param{ //手势密码验证错误后的弹窗内容，如果不需要弹窗，传null
     *  errorTitle:string  //手势验证错误后的弹窗标题
     *  errorContent:string //手势验证错误后的弹窗提示内容
     *  errorBtn:string //确定按钮的文字
     * }
     */
    verifyGestureLock: (params, success, error) =>
      exec({ plugin: 'GestureLockPlugin', action: 'verifyGestureLock', params, success, error }),

    /**
     * @method verifyGestureLock
     * @description 指纹、人脸识别是否支持 touchId：false,faceId：false
     */
    supportTypes: (params, success, error) =>
      exec({ plugin: 'LocalAuthenticationPlugin', action: 'supportTypes', params, success, error }),
    /**
     * @method getLocalAuthenticationStates
     * @description 获取人脸/指纹，总开关open:true/false，登录login:false/true ,
     */
    getLocalAuthenticationStates: (params, success, error) =>
      exec({
        plugin: 'LocalAuthenticationPlugin',
        action: 'getLocalAuthenticationStates',
        params,
        success,
        error,
      }),
    setLocalAuthenticationStates: (params, success, error) =>
      exec({
        plugin: 'LocalAuthenticationPlugin',
        action: 'setLocalAuthenticationStates',
        params,
        success,
        error,
      }),
    /**
     * @method verifyLocalLock
     * @description 验证人脸/指纹
     */
    verifyLocalAuthenticationLock: (params, success, error) =>
      exec({
        plugin: 'LocalAuthenticationPlugin',
        action: 'verifyLocalLock',
        params,
        success,
        error,
      }),
    /**
     * @method getEnrolledState
     * @description 验证人脸/指纹 是否设置
     * @callback {isEnrolled:true/false}
     */
    getEnrolledState: (params, success, error) =>
      exec({
        plugin: 'LocalAuthenticationPlugin',
        action: 'getEnrolledState',
        params,
        success,
        error,
      }),
  },
  routor: {
    /**
     *获取离线应用包原本的在线根路径
     */
    getRouterPath: (params, success, error) => {
      if (typeof params === 'function') {
        return exec({
          plugin: 'NavigationPlugin',
          action: 'getRouterPath',
          params: {},
          success: params,
          error: success,
        });
      } else {
        return exec({
          plugin: 'NavigationPlugin',
          action: 'getRouterPath',
          params: {},
          success,
          error,
        });
      }
    },
    /**
     * @method push
     * @description 打开web页面
     * @param {
     * url  跳转路径
     * param 跳转携带的参数
     * options:{ 移动端
     *      openWebView  是否新开webview，默认true
     *      useNativeBanner 是否使用原生导航栏，默认true
     *      useNativeStatusBar 是否使用原生状态栏，默认true，如果为false，webview将在导航栏底下
     *      nativeStatusBarTextBlack 原生状态栏文字是否为黑色，默认true（如果适配了暗黑模式，在暗黑模式下此项意义为是否为白色）
     *      replaceTop 是否替换调当前webview栈顶页面，默认false，优先级高于openWebView
     *      showOrientationButton 是否强制显示标题栏右侧的横屏按钮
     *      pushInDetailPad 在pad上时，是否显示在内容区
     *      clearDetailPad 在pad上时，是否清空内容区
     *      appInfo:{
     *         appId:'xxx',应用id
     *         homePage:'xxx',应用首页
     *         description,应用描述
     *      }
     * }
     * pcOptions: { PC端ss
     *      openType: 'inTab' | 'inModal'
     * }
     * } params
     */
    push: (params, success, error) =>
      exec({ plugin: 'NavigationPlugin', action: 'pushPage', params, success, error }),
    /**
     * @method pop
     * @description 关闭web页面
     * @param {
     * backIndex  需要关闭的页面层数，默认1
     * param 关闭跳转携带的参数
     * options:{
     *      clearDetailPad 在pad上时，是否清空内容区
     * }} params
     */
    pop: (params, success, error) =>
      exec({ plugin: 'NavigationPlugin', action: 'popPage', params, success, error }),
    /**
     * @method getParams
     * @description 获取页面跳转参数
     */
    getParams: (params, success, error) => {
      if (typeof params === 'function') {
        return exec({
          plugin: 'NavigationPlugin',
          action: 'getParams',
          params: {},
          success: params,
          error: success,
        });
      } else {
        return exec({
          plugin: 'NavigationPlugin',
          action: 'getParams',
          params: {},
          success,
          error,
        });
      }
    },
    /**
     * @method getBackparams
     * @description 获取页面返回参数
     */
    getBackParams: (params, success, error) => {
      if (typeof params === 'function') {
        return exec({
          plugin: 'NavigationPlugin',
          action: 'getBackParams',
          params: {},
          success: params,
          error: success,
        });
      } else {
        return exec({
          plugin: 'NavigationPlugin',
          action: 'getBackParams',
          params: {},
          success,
          error,
        });
      }
    },
    /**
     * @method overrideBack
     * @description 接管物理返回键
     * @params{
     * override:Boolean,//是否接管
     * }
     */
    overrideBack: (params, callback, error) => {
      if (params.override) {
        shellEvent.on('backbutton', () => {
          callback();
        });
      }
      exec({
        plugin: 'CoreBridge',
        action: 'overrideBack',
        params,
        error,
      });
    },
    /**
     * 关闭当前页面
     */
    close: () => {
      exec({
        plugin: 'CoreBridge',
        action: 'close',
        params: {},
        error: () => {},
      });
    },
    /**
     * @method setGestureBackState
     * @description 设置页面是否可滑动返回
     * @param {state 默认true}
     */
    setGestureBackState: (params, success, error) =>
      exec({ plugin: 'NavigationPlugin', action: 'setGestureBackState', params, success, error }),
    /**
     * @method clearDetailPad
     * @description 在pad上时清空内容区
     */
    clearDetailPad: (params, success, error) =>
      exec({ plugin: 'NavigationPlugin', action: 'clearDetailPad', params, success, error }),
    /**
     * @method isInDetailPad
     * @description 在pad上判断当前页面是否在内容区
     * 返回 0: false ,1: true, 2 :不是pad
     */
    isInDetailPad: (params, success, error) =>
      exec({ plugin: 'NavigationPlugin', action: 'isInDetailPad', params, success, error }),
    /**
     * @method isDetailEmpty
     * @description 在pad上判断当前内容区是否为空
     * 返回 0: false ,1: true, 2 :不是pad
     */
    isDetailEmpty: (params, success, error) =>
      exec({ plugin: 'NavigationPlugin', action: 'isDetailEmpty', params, success, error }),
  },
  titleBar: {
    /**
     * 标题栏设置接口，全部整合到一起
     * @param {
     * titleBar：{
     *    visible 是否显示原生标题栏
     *    dividerVisible是否显示原生标题栏下部分割线
     *    title
     *    float:bool  //标题栏透明且悬浮在页面上，float时背景色设置将不生效
     *    gravity标题位置，仅支持left，middle，默认middle
     *    textColor标题颜色，同时影响返回按钮等默认按钮的颜色
     *    backgroundColor
     * }
     * statusBar:{
     *    visible:是否显示
     *    color//状态栏颜色，如果不设置默认跟标题背景色相同
     *    textBlack//状态栏文字是否是黑色，如果不设置将根据statusBarColor自动处理
     * }
     * rightButtons:[
     * {
     *    id:按钮的id，不传默认使用callback回调作为id
     *    text:按钮文字
     *    textColor：按钮文字颜色
     *    textSize：按钮文字大小
     *    imageUrl：图片类型时的url地址
     *    callback:()=>void
     * }
     * ]
     *
     * } params
     * @param {*} callback
     * @param {*} error
     */
    setTitleBar: (params, success, error) => {
      if (params.rightButtons) {
        const btns = [];
        params.rightButtons.forEach(b => {
          const fixId = b.id || (b.callback && b.callback.toString());
          btns.push({
            id: fixId,
            callback: b.callback,
          });
          delete b.callback;
          b.id = fixId;
        });
        shellEvent.on('HeaderEventTrigger', function (id) {
          const btn = btns.filter(b => b.id === id);
          if (btn && btn[0] && btn[0].callback) {
            btn[0].callback();
          }
        });
      }
      exec({ plugin: 'NativeTitlePlugin', action: 'setTitleBar', params, success, error });
    },

    /**以下接口废弃，后续可能删除 */

    /**
     * @method setTitleDividerState
     * @description 设置原生标题栏下方的分割线是否显示
     * @param {
     * hide:是否隐藏，默认true
     * }
     */
    setTitleDividerState: (params, success, error) =>
      exec({ plugin: 'NativeTitlePlugin', action: 'setTitleDividerState', params, success, error }),
    /**
     * @method setCloseButtonState
     * @description 设置原生标题栏的关闭按钮是否显示
     * @param {
     * hide:是否隐藏，默认true
     * }
     */
    setCloseButtonState: (params, success, error) =>
      exec({ plugin: 'NativeTitlePlugin', action: 'setCloseButtonState', params, success, error }),

    /**
     * @method setBackButtonStyle
     * @description 设置原生标题栏的关闭按钮是否显示
     * @param {
     * style: 可选值 'back' 'close'，默认back
     * }
     */
    setBackButtonStyle: (params, success, error) =>
      exec({ plugin: 'NativeTitlePlugin', action: 'setBackButtonStyle', params, success, error }),

    /**
     * @method setTitle
     * @description 设置原生标题栏的标题
     * @param {
     * title: 标题
     * appName?: 应用标题（桌面端）
     * }
     */
    setTitle: (params, success, error) =>
      exec({ plugin: 'NativeTitlePlugin', action: 'setTitle', params, success, error }),

    /**
     * @method setTitleGravity
     * @description 设置原生标题栏的标题
     * @param  标题位置，仅支持left和middle
     */
    setTitleGravity: (param, success, error) =>
      exec({
        plugin: 'NativeTitlePlugin',
        action: 'setTitleGravity',
        params: { gravity: param },
        success,
        error,
      }),

    /**
     * @method setTitleBarState
     * @description 设置原生标题栏状态
     * @param {
     * state: 状态 //0隐藏1显示2隐藏但可以被手势拉出隐藏3显示但可以被拉出隐藏,2,3手势响应仅在横屏或pad上生效, 默认1
     * }
     */
    setTitleBarState: (params, success, error) =>
      exec({ plugin: 'NativeTitlePlugin', action: 'setTitleBarState', params, success, error }),

    /**
     * @method addLeftButton
     * @description 设置左侧按钮，将覆盖之前的设置值
     * @param {
     * buttons:[
     * {
     * id:按钮的id
     * backgroundColor：背景色
     * activeBackgroundColor：激活状态背景色
     * backgroundImage：背景图片
     * activeBackgroundImage：激活状态背景图片
     * text:按钮文字
     * activeText：激活状态文字
     * textColor：按钮文字颜色
     * activeTextColor：激活状态文字颜色
     * textSize：按钮文字大小
     * activeTextSize：激活状态文字大小
     * }
     * ]
     * }
     */
    addLeftButton: (params, callback, error) => {
      shellEvent.on('HeaderEventTrigger', function (id) {
        callback(id);
      });
      exec({
        plugin: 'NativeTitlePlugin',
        action: 'addLeftButton',
        params,
        error,
      });
    },

    /**
     * @method addRightButton
     * @description 设置右侧按钮，将覆盖之前的设置值
     * @param {
     * buttons:[
     * {
     * id:按钮的id
     * type:类型  可选值 text image
     * text:按钮文字
     * textColor：按钮文字颜色
     * textSize：按钮文字大小
     * imageUrl：图片类型时的url地址
     * }
     * ]
     * }
     */
    addRightButton: (params, callback, error) => {
      shellEvent.on('HeaderEventTrigger', function (id) {
        callback(id);
      });
      exec({
        plugin: 'NativeTitlePlugin',
        action: 'addRightButton',
        params,
        error,
      });
    },

    /**
     * @method activeLeftButton
     * @description 激活左侧按钮
     * @param {
     * id: 按钮id
     * }
     */
    activeLeftButton: (params, success, error) =>
      exec({ plugin: 'NativeTitlePlugin', action: 'activeLeftButton', params, success, error }),

    /**
     * @method setTitleBarDefaultStyle
     * @description 设置标题栏默认样式
     * @param {
     * backgroundColor: 背景色
     * textColor:文字色
     * }
     */
    setTitleBarDefaultStyle: (params, success, error) =>
      exec({
        plugin: 'NativeTitlePlugin',
        action: 'setTitleBarDefaultStyle',
        params,
        success,
        error,
      }),

    /**
     * 暂不对外
     * @method resetTitle
     * @description 重置标题栏
     */
    resetTitle: (params, success, error) =>
      exec({ plugin: 'NativeTitlePlugin', action: 'resetTitle', params, success, error }),

    /**
     * @method setStatusBarStatus
     * @description 设置状态栏
     * @param{
     *   visible: 是否使用原生状态栏  布尔值,true/false
     *   color: 原生状态栏颜色  rgb色，如#ffffff 仅visible为true生效
     *   textBlack:原生状态栏文字是否为黑色 布尔值,true/false ，如果传递了color值，且未传递本值，将根据color自动处理文字色
     * }
     */
    setStatusBarStatus: (params, success, error) =>
      exec({ plugin: 'NativeTitlePlugin', action: 'setStatusBarStatus', params, success, error }),
  },
  http: {
    /**
     * @method request
     * @description 网络请求
     * @param {
     * url:请求路径
     * method:请求类型，默认get
     * timeout:超时时间，毫秒，默认10000
     * parameter:请求参数(post时有效)
     * dataType：请求参数类型，默认application/json(post时有效)
     * headers:请求头，将与原生默认头合并
     * responseHeader:是否返回返回头，默认false(注意，返回header时与不返回时，数据多包了一层)
     * requestId:请求id
     * cacheAble:Bolean 是否允许缓存
     * }
     * @callback
     * 请求结果字符串
     */
    request: (params, success, error) =>
      exec({ plugin: 'RequestPlugin', action: 'request', params, success, error }),
    /**
     * @method cancel
     * @description 取消请求
     * @param {
     * requestIds:请求id
     * }
     * @callback
     */
    cancel: (params, success, error) =>
      exec({ plugin: 'RequestPlugin', action: 'cancel', params, success, error }),
    /**
     * @method download
     * @description 下载文件
     * @param {
     * filename:文件保存的名字
     * url:文件下载路径
     * headers:请求头信息，将与原生默认头合并
     * lastModified:文件最后修改日期，将用于判断是否需要下载
     * isSaveToLocal：是否保存记录到数据库，默认true
     * requestId 下载请求id
     * }
     * @callback
     * pos 下载进度
     * target  下载文件本地路径
     * requestId 下载请求id
     */
    download: (params, success, error) =>
      exec({ plugin: 'DownLoadPlugin', action: 'download', params, success, error }),

    /**
     * @method upload
     * @description 上传文件
     * @param {
     * url:上传地址
     * headers:请求头信息，将与原生默认头合并
     * fileList:[
     * {
     * filepath:文件路径
     * base64:如果是base64编码的图片，将以base64为准
     * requestId:请求id
     * }
     * ]
     * }
     * @callback
     * pos 上传进度
     * response  上传结果
     * requestId 上传请求id
     */
    upload: (params, success, error) =>
      exec({ plugin: 'UploadPlugin', action: 'upload', params, success, error }),
  },
  soket: {
    /**
     * 监听webSocket消息
     * @param {*} messageType 消息类型，由于实现的原因，请自行保证messageType唯一
     * @param {*} callback 消息回调
     * @param {*} error 监听失败的回调
     */
    on: (params, callback, error) => {
      exec({
        plugin: 'SocketPlugin',
        action: 'onWebSocketEvent',
        params,
        success: ({ id, data }) => {
          if (id) {
            callback && callback(id);
          } else if (data) {
            callback && callback(data);
          }
        },
        error,
      });
    },
    /**
     * 取消监听webSocket消息
     * @param {
     *  messageType 消息类型，参见M5消息类型
     *  uuId 监听的uuid，在on方法的success回调返回
     * }
     * @param {*} success 取消监听成功的回调
     * @param {*} error 取消监听失败的回调
     */
    clear: (params, success, error) => {
      exec({
        plugin: 'SocketPlugin',
        action: 'clearWebSocketEvent',
        params,
        success,
        error,
      });
    },
  },
  printer: {
    /**
     * @method isCanPrint
     * @description 判断设备是否支持打印
     * @callback
     * canPrint 是否支持
     */
    isCanPrint: (params, success, error) =>
      exec({ plugin: 'PrintPlugin', action: 'isCanPrint', params, success, error }),

    /**
     * @method print
     * @description 调起系统打印服务
     * @param {
     * path:要打印的文件路径
     * fileType:要打印的文件类型
     * maxFileSize:文件大小限制,单位byte，默认15*1024*1024
     * }
     */
    print: (params, success, error) =>
      exec({ plugin: 'PrintPlugin', action: 'print', params, success, error }),
  },
  data: {
    /**
     * @method get
     * @description 获取数据
     * @param {
     * key:存储的key
     * withServer:数据是否跟当前服务器绑定，默认true
     * withUser:数据是否跟当前用户绑定，默认true
     * }
     */
    get: (params, success, error) =>
      exec({ plugin: 'DataPlugin', action: 'get', params, success, error }),

    /**
     * @method  set
     * @description 存储数据
     * @param {
     * key:存储的key
     * value:存储的值
     * withServer:数据是否跟当前服务器绑定，默认true
     * withUser:数据是否跟当前用户绑定，默认true
     * }
     */
    set: (params, success, error) =>
      exec({ plugin: 'DataPlugin', action: 'set', params, success, error }),
  },
  screenDisplay: {
    /**
     * @method open
     * @description 调起无线投屏
     */
    open: (params, success, error) =>
      exec({ plugin: 'ScreenDisplayPlugin', action: 'open', params, success, error }),
  },
  /**
   * 用户相关插件
   */
  user: {
    /**
    *获取当前登录用户的一些信息
    @returns {i18nextLng: 'zh-CN', isAdmin: 'false', login-device: 'mobile', memberId:'1234', name:'张三'...}
    */
    getLoginUserInfo: (params, success, error) => {
      if (typeof params === 'function')
        //暂时兼容以前的调用，后期会移除，以前的参数列表为success,error
        return exec({
          plugin: 'UserPlugin',
          action: 'getLoginUserInfo',
          params: {},
          success: params,
          error: success,
        });
      else
        return exec({
          plugin: 'UserPlugin',
          action: 'getLoginUserInfo',
          params: {},
          success,
          error,
        });
    },
    /**
    *获取当前登录环境的一些信息 异步
    @returns {/portal/config接口全量信息}
    */
    getLoginSystemConfig: (params, success, error) => {
      if (typeof params === 'function') {
        return exec({
          plugin: 'UserPlugin',
          action: 'getLoginSystemConfig',
          params: {},
          success: params,
          error: success,
        });
      } else {
        return exec({
          plugin: 'UserPlugin',
          action: 'getLoginSystemConfig',
          params: {},
          success,
          error,
        });
      }
    },
    /**
    *获取当前登录环境的一些信息 同步
    @returns {/portal/config接口全量信息}
    */
    //getLoginUserInfo的同步方法
    getLoginUserInfoSync: () => syncExec({ plugin: 'UserPlugin', action: 'getLoginUserInfo' }),

    //getLoginSystemConfig的同步方法
    getLoginSystemConfigSync: () =>
      syncExec({ plugin: 'UserPlugin', action: 'getLoginSystemConfig' }),

    // 退出登录
    logout: (params, success, error) =>
      exec({ plugin: 'UserPlugin', action: 'clearLoginResult', params, success, error }),
    // 获取m5版本 ：{value:"1.0.0"，build:"xxx"}
    getVersion: (params, success, error) =>
      exec({ plugin: 'UserPlugin', action: 'getVersion', params, success, error }),
    // 清除缓存
    clearCache: (params, success, error) =>
      exec({ plugin: 'UserPlugin', action: 'clearCache', params, success, error }),
    /**
     * 获取缓存大小
     * @returns {size:12M}
     */
    getCacheSize: (params, success, error) =>
      exec({ plugin: 'UserPlugin', action: 'getCacheSize', params, success, error }),
    /**
     * 设置自动清理缓存的时间间隔
     * @params {timeInterval:15} 时间单位天
     */
    setClearCacheTimeInterval: (params, success, error) =>
      exec({ plugin: 'UserPlugin', action: 'setClearCacheTimeInterval', params, success, error }),
    /**
     * 获取自动清理缓存的时间间隔
     * @returns {timeInterval:15} 时间单位天
     */
    getClearCacheTimeInterval: (params, success, error) =>
      exec({ plugin: 'UserPlugin', action: 'getClearCacheTimeInterval', params, success, error }),
    /**
     *
     * @params {password:xxx}
     * @returns   {value:true/false}
     */
    checkPassword: (params, success, error) =>
      exec({ plugin: 'UserPlugin', action: 'checkPassword', params, success, error }),
    //隐私协议
    showPrivacyProtection: (params, success, error) =>
      exec({ plugin: 'UserPlugin', action: 'showPrivacyProtection', params, success, error }),
    /**
     *获取当前人员的底导航门户配置
     * @params {}，传空
     * @returns {
     * portalId:xxxx,//门户Id
     * allowEdit：bool//是否可以编辑
     * }
     */
    getTabbarConfig: (params, success, error) =>
      exec({ plugin: 'UserPlugin', action: 'getTabbarConfig', params, success, error }),
    /**
     * 沙箱登录
     * @param {*} params
     * @param {*} success
     * @param {*} error
     * @returns
     */
    sandBoxLogin: (params, success, error) =>
      exec({ plugin: 'UserPlugin', action: 'sandboxLogin', params }),
  },
  /**
   * 切换组织，切换集团及集团下子单位
   */
  switchOrg: {
    /**
     * @method getCompanyInfo
     * @description 获取单位信息
     *
     * @returns {
     *   switchEnable:boolean,能否切换单位
     *   orgName:单位名称
     *   orgId:单位id
     * }
     */
    getSwitchOrgInfo: (params, success, error) =>
      exec({ plugin: 'SwitchOrgPlugin', action: 'getSwitchOrgInfo', params, success, error }),
    //打开切换组织界面
    openOrgSwitcher: (params, success, error) =>
      exec({ plugin: 'SwitchOrgPlugin', action: 'openOrgSwitcher', params, success, error }),
  },
  /**
   * 该组件强依赖A9业务，不对外
   */
  share: {
    /**
     * @method showMenu
     * @description 调起分享组件
     * @param {
     * appId:调起分享的应用id，用于权限判断
     * shareBtnList:[
     * {
     * key:应用key，预制应用仅传key即可，目前预制im/wechat/qq/other
     * title:图标名，非必填
     * icon:图标url，非必填
     * }
     * ]扩展按钮列表
     * }
     * @callback
     * key:点击的应用key
     */
    showMenu: (params, success, error) =>
      exec({ plugin: 'SharePlugin', action: 'share', params, success, error }),
    /**
     * @method toWxMiniProgram
     * @description 分享到微信小程序
     * @param {
     * miniProgramType = 0 // 正式版:0，测试版:1，体验版:2
     * userName // 小程序原始id
     * path //小程序页面路径
     * title // 小程序消息title
     * description // 小程序消息desc
     * thumbImage //小程序封面图小图
     * hdThumbImage //小程序封面图大图
     * appId //小程序所属的应用id
     * }
     */
    toWxMiniProgram: (params, success, error) =>
      exec({ plugin: 'SharePlugin', action: 'shareWxMiniProgram', params, success, error }),
    /**
     * @method shareToIm
     * @description 分享到IM
     * @param {
     * type 类型，目前仅支持file，后续可能扩展message
     * paramsUpload?: {//参数上传信息，如果参数超长（大于1000字节），M5可能不响应，需要把参数存储到服务端，然后在M5内部再去取
     *    uploadRequest?: {
     *      type: 'post' | 'get'//上传请求类型，默认post
     *      headers?: any,//请求头
     *      urlModal: string//上传url模版,某些情况下参数可能字节拼到url中，请使用${param}占位，如http://xxx.xxx.com/upload/分享参数，请拼为http://xxx.xxx.com/upload/${param}，将会把参数转为json字符串后替换
     *      paramModal?: string //参数模版,如请求参数为{params:分享参数}，请将此字段拼接成{params:${param}}
     *      toString?: boolean //拼接参数模版时是否要将分享参数转成json字符串
     *      urlPath?: string//上传后，返回值中取下载地址的路径，如{content:{url:"下载路径"}},该urlPath值请拼成content/url以/分隔路径
     *    },
     *    downloadRequest?: {
     *      type: 'post' | 'get'//下载请求类型，默认get
     *      headers?: any,//请求头
     *      urlModal?: string//下载url模版，某些情况下，返回值可能不是完整的路径，需要一个模版来生成完整路径，如http://xxx.xxx.com/download/下载路径,请将urlModal拼成http://xxx.xxx.com/download/${path},下载参数时将会替换${path}成真实路径
     *      paramModal?: string //参数模版,某些情况下，下载路径可能可能不是拼接在url中，而是作为参数值来调用接口，如请求参数为{params:下载路径}，请将此字段拼接成{params:${path}}
     *      paramPath?: string//请求接口返回值中，参数的路径，如{content:{param:分享参数}},该urlPath值请拼成content/param以/分隔路径
     *    }
     *  }
     * params:{
     *   //type为file时
     *    filePaths:[
     *        "",""
     *    ]
     *   //type为message时
     *    messageType://消息类型，参见M5消息类型
     *   message:{//构建好的消息体数据，参见M5消息卡片
     *   }
     * }
     */
    toIm: (params, success, error) => {
      if (isM5()) {
        //在壳中
        return exec({ plugin: 'SharePlugin', action: 'shareToIm', params, success, error });
      } else {
        openFromBrowser('SharePlugin.shareToIm', params, success, error);
      }
    },
    /**
     * @method shareFile
     * @description 分享文件到...
     * @param {
     * shareType://file  或 link
     * targetType :目标名，qq,wechat,im(致信),other(其他，除qq，wechat外的其他应用)
     * paths:[
     *      "",""
     * ]
     * }
     */
    toOther: (params, success, error) =>
      exec({ plugin: 'SharePlugin', action: 'shareFile', params, success, error }),
  },
  team: {
    /**
     * 获取团队协作的一些上下文数据
     * @param {*} success
     * @param {*} error
     * @returns
     */
    getTeamContext: (params, success, error) => {
      if (typeof params === 'function') {
        return shellEvent.send('TeamWork.getTeamContext', '', params, success);
      } else {
        return shellEvent.send('TeamWork.getTeamContext', '', success, error);
      }
    },
    /**
     * 创建选项卡
     * @param {
     * suggestedDisplayName：显示名
     * websiteUrl：pc地址
     * mobileUrl：移动端地址
     * removeUrl：移除接口
     * mobileRemoveUrl：移动端移除地址
     * editUrl：编辑地址
     * mobileEditUrl：移动端编辑地址
     * type：类型
     * entityId：选项卡id
     * extendData:扩展字段，string，可不传
     * }
     */
    createTeamTab: (params, success, error) => {
      if (typeof success !== 'function') {
        return shellEvent.send(
          'TeamWork.createTeamTab',
          { targetId: params, tabInfo: success },
          error,
          () => {}
        );
      } else {
        return shellEvent.send('TeamWork.createTeamTab', params, success, error);
      }
    },
    /**
     * 编辑选项卡
     * @param {
     * suggestedDisplayName：显示名
     * websiteUrl：pc地址
     * mobileUrl：移动端地址
     * removeUrl：移除接口
     * mobileRemoveUrl：移动端移除地址
     * editUrl：编辑地址
     * mobileEditUrl：移动端编辑地址
     * type：类型
     * entityId：选项卡id
     * extendData:扩展字段，string，可不传
     * groupId: string; //群组id
     * id: string; //	主键
     * }
     */
    editTeamTab: (params, success, error) =>
      shellEvent.send('TeamWork.editTeamTab', params, success, error),
    /**
    /**
     * 发送卡片消息
     * @param {*} targetId
     * @param {*} cardContent
     * @param {*} success
     * @param {*} error
     * @returns
     */
    sendCardMessage: (params, success, error) => {
      if (typeof success !== 'function') {
        return shellEvent.send(
          'TeamWork.sendCardMessage',
          { targetId: params, cardContent: success },
          error,
          () => {}
        );
      } else {
        return shellEvent.send('TeamWork.sendCardMessage', params, success, error);
      }
    },
    /**
     * 监听原生触发创建选项卡事件
     * @param {*} callback
     * @returns
     */
    onNativePerformCreateTeamTab: (params, callback) => {
      if (typeof params === 'function') {
        return shellEvent.on('TeamWork.nativePerformCreateTeamTab', params);
      } else {
        return shellEvent.on('TeamWork.nativePerformCreateTeamTab', callback);
      }
    },
    /**
     * 监听原生触发编辑选项卡提交事件
     * @param {*} callback
     * @returns
     */
    onNativePerformEditTeamTabSubmit: (params, callback) => {
      if (typeof params === 'function') {
        return shellEvent.on('TeamWork.nativePerformEditTeamTabSubmit', params);
      } else {
        return shellEvent.on('TeamWork.nativePerformEditTeamTabSubmit', callback);
      }
    },
  },

  language: {
    /**
     * 获取支持的语言
     * @callback   {default=1;lang="zh_CN";name="\U4e2d\U6587\Uff08\U7b80\U4f53\Uff09";},{default=0;lang=en;name=English;},{default=0;lang="zh_TW";name="\U4e2d\U6587\Uff08\U7e41\U9ad4\Uff09";}
     */
    getList: (params, success, error) =>
      exec({ plugin: 'LanguagePlugin', action: 'getList', params, success, error }),
    /**
     * 设置选中的语言
     * @callback
     */
    setLanguage: (params, success, error) =>
      exec({ plugin: 'LanguagePlugin', action: 'setLanguage', params, success, error }),
  },
  theme: {
    /**
     * 是否支持
     * @callback {value:true/false}
     */
    isSupportThemeSetting: (params, success, error) =>
      exec({ plugin: 'ThemePlugin', action: 'isSupportThemeSetting', params, success, error }),
    /**
     * 获取
     * @callback {theme:sys/white/black}
     */
    getTheme: (params, success, error) =>
      exec({ plugin: 'ThemePlugin', action: 'getTheme', params, success, error }),
    /**
     * 设置
     * @params {theme:sys/white/black}
     * @callback
     */
    setTheme: (params, success, error) =>
      exec({ plugin: 'ThemePlugin', action: 'setTheme', params, success, error }),
  },

  pushConfig: {
    /**
     * 获取
     * @callback
     */
    getPushConfig: (params, success, error) =>
      exec({ plugin: 'PushPlugin', action: 'getPushConfig', params, success, error }),
    /**
     * 设置
     * @params
     * @callback
     */
    setPushConfig: (params, success, error) =>
      exec({ plugin: 'PushPlugin', action: 'setPushConfig', params, success, error }),
    /**
     * 设置
     * @params
     * @callback    {value:true/false}
     */
    getRemoteNotificationEnable: (params, success, error) =>
      exec({ plugin: 'PushPlugin', action: 'getRemoteNotificationEnable', params, success, error }),
  },
  //来电识别
  callIdentification: {
    /**
     * 获取
     * @callback  {state:true/false}
     */
    getState: (params, success, error) =>
      exec({
        plugin: 'CallIdentificationPlugin',
        action: 'getCallIdentificationState',
        params,
        success,
        error,
      }),
    /**
     * 设置
     * @params {state:true/false}
     * @callback
     */
    setState: (params, success, error) =>
      exec({
        plugin: 'CallIdentificationPlugin',
        action: 'setCallIdentificationState',
        params,
        success,
        error,
      }),
    /**
     * 设置
     * @params
     * @callback    {value:true/false}
     */
    isSupport: (params, success, error) =>
      exec({
        plugin: 'CallIdentificationPlugin',
        action: 'isSupportCallIdentification',
        params,
        success,
        error,
      }),
  },
  //检查更新
  shell: {
    //检查更新
    checkUpdate: (params, success, error) =>
      exec({ plugin: 'ShellPlugin', action: 'checkUpdate', params, success, error }),
    //意见反馈
    showFeedback: (params, success, error) =>
      exec({ plugin: 'ShellPlugin', action: 'showFeedback', params, success, error }),
  },
  //视频会议
  meeting: {
    /**
     * @params {
     * confId:string
     * }
     */
    joinMeetingByConfId: (params, success, error) =>
      exec({ plugin: 'MeetingPlugin', action: 'joinMeetingByConfId', params, success, error }),
    showCreateMeeting: (params, success, error) => {
      if (typeof params === 'function') {
        return exec({
          plugin: 'MeetingPlugin',
          action: 'showCreateMeeting',
          params: {},
          success: params,
          error: success,
        });
      } else {
        return exec({
          plugin: 'MeetingPlugin',
          action: 'showCreateMeeting',
          params: {},
          success,
          error,
        });
      }
    },
    showJoinMeeting: (params, success, error) => {
      if (typeof params === 'function') {
        return exec({
          plugin: 'MeetingPlugin',
          action: 'showJoinMeeting',
          params: {},
          success: params,
          error: success,
        });
      } else {
        return exec({
          plugin: 'MeetingPlugin',
          action: 'showJoinMeeting',
          params: {},
          success,
          error,
        });
      }
    },
  },
  //视频会议
  TestBridge: {
    /**
     * @params {
     * confId:string
     * }
     */
    TestBridge: (params, success, error) =>
      exec({ plugin: 'TestBridge', action: 'testFunc', params, success, error }),
  },
  appPlugin: {
    // 输入插件
    InputPlugin: (params, actionName, success, error) =>
      exec({ plugin: 'InputPlugin', action: actionName, params, success, error }),
    // 语音播报插件
    SpeechPlugin: (params, actionName, success, error) =>
      exec({
        plugin: 'SpeechPlugin',
        action: actionName,
        params,
        success,
        error,
      }),
    // webview跳转插件
    WebPagePlugin: (params, actionName, success, error) =>
      exec({
        plugin: 'WebPagePlugin',
        action: actionName,
        params,
        success,
        error,
      }),
    // 获取系统UI插件
    SystemUiPlugin: (params, actionName, success, error) =>
      exec({
        plugin: 'SystemUiPlugin',
        action: actionName,
        params,
        success,
        error,
      }),
    // 系统comfrim插件
    DialogPlugin: (params, actionName, success, error) =>
      exec({
        plugin: 'DialogPlugin',
        action: actionName,
        params,
        success,
        error,
      }),
  },
  loginPlugin: {
    // 登录插件
    getToken: (params, actionName, success, error) =>
      exec({
        plugin: 'LoginPlugin',
        action: actionName,
        params,
        success,
        error,
      }),
    refreshToken: (params, actionName, success, error) =>
      exec({
        plugin: 'LoginPlugin',
        action: actionName,
        params,
        success,
        error,
      }),
    logout: (params, actionName, success, error) =>
      exec({
        plugin: 'LoginPlugin',
        action: actionName,
        params,
        success,
        error,
      }), 
  },
};
window.jssdk = nativeSdk;
export default nativeSdk;
