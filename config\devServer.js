// const proxySite = 'http://10.1.131.189';
// const proxySite = 'http://a82.seeyoncd.com/';
// const proxySite = 'http://************:8080/';
// const proxySite = 'http://47.92.109.34/';
// const proxySiteBi = 'http://47.92.109.34';
// const proxySite = 'http://118.112.188.178:6680';
// const proxySite = 'http://118.112.188.178:6680'
// const proxySite = 'http://10.1.200.140';
// const proxySite = 'http://************:80';
// const proxySite = 'http://10.2.4.60:80';
//胡飞环境
const proxySite = 'http://************:80';
// const proxySite = 'http://************:8080';
// const proxySite = 'http://**********:3302';
// const proxySite = 'http://************'
export default {
  proxy: {
    '^/ai-manager$': {
      target: proxySite,
      changeOrigin: true,
      rewrite: (path) => path.replace(/^\/ai-manager/, '/ai-manager'),
      configure: (proxy, options) => {
        proxy.on('proxyReq', (proxyReq, req, res) => {
          const url = new URL(proxySite);
          proxyReq.setHeader('Host', url.host);
          proxyReq.setHeader('Origin', url.host);
          proxyReq.setHeader('Referer', proxySite);
          // 防止nginx反向代理验证ip
          req.removeHeader('x-forwarded-for');
          req.removeHeader('x-forwarded-port');
          req.removeHeader('x-forwarded-host');
        });
        proxy.on('proxyRes', (proxyRes, req, res) => {
          const { statusCode, headers } = proxyRes;
          const { location } = headers;
          if (/^30[12]$/.test(String(statusCode)) && /:\/\//.test(location)) {
            const url = new URL(location);
            url.host = proxyRes.headers.host;
            headers.location = url.href;
          }
        });
      },
    },
    '^/pages/.*/ai-manager': {
      target: proxySite, // 你的目标服务器地址
      changeOrigin: true,
      rewrite: (path) => path.replace(/^\/pages\/.*\/ai-manager/, '/comi/ai-manager')
    },
    '/seeyon': {
      target: proxySite,
      changeOrigin: true,
      rewrite: (path) => path.replace(/^\/seeyon/, '/seeyon')
    },
    '/comi': {
      target: proxySite,
      changeOrigin: true,
      rewrite: (path) => path.replace(/^\/comi/, '/comi')
    },
    '/comi/seeyon': {
      target: proxySite,
      changeOrigin: true,
      rewrite: (path) => path.replace(/^\/comi\/seeyon/, '/comi/seeyon')
    },
    '/static': {
      target: proxySite,
      changeOrigin: true,
      rewrite: (path) => path.replace(/^\/static/, '/static')
    },
  },
  compress: false
};
