<template>
    <div 
      class="touch-handler"
      @touchstart="onTouchStart"
      @touchmove="onTouchMove"
      @touchend="onTouchEnd"
      @scroll="handleScroll"
      @contextmenu="preventDefaultMenu"
    >
      <slot></slot>
      <div v-if="isLoading" class="loading-indicator">
        <AiStateLoading />
      </div>
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref, onMounted, onBeforeUnmount } from 'vue';
  import API from '@/api/index';
  import AiStateLoading from '@/components/ui/ai-state/loading.vue';
  import type { HistoryParams } from '@/types/chat';
  
  const props = defineProps<{
    onLongPress?: (id: string, x: number, y: number) => void;
    historyParams?: HistoryParams;
    hasMore?: boolean;
    onLoadMore?: (isLoadMore: boolean) => void;
    historyLoading?: boolean;
  }>();
  
  let timer: any = null;
  let touchStartX = 0;
  let touchStartY = 0;
  const isLoading = ref(false);
  
  const onTouchStart = (e: TouchEvent) => {
    API.action.closeLongtouch();
    const _target = (e.target as HTMLElement);
    const isCardContainer = _target.closest('.data-shell');
    //屏蔽图表的长按
    const isChartContainer = _target.closest('.data-chart-wrap');
    //屏蔽知识源的长按
    const isKnowledge = _target.closest('.knowledge-source');
    if (!isCardContainer || isChartContainer  || isKnowledge) return;
    
    const messageList = _target.closest('.message-list-item');
    if (!messageList || !messageList.id) return;
    
    const id = messageList.id;
    touchStartX = e.touches[0].clientX;
    touchStartY = e.touches[0].clientY;
    
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => {
      const targetCard = API.store.card.find(id);
      if (!targetCard || !targetCard.data?.cardData.length || !targetCard.data?.isCompleted || targetCard.data.illageSessionType || targetCard.data.isStop) return;
      
      props.onLongPress?.(id, touchStartX, touchStartY);
    }, 500);
  };
  
  const onTouchMove = (e: TouchEvent) => {
    const moveX = e.touches[0].clientX;
    const moveY = e.touches[0].clientY;
    if (moveX !== touchStartX && moveY !== touchStartY && timer) {
      clearTimeout(timer);
    }
  };
  
  const onTouchEnd = () => {
    timer && clearTimeout(timer);
  };
  
  const preventDefaultMenu = (e: Event) => {
    const targetDom = e.target as HTMLElement;
    if (targetDom.closest('.ai-request-card')) return;
    e.preventDefault();
  };
  
  const handleScroll = (e: Event) => {
    const target = e.target as HTMLElement;
    const { scrollTop, scrollHeight, clientHeight } = target;
    
    // 当滚动到距离顶部一定距离时触发加载
    const threshold = 50; // 距离顶部的阈值
    const shouldLoad = scrollTop <= threshold && props.hasMore && !props.historyLoading;
    
    // console.log('滚动信息:', {
    //   scrollTop,
    //   scrollHeight,
    //   clientHeight,
    //   hasMore: props.hasMore,
    //   historyLoading: props.historyLoading,
    //   shouldLoad
    // });
    
    if (shouldLoad) {
      loadMore();
    }
  };
  
  const loadMore = async () => {
    if (isLoading.value || !props.onLoadMore) return;
    
    try {
      isLoading.value = true;
      await props.onLoadMore(true);
    } finally {
      isLoading.value = false;
    }
  };
  
  onBeforeUnmount(() => {
    timer && clearTimeout(timer);
  });
  </script>
  
  <style lang="scss" scoped>
  .touch-handler {
    width: 100%;
    position: relative;
    
    .loading-indicator {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 30px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  </style>