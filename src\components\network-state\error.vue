<template>
  <div class="network-state-error">
    <span class="error-content">{{errorContent}}</span>
    <span v-if="action.show" @click="refreshAction">{{action.content}}</span>
  </div>
</template>
<script setup lang="ts">
  const props = defineProps({
    errorContent: {
      type: String,
      default: ''
    },
    action: {
      type: Object,
      default: () => ({})
    }
  });
  const _$emit = defineEmits(['refreshAction']);
  function refreshAction() {
    _$emit('refreshAction');
  }
</script>

<style lang="scss" scoped>
  .network-state-error {
    margin-top: 16px;
    text-align: center;
    font-size: 14px;
    font-size: var(--theme-font-size1, 14px);
    .error-content {
      color: rgab(0, 0, 0, 0.4);
      color: var(--theme-font-color3, rgab(0, 0, 0, 0.4))
    }
    .error-action{
      color: #4379FF;
      color: var(--theme-color5, #4379FF)
    }
  }
</style>