<template>
  <div class="data-table-wrap">
    <DataHeader ref="dataHeaderRef" dataType="table" :data="JSON.parse(JSON.stringify(content))" />
    <TableContent
      :isPreview="isPreview"
      :data="JSON.parse(JSON.stringify(content))"
      @preview="preview"
      v-if="showTable"
    />
  </div>
</template>
<script setup lang="ts">
import TableContent from './content.vue';
import DataHeader from '../data-header/index.vue';
import { ref, computed, type ComponentPublicInstance } from 'vue';
import { ChartTableType } from '@/types';

const { content, isPreview } = defineProps<{
  isPreview: boolean;
  content: ChartTableType;
}>();
const dataHeaderRef = ref<ComponentPublicInstance<typeof DataHeader> | null>(null);
function preview() {
  if (dataHeaderRef.value) {
    dataHeaderRef.value?.$?.exposed?.hidePreviewTip();
  }
}
// 是否显示表
const showTable = computed(() => {
  return content?.data?.length > 0;
});
</script>
<style lang="scss">
.data-table-wrap {
  height: 100%;
  display: flex;
  flex-direction: column;
  width: 100%;
  overflow: hidden;
}
</style>
