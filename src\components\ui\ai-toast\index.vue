<template>
  <div class="ai-toast-container" :style="{ top: positionTop }">
    <Transition appear name="toast" @afterLeave="toastAnimationEnd">
      <div v-if="isShowToast" class="ai-toast">
        <div class="text">{{ content }}</div>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import {ref, onMounted} from 'vue';
const { content, positionTop, timer } = defineProps({
  content: {
    type: String,
    default: '',
  },
  positionTop: {
    type: String,
    default: 'center',
  },
  timer: {
    type: Number,
    default: 1500,
  },
});
let isShowToast = ref(true);
let _$emit = defineEmits(['close']);
onMounted(() => {
  setTimeout(() => {
    // emit('close');
    isShowToast.value = false;
  }, timer);
});

function toastAnimationEnd() {
  _$emit('close');
}
</script>

<style lang="scss" scoped>
.toast-enter-from, .toast-leave-to {
  opacity: 0;
}
.toast-enter-active, .toast-leave-active {
  transition: opacity 200ms ease-in;
}
.ai-toast-container {
  width: 80%;
  position: absolute;
  z-index: 999;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  justify-content: center;
  .ai-toast {
    background: rgba(0, 0, 0, 0.8);
    padding: 12px;
    border-radius: 6px;
    box-sizing: border-box;

    .text {
      font-size: 14px;
      font-size: var(--theme-font-size1, 14px);
      color: #fff;
      color: var(--theme-font-color5, #fff);
      line-height: 22px;
      line-height: var(--theme-line-height1, 22px);
      word-break: break-all;
    }
  }
}
</style>
