// modalPlugin.js
import { createApp, h, ref } from 'vue';
import Modal from '@/components/ui/ai-login-modal/index.vue';

type LoginNameType = {
  loginname: string;
  password: string;
}

type Options = {
  title?: string;
  onOk?: (param: LoginNameType) => void;
  onCancel?: () => void;
  content?: string;
}
export const aiModal = (options: Options) => {
  // 创建容器
  const container = document.createElement('div');
  document.body.appendChild(container);

  const visible = ref(true);

  const instance = createApp({
    setup() {
      const handleClose = () => {
        visible.value = false;
        setTimeout(() => {
          instance.unmount();
          document.body.removeChild(container);
        }, 300);
      };

      return () =>
        h(
          Modal,
          {
            visible: visible.value,
            title: options.title,
            onOk: (params: LoginNameType) => {
              options.onOk?.(params);
              handleClose();
            },
            onCancel: () => {
              options.onCancel?.();
              handleClose();
            },
            'onUpdate:visible': (val: boolean) => {
              visible.value = val;
            },
          },
          {
            default: () => options.content,
          }
        );
    },
  });

  instance.mount(container);
};

export default {
  aiModal,
};
