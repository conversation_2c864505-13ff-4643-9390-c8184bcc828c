<template>
  <div class="ai-header">
    <div v-if="!hiddenBackBtn" class="iconfont ai-icon-zuo back-btn" @click="goBack"></div>
    <div class="ai-header-content">
      <slot></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive } from 'vue';
import API from '@/api/index';
import { getSystemUI, closeWebView } from '@/plugins/app-plugin';
import { getQueryString, getPagePaddingTopStyle } from '@/utils/common';

const openType = getQueryString('openType');

const props = defineProps({
  hiddenBackBtn: {
    type: Boolean,
    default: false,
  },
});
let wrapStyle = reactive<any>({
  ...getPagePaddingTopStyle(),
});
function goBack() {
  closeWebView(openType);
};
</script>

<style lang="scss" scoped>
.ai-header {
  padding: 0 28px 0 12px;
  display: flex;
  line-height: 44px;
  font-weight: 600;
  box-sizing: border-box;
  box-sizing: border-box;
  .back-btn {
    vertical-align: middle;
    font-size: 20px;
    font-weight: 400;
    color: #333;
  }
  .ai-header-content {
    flex: 1;
    height: 44px;
    padding: 0 12px;
    text-align: center;
    box-sizing: border-box;
    white-space: nowrap; /* 确保文本在一行内显示 */
    overflow: hidden; /* 隐藏超出容器的内容 */
    text-overflow: ellipsis; /* 显示省略号 */
  }
}
</style>
