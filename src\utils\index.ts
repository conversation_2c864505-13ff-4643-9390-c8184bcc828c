import { setStreamString, parseString, getNewToken, formatContent, formatHistoryContent, handleStreamMessage, dealKnowledgeData, dealMarkerData } from './utils';
import * as common from './common';

const utils = {
  setStreamString,
  parseString,
  getNewToken,
  formatContent,
  formatHistoryContent,
  handleStreamMessage,
  dealKnowledgeData,
  dealMarkerData,
  common
};

export default utils;
export { setStreamString, parseString, getNewToken, formatContent, formatHistoryContent, common, handleStreamMessage, dealKnowledgeData, dealMarkerData };
