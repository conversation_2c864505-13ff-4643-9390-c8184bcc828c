<template>
  <div class="feedback-help" :style="personalWrap">
    <LayoutHeader>帮助反馈</LayoutHeader>
    <div class="body-container">
      <div>
        <span>反馈信息</span>
        <span class="must-fill">*</span>
      </div>
      <div class="textarea-container">
        <textarea
          v-model="feedbackMessage"
          class="feedback-textarea"
          placeholder="请输入"
          maxlength="500"
        ></textarea>
        <div class="feedback-textarea-count">
          <span>{{ feedbackMessage.length }}</span>
          <span>/</span>
          <span>500</span>
        </div>
      </div>
      <div class="contact-information">
        <div class="title">联系方式</div>
        <input
          v-model="contactMessage"
          type="text"
          class="contact-message"
          placeholder="请输入您的联系方式"
        />
      </div>
    </div>
    <LayoutFooter
      :buttonText="buttonText"
      :type="buttonType"
      :disabled="disabled"
      @buttonAction="submitAction"
      class="padding-setting"
    ></LayoutFooter>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue';
import LayoutHeader from '@/components/layout/layout-header.vue';
import LayoutFooter from '@/components/layout/layout-footer.vue';
import { getDeviceInfo, getQueryString } from '@/utils/common';
import { closeWebView } from '@/plugins/app-plugin';
import { aiToast } from '@/plugins/ai-toast';
import request from '@/api/requests';
const openType = getQueryString('openType');
const { isApp } = getDeviceInfo();
const buttonText = '提交';
const buttonType = 'primary';
const personalWrap = reactive({
  height: '100vh',
  width: '100%',
  background: isApp ? 'transparent' : '#edf2fc',
});
const feedbackMessage = ref('');
const contactMessage = ref('');
const disabled = computed(() => {
  if (feedbackMessage.value) {
    return false;
  }
  return true;
});
//TODO: 提交反馈信息
function submitAction() {
  const params = {
    content: feedbackMessage.value,
    contactMethod: contactMessage.value,
  };
  request.feedbackInfo(params).then(
    data => {
      if (Number(data?.code) === 0) {
        aiToast({
          content: '反馈成功',
          timer: 1500,
        });
        //提示显示完再关闭
        setTimeout(() => {
          closeWebView(openType);
        }, 1800);
      } else if (data?.message) {
        aiToast({
          content: data.message,
        });
      }
    },
    err => {}
  );
}
</script>

<style lang="scss" scoped>
.feedback-help {
  display: flex;
  flex-direction: column;
  height: 100vh;
  .body-container {
    font-size: 14px;
    flex: 1;
    padding: 0 12px;
    margin-top: 8px;
    overflow: auto;
    .must-fill {
      color: #ff4f4b;
      margin-left: 2px;
      vertical-align: middle;
    }
    .textarea-container {
      height: 286px;
      overflow: hidden;
      box-sizing: border-box;
      margin-top: 4px;
      border-radius: 8px;
      padding: 8px;
      background: rgba(255, 255, 255, 1);
      background: var(--theme-font-color5, rgba(255, 255, 255, 1));
    }
    .feedback-textarea {
      height: 240px;
      overflow: auto;
      width: 100%;
      outline: 0;
      -webkit-appearance: none;
      -webkit-user-select: text;
      border: none;
      resize: none;
      font-size: 16px;
      &:active {
        background: transparent !important;
      }
    }
    .feedback-textarea-count {
      color: rgba(0, 0, 0, 0.2);
      text-align: right;
      line-height: 22px;
      margin-top: 3px;
      font-size: 12px;
    }
  }
  .button-style {
    background-color: #4379ff;
    color: #fff;
  }
  .contact-information {
    margin-top: 12px;
    margin-bottom: 12px;
    .contact-message {
      margin-top: 4px;
      height: 40px;
      padding: 8px;
      box-sizing: border-box;
      width: 100%;
      border: none;
      border-radius: 8px;
      font-size: 16px;
    }
  }
  textarea::-webkit-input-placeholder,
  input::-webkit-input-placeholder {
    color: rgba(0, 0, 0, 0.2);
  }
  .padding-setting {
    padding-left: 12px;
    padding-right: 12px;
  }
}
</style>

<style>
body, html {
  overflow: hidden !important;
}
::-webkit-scrollbar {
  display: none;
}
</style>
