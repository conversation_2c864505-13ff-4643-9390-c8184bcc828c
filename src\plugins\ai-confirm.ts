import { createApp, h, ref, type App } from 'vue';

import AiConfirm from '@/components/ui/ai-confirm/index.vue';
type Options = {
  title?: string;
  content?: string;
  onConfirm?: () => void;
  onCancel?: () => void;
};

export const aiConfirm = (options: Options) => {
  // 创建容器
  const container = document.createElement('div');
  document.body.appendChild(container);

  // 创建 AiConfirm 组件实例
  const dialogApp = createApp({
    setup() {
      const visible = ref(true);
      //确认回调
      const handleConfirm = () => {
        if (options.onConfirm) {
          options.onConfirm();
        }
      };
      //取消回调
      const handleCancel = () => {
        if (options.onCancel) {
          options.onCancel();
        }
      };

      return {
        visible,
        handleConfirm,
        handleCancel,
      };
    },
    render() {
      return h(AiConfirm, {
        title: options.title,
        content: options.content,
        visible: this.visible,
        onConfirm: this.handleConfirm,
        onCancel: this.handleCancel,
        'onUpdate:visible': (value: boolean) => {
          if (!value) {
            destroy();
          }
          this.visible = value;
        },
      });
    },
  });

  // 渲染 AiConfirm
  dialogApp.mount(container);

  // 销毁 AiConfirm
  function destroy() {
    dialogApp.unmount();
    document.body.removeChild(container);
  }
};

export default {
  aiConfirm,
  install(app: App) {
    // 全局挂载方法
    app.config.globalProperties.$aiConfirm = aiConfirm
  },
};
