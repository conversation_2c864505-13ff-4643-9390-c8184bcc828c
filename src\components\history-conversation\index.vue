<template>
  <div class="history-conversation">
    <div class="left-linear linear-common"></div>
    <div class="conversation-content">下拉查看历史会话</div>
    <div class="right-linear linear-common"></div>
  </div>
</template>

<script setup lang="ts">
</script>

<style lang="scss" scoped>
.history-conversation {
  display: flex;
  align-items: center;
  text-align: center;
  .linear-common {
    flex: 1;
    height: 1px;
    transform: scaleY(0.5);
    vertical-align: middle;
  }
  .left-linear {
    background: linear-gradient(90deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.2) 99.74%);
  }
  .right-linear {
    background: linear-gradient(90deg, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0) 99.74%);
  }
  .conversation-content {
    margin: 0 12px;
    font-size: 14px;
    font-size: var(--theme-font-size2, 14px);
    color: rgba(0, 0, 0, 0.4);
    color: var(--theme-font-color3, rgba(0, 0, 0, 0.4));
  }
}
</style>
