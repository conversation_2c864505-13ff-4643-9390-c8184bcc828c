import store from './store';
import Vue from 'vue';
// 状态管理
export const BASE_STATUS = {
  INIT: 0, // 初始化
  LOADING: 1, // 加载中
  LOADED: 2, // 已加载
  ERROR: 3, // 出错
};
export const REQUEST_LOOP_TIME = 500;
export const AWAIT_AI_TIMEOUT_MS = 450 * 1000;

// loading组件缓存
let vueLoadingExtend = null;
export const awaitFunction = (fn, check) => {
  return new Promise((resolve, reject) => {
    setTimeout(async () => {
      if(!check()){
        await fn();
      }
      resolve();
    }, REQUEST_LOOP_TIME);
  });
};
export const fastAwaitFunction = (fn, check) => {
  return new Promise((resolve, reject) => {
    setTimeout(async () => {
      if(!check()){
        await fn();
      }
      resolve();
    }, 100);
  });
};
// 观察者
function streamFn(card, option = {}) {
  this.card = card;
  this.isStop = false;
  this.timer = null;
  this._id = new Date().getTime();
  this.startTime = this._id;
  // option转存
  this.remove = option.remove;
  this.startTask = option.startTask;
  this.runTask = option.runTask;
  this.stopTask = option.stopTask;
  this.innerHTML = option.innerHTML || '';
  this.context = '';
  this.hasContext = false;
  this.status = BASE_STATUS.INIT;
}

streamFn.prototype = {
  run: async function () {
    let { card, startTime, startTask, runTask } = this;
    let startRes = await startTask();
    let taskId = startRes;
    vueLoadingExtend = vueLoadingExtend || Vue.extend(TextLoading);

    let loadingInstance = new vueLoadingExtend({
      propsData: {},
    }).$mount();
    let loadingInnerHTML = loadingInstance.$el.innerHTML;
    // let context = '数据 \n::: chart {"id":"3e1cb1c4-bc4c-4340-825a-5bd7eabc2a94"}\n:::\n'; // 上下文
    // code
    if (!startRes) {
      return this.stop(false, '未获取到任务队列，请稍后重试！');
    }
    // 终止
    if (this.isStop) {
      return;
    }
    // loop----
    const httpRun = async () => {
      let res = await runTask(taskId);
      if (res) {
        if(this.isStop){
          return;
        }
        if(res.content){
          this.hasContext = true;
        }
        this.context = this.context + res.content;
        let extra = res.extra || {};
        // 图片处理
        if (extra.data_detail) {
          extra.data_detail = extra.data_detail.map(el => {
            return JSON.parse(el);
          });
          this.hasContext = true;
        }
        if(extra.error_input && extra.error_input.length){
          this.context = this.context + extra.error_input[0];
          this.hasContext = true;
        }
        // 推荐问题
        if (extra.extra_questions) {
          // recomment related
          extra.question_type = 'recomment';
          extra.extra_questions = JSON.parse(extra.extra_questions[0]);
          this.hasContext = true;
          // extra.extra_questions = ['分析一下标记为收益的记录中金额最高的前五条记录', '查询标记为亏损的记录中金额总和', '对比不同公司的平均金额'];
        }
        // 新增上下文
        store.action.addContext(extra);
        if (res.over) {
          if (res.message) {
            // 显示错误信息
            this.context = this.context + res.message;
          }
          // 更新进度
          store.action.setExtendData(card, {
            status: BASE_STATUS.LOADED,
            innerHTML: this.context,
            ...extra,
            loadingInnerHTML: '',
          });
          store.scrollToBottom();
          this.stop(true);
        } else {
          // 更新进度
          store.action.setExtendData(card, {
            status: BASE_STATUS.LOADING,
            innerHTML: this.context,
            ...extra,
            loadingInnerHTML,
          });
          store.scrollToBottom();
          // 超时
          if (new Date().getTime() - startTime > AWAIT_AI_TIMEOUT_MS) {
            this.stop(false, '请求已超时');
          } else {
            await awaitFunction(httpRun, ()=>{
              if(this.isStop)return true;
            });
          }
          
        }
      } else {
        // 错误
        this.stop(false, res.message || '发生错误！！');
      }
      store.scrollToBottom()
    };
    await httpRun(taskId);
    return this.status;
  },
  stop: function (isNormal, msg) {
    if(!this.isStop){
      this.status = isNormal ? BASE_STATUS.LOADED : BASE_STATUS.ERROR;
      this.isStop = true;
      this.remove(this);
      if (this.card) {
        if (!isNormal) {
          msg = msg || '此消息已停止生成';
          if(!this.hasContext){
            this.context = this.context + msg
            store.action.setExtendData(this.card, { status: BASE_STATUS.LOADED, loadingInnerHTML: this.context });
          }else{
            store.action.setExtendData(this.card, { status: BASE_STATUS.ERROR,msg , loadingInnerHTML: '' });

          }
        }
      }
      // 如果非正常结束，才调用停止接口
      if (!isNormal) {
        this.stopTask();
      }
    }
  },
};

export default streamFn;
