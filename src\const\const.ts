export const DIALOGUE = 'dialogue'; // CoMI
export const AGENT = 'agent'; // 智能体
export const SIDEBAR = 'sidebar';
export const MAX_RETRY_COUNT = 3;
export const BASEURL = '/comi'
export const ICON_MAP = {
  doc: 'docIconfont doc doc-color',
  docx: 'docIconfont docx docx-color',
  pdf: 'docIconfont pdf pdf-color',
  wps: 'docIconfont wps wps-color',
  xlsx: 'docIconfont xls xlsx-color',
  txt: 'docIconfont txt txt-color',
  news: 'docIconfont news news-color',
  ppt: 'docIconfont ppt ppt-color',
  1: 'ai-icon-xietong', //协同
  2: 'ai-icon-biaodan', //表单
  3: 'ai-icon-wendang', //文档
  4: 'ai-icon-gongwen1', //公文
  5: 'ai-icon-shijiananpai', //计划，时间安排
  6: 'ai-icon-shijiananpai', //会议 -> 时间安排
  7: 'ai-icon-wenhuajianshe', //新闻、公告、调查、讨论
  8: 'ai-icon-wenhuajianshe',
  9: 'ai-icon-wenhuajianshe',
  10: 'ai-icon-wenhuajianshe',
  62: 'ai-icon-tongxunlu',//通讯录
  66: 'ai-icon-biaodan', //无流程
  // questionAndAnswer: '',问答,
  // application: 'ai-icon-yingyong', //应用
  127: 'ai-icon-xietongyouxiang',//协同邮箱
  70: 'ai-icon-baobiao', //报表
  94: 'ai-icon-shijiananpai', //领导日程 -> 时间安排
  30: 'ai-icon-shijiananpai', // 任务管理 -> 时间安排
  11: 'ai-icon-shijiananpai', // 日程 -> 时间安排
  // 5: 'ai-icon-shijiananpai' // 计划 -> 时间安排， 移动端没有
};

//  应用映射关系
export const APP_NAME_MAP:{[key: string]: string} = {
  'all': '全部',
  '1': '协同',
  '2': '表单',
  '3': '文档',
  '4': '公文',
  'cultureBuild': '文化建设',
  '62': '通讯录',
  '70': '报表',
  '127': '协同邮箱',
  'timeSchedule': '时间安排',
  '0': '本地知识'
}
//顺序：全部、问答、通讯录、应用、协同、协同邮箱、表单、公文、报表、文档、文化建设、时间安排、第三方待办， 目前没有问答、应用、时间安排和第三方待办
export const TYPE_ARR = ['all', '62', '1', '127', '2', '4', '70', '3', 'cultureBuild', 'timeSchedule', '0'];