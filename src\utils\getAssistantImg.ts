// TODO 后期中台返回
import img1 from '@/assets/assistant/-951648709342113926.png'; // 组织日常效能专家
import img2 from '@/assets/assistant/-6306818762847599426.png'; // 合同
import img3 from '@/assets/assistant/5880085826466236088.png'; // 周报
import img4 from '@/assets/assistant/7571040887826172408.png'; // 制度
export const getAssistantImg = (id: string) => {
  const allAssistantImg = {
    '-951648709342113926': {
      img: img1,
      background: '',
    },
    '-6306818762847599426': {
      img: img2,
      background: '',
    },
    '5880085826466236088': {
      img: img3,
      background: '',
    },
    '7571040887826172408': {
      img: img4,
      background: '',
    }
  };
  return allAssistantImg[id as keyof typeof allAssistantImg] || null;
};

