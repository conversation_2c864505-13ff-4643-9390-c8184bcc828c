<template>
  <div class="data-stream">
    <div v-html="mdHTML"></div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue';
import { markdownRender } from '@/utils/markdown'; // 假设 markdownRender 是一个工具函数

// 定义 props
const props = defineProps({
  content: {
    type: Object,
    required: true,
  },
  historyData: {
    type: Boolean,
    default: false,
  }
});

// 计算 mdHTML
const mdHTML = computed(() => {
  let markdownData = props.content.context || '';
  // let isEnd = props.content.extend?.isEnd || false;
  let mdHTML = null;
  if(props.content.finish === 1){
    mdHTML = markdownRender(markdownData, true);
  }else{
    mdHTML = markdownRender(markdownData, false);
  }
  return mdHTML;
});

onMounted(() => {});

</script>

<style lang="scss">
.data-stream {
  table{
    table-layout: fixed;
    display: block;
    overflow-x: auto;
    border-collapse: collapse;
    text-align: center;
    position: relative;
    width: 100%;
    margin-bottom: 8px;
    th {
      background-color: #f3f6ff;
    }
    th, td {
      padding: 8px 12px;
      box-sizing: border-box;
      border: 0.5px solid #BDC5D6;
      min-width: 106px;
    }
    td {
      color: #333;
    }

  }
  > div {
    width: 100%;
  }

  h1 {
    font-size: 18px;
    font-size: var(--theme-font-size3, 18px);
    font-weight: bold;
    line-height: 28px;
    line-height: var(--theme-line-height4, 28px);
  }

  h2, h3 {
    font-size: 16px;
    font-size: var(--theme-font-size2, 16px);
    font-weight: 400;
    // color: #8f8f94;
    font-weight: bold;
    line-height: 26px;
    line-height: var(--theme-line-height3, 26px);
  }

  pre {
    white-space: pre-wrap;
    word-wrap: break-word;
  }
     
}
</style>
