import { getAssistantId } from '@/utils/common';
function getIndexDB() {
  const indexedDB: IDBFactory | null = window.indexedDB
  if (indexedDB) {
    return indexedDB;
  }
  console.error('indexedDB not supported by this browser');
  return null;
}

export default class ComiDB {
  static db: IDBDatabase | null = null;
  static storeName = 'messages-history';
  constructor() { }
  static async initDB() {
    let assistantId = getAssistantId()
    return new Promise((resolve, reject) => {
      let db;
      // ComiDB.storeName = 'comi-messages-' + assistantId;
      const indexedDB = getIndexDB();
      if (!indexedDB) {
        reject(new Error('indexedDB not supported by this browser'));
        return;
      }
      const ComiDBRequest = indexedDB.open('comi-' + assistantId, 1);
      //onupgradeneeded
      ComiDBRequest.onupgradeneeded = () => {
        // 获取数据库连接
        db = ComiDBRequest.result;
        // 定义新的存储
        db.createObjectStore(ComiDB.storeName, {
          keyPath: 'indexKey',
          autoIncrement: false,
        });
        ComiDB.db = db;
      };

      ComiDBRequest.onerror = event => console.error('IndexDB Error: ', event);
      // 初始化成功
      ComiDBRequest.onsuccess = event => {
        const target = event.target as IDBRequest;
        db = target.result;
        console.log('comi-db is success');
        ComiDB.db = db;
        resolve(db);
      };
    });
  }
  static addData(indexKey: string, data: any) {
    const storeName = ComiDB.storeName;
    return new Promise((resolve, reject) => {
      if (ComiDB.db === null) {
        reject(new Error('ComiDB.db not init'));
        return;
      }
      const store = ComiDB.db.transaction([storeName], 'readwrite').objectStore(storeName);
      const request2 = store.add({ indexKey, data }); // 仓库对象
      request2.onsuccess = function (event2) {
        resolve(event2);
      };
    });
  }
  static addOrUpdateData(indexKey: number | string, data: any) {
    const storeName = ComiDB.storeName;
    return new Promise((resolve, reject) => {
      if (ComiDB.db === null) {
        reject(new Error('ComiDB.db not init'));
        return;
      }
      const store = ComiDB.db.transaction([storeName], 'readwrite').objectStore(storeName);
      const request1 = store.delete(indexKey);
      // 先删除旧数据
      request1.onsuccess = function (event1) {
        const request2 = store.add({ indexKey, data }); // 仓库对象
        request2.onsuccess = function (event2) {
          resolve(event2);
        };
      };
    });
  }
  static delete(indexKey: number | string) {
    return new Promise((resolve, reject) => {
      if (ComiDB.db === null) {
        reject(new Error('ComiDB.db not init'));
        return;
      }
      const storeName = ComiDB.storeName;
      const store = ComiDB.db.transaction([storeName], 'readwrite').objectStore(storeName);
      const request1 = store.delete(indexKey);
      request1.onsuccess = function (event1) {
        resolve(event1);
      };
    });
  }
  static clean() {
    const storeName = ComiDB.storeName;
    return new Promise((resolve, reject) => {
      if (ComiDB.db === null) {
        reject(new Error('ComiDB.db not init'));
        return;
      }
      const store = ComiDB.db.transaction([storeName], 'readwrite').objectStore(storeName);
      const request = store.clear(); // 清除所有数据
      request.onsuccess = function (event) {
        console.log('所有数据已清除');
        resolve(event);
      };
      request.onerror = function (event) {
        console.error('清除数据失败:', event);
        reject(event);
      };
    });
  }
  static queryAll(filter?: any) {
    const storeName = ComiDB.storeName;
    return new Promise((resolve, reject) => {
      if (ComiDB.db === null) {
        reject(new Error('ComiDB.db not init'));
        return;
      }
      const store = ComiDB.db.transaction([storeName], 'readonly').objectStore(storeName);
      const request = store.getAll();
      request.onsuccess = function (event) {
        const target = event.target as IDBRequest;
        const allData = target.result;
        if (filter) {
          const filterData = allData.filter(filter);
          resolve(filterData);
        } else {
          resolve(allData);
        }
      };
    });
  }
  static query(indexKey: number | string) {
    return new Promise((resolve, reject) => {
      return new Promise((resolve, reject) => {
        if (ComiDB.db === null) {
          reject(new Error('ComiDB.db not init'));
          return;
        }
        const store = ComiDB.db.transaction([ComiDB.storeName], 'readonly').objectStore(ComiDB.storeName);
        const request1 = store.get(indexKey);
        request1.onsuccess = function (event1) {
          resolve(request1.result);
        };
      });
    })
  }
}
