import { reactive, markRaw } from 'vue';
import { buildUUID, getQueryString } from '@/utils/common';
import { DIALOGUE } from '@/const/const';
import { CardDataType, CardContentType } from '@/types/api';

interface CardType {
  add: (data: any, targetData?: any, option?: any) => any;
  remove: (data: any) => any;
  find: (id: string | number, option?: any) => any;
  addTimeBox: (currTime: number) => any;
  [key: string]: (...args: any[]) => any;
}

const action: any = {};
const card = {} as CardType
//静态的非响应式数据
const staticData: any = {
  lastCard: null,
  streamParams: {
    assistantId: '',
    assistantCode: '',
    chatSessionId: '',
    citations: [],
    input: '',
    sessionId: '',
  },
  lastTime: 0,
  assistantId: '',
  lastCardTime: null,
  historyDataList: [], //历史记录时间显示计算
  callId: null,
  isInterrupt: false,
  aiSessionIdKey: '',
  weekRequestCount: 0,
  instanceLongTouch: null,
  longTouchCard: null, //长按的卡片
  hasVoiceKey: false,
  uploadFileInfo: null,
  isDigtalChat: false //是否是数字人
};
const state: any = reactive({
  allCardData: [], //页面上数据的data
  isChat: false, // 是否是对话页
  isLoading: false,
  isOpenVoice: false, //是否开启语音
  requestFailure: false, //是否发送失败了
  lastRequestTime: null, //最后一次发送的时间
  systemStyle: {
    top: 0,
    bottom: 0,
  }, // 系统参数，获取顶部底部的边距
  chatSessionId: buildUUID(),
  // 判断是主 COMI 还是 单个智能体 single => true:单体对话;false:comi对话
  single: getQueryString('isSingle').toLowerCase() === '0'? true : false,
  // historyModel => true: history模式；false:chat模式
  historyModel: getQueryString('fromHistory').toLowerCase() === 'true' ? true : false, 
  historySessionId: getQueryString('historySessionId'),//历史记录的sessionId
  currentHeaderKey: DIALOGUE, // 当前头部key 默认对话
  isNewView:  getQueryString('isNewView') === '1' ? true : false, 
  isScrolling: true,
  assistantInfo: null, // 助手信息
  showScrollBtn: false, // 是否显示滚动按钮
  currentTabKey: '', // 智能体页签
  selectedAgentInfo: null, //选中的agent的信息
});


function init() {

}

/**
 * @description 设置属性，方便监听
 * @param {String} key key
 * @param {Any} value 属性
 */
action.setState = function (key: string, value: any) {
  state[key] = value;
};
action.setStaticData = function (key: string, value: any) {
  staticData[key] = value;
};
/**
 * @description 单独抽出来的应用页面跳转方法
 * @param {Object} options
 * @param {String} options.appId //应用id
 * @param {Object} options.gotoParam //应用跳转的参数
 */
card.add = function (data, targetData, option) {
  if (!data) return;
  const random = Math.floor(Math.random() * 1000); 
  const now = new Date();
  const currTime = now.getTime() + "_" + random; //出现了两个一样的时间戳，获取时间加上一个随机数字符串
  data._id = data._id || currTime;
  data._t = currTime;
  // 禁止卡片响应式
  data = markRaw(data);
  if (data.data) {
    data.data = reactive(data.data);
  }
  // 清除属性
  if (staticData.lastCard && data.componentKey !== 'PersonalInfo') { //如果是人员卡片，不视为是最后一张卡片
    staticData.lastCard.config.isLast = false;
  }
  // 最后卡片标记
  if (['AiDataCard'].includes(data.componentKey)) {
    data.config = reactive({
      isLast: true
    });
    staticData.lastCard = data;
  }
  if (targetData) {
    state.allCardData[state.allCardData.length - 1] = data;
  } else {
    // 只有新卡片才追加time标记 超过10分钟 测试代码 10秒钟
    if (staticData.lastTime && currTime - staticData.lastTime > 5 * 1000 * 60 && !staticData.isDigtalChat) {
      // 追加时间卡片
      card.addTimeBox(currTime);
    } else {
      staticData.lastTime = currTime;
    }
    state.allCardData.push(data);
  }
  // 触发存储
  // scrollToBottom();
  return data;
};
// 添加时间卡片
card.addTimeBox = function (currTime: number) {
  staticData.lastTime = currTime;
  card.add({
    componentKey: 'AiShowTimeCard',
    staticData: {
      time: currTime
    }
  });
};

// 删除卡片
card.remove = function (data) {
  if (data) {
    state.allCardData = state.allCardData.filter((item:CardDataType) => item._id !== data._id);
    action.removeCardFromHistory(data);
  }
};
//默认从后往前找
card.find = function (id, option = { reverse: true }) {
  if (option.reverse) {
    return state.allCardData.reduceRight((target:CardDataType | null, item:CardDataType) => {
      // 此处调整为字符串比较  目前接口返回的 id 值超出 parseInt 能处理的最大值
      if (String(item._id) === String(id)) {
        target = item;
      }
      return target;
    }, null);
  } else {
    return state.allCardData.find((item:CardDataType) => String(item._id) === String(id));
  }
};

//获取指定卡片里面的文本数据
card.getCardToText = function (cardData) {
  let text = '';
  if (cardData) {
    cardData.forEach((child:CardContentType) => {
      if (!child.isCard && !child.isIframe) {
        text += child.context;
      }
    });
  }
  return text;
}

// 滚动到底部
function scrollToBottom() {
  if (!state.isScrolling) return;
  setTimeout(() => {
    let dom = document.getElementById('messageBoxBottom');
    if (dom) {
      dom.scrollIntoView(false);
    }
  });
}



action.clearAllData = function () {
  state.allCardData = [];
  staticData.historyDataList = [];
  // 清除最后一个时间标记
  staticData.lastTime = 0;
  staticData.callId = null;
  staticData.weekRequestCount = 0;
};
action.addCustomCard = function (data: CardDataType) {
  state.allCardData.push(data);
}
//历史记录需要在前面插入
action.addCustomCardBefore = function (data: CardDataType) {
  state.allCardData.unshift(data);
}
action.changePlayState = function (state: boolean) {
  if (staticData.longTouchCard?.data) {
    staticData.longTouchCard.data.isPlay = state;
  }
}



// 抛出对象
export default {
  state,
  action,
  card,
  staticData,
  init,
  scrollToBottom,
};
