import { createApp, defineAsyncComponent } from 'vue';
import App from './index.vue';
import '../../assets/base.css';
import '@/assets/variables.css';
import '@/assets/icon/iconfont.css';
import '@/assets/icon/doc-iconfont.css';
import AiConfirm from '../../plugins/ai-confirm';
import AiToast from '../../plugins/ai-toast';
import API from '@/api';
import "@seeyon/seeyon-comi-plugins-library/dist/seeyon-comi-plugins-library.css";

// let eg = [
//  '哈哈哈哈，这是我的总结：','的总结：<ai',`card>{test`,`: \'123\'}</aicard> 哈哈`, '阿斯达发斯蒂芬<','ai','123213</aicard>', '阿斯达发斯蒂芬'
// ]
// let dataItem = {
//     index: 0,
//     context: ""
// };
// let items = [];
// items.push(dataItem);
// const testData = '哈哈哈哈，这是我的总结：<aicard>{test: \'123\'}</aicard> 哈哈哈哈'
// ['哈哈哈哈，这是我的总结：','的总结：<ai',`card>{test`,`: \'123\'}</aicard> 哈哈`, '阿斯达发斯蒂芬'].forEach((element,index) => {
//     items = API.utils.setStreamString(element, items[items.length - 1], index)
//     items.forEach((item,idx)=>{
//         console.log(item.index+"-"+idx, item.context, item.buffer)
//     })
// });
// eg.forEach((element,index) => {
//     let tmpItmes = API.utils.setStreamString(element, items[items.length - 1], index)
//     tmpItmes.forEach((item,idx)=>{
//         console.log(item.index+"-"+idx, item.context, item.buffer)
//         items[item.index] = item;
//     })
// });
// console.log(items)
// 初始化
API.init();
const app = createApp(App);

// 引入远程组件
const ComiExternalBiCard = defineAsyncComponent(() => import("remote_app/ComiExternalBiCard"));

// 注册远程组件
app.component("ComiExternalBiCard", ComiExternalBiCard);

app.use(AiToast);
app.use(AiConfirm);
app.use(API);
app.config.errorHandler = function(err:any, instance, info) {
  const params = {
    type: 'error',
    info: err.message
  }
  API.requests.logRecord(params);
}
app.mount('#app');
