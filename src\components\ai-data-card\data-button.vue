<template>
  <div class="data-button">
    <div class="left">
      <span v-for="item in leftIconArray" :key="item.id">
        <img
          v-if="item.type === 'image'"
          :src="imageSrcObj[item.name]"
          @click="iconfontClick(item)"
          class="icon-common image"
        />
        <span
          v-else
          :class="item.name"
          class="iconfont icon-common"
          @click="iconfontClick(item)"
        ></span>
      </span>
    </div>
    <div class="right">
      <span v-for="item in rightIconArray" :key="item.id">
        <img
          v-if="item.type === 'image'"
          :src="imageSrcObj[item.name]"
          @click="iconfontClick(item)"
          class="icon-common image"
        />
        <span
          v-else
          :class="item.name"
          class="icon-right iconfont icon-common"
          @click="iconfontClick(item)"
        ></span>
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
interface IconItem {
  id: number | string;
  name: string;
  type?: string;
}
interface ImageSrcObj {
  [key: string]: string;
}
const props = defineProps({
  leftIconArray: {
    type: Array<IconItem>,
    default: () => ([]),
  },
  rightIconArray: {
    type: Array<IconItem>,
    default: () => ([]),
  },
});
import playBlue from '@/assets/images/play-blue.gif';
const imageSrcObj: ImageSrcObj = {
  reading: playBlue,
};
const _$emit = defineEmits(['iconClick']);
function iconfontClick(item: IconItem) {
  _$emit('iconClick', item);
}
</script>

<style lang="scss" scoped>
.data-button {
  width: 100%;
  display: flex;
  .left {
    flex: 1;
  }
  .icon-common {
    color: rgba(0, 0, 0, 0.6);
    color: var(--theme-font-color2, rgba(0, 0, 0, 0.6));
    font-size: 20px;
  }
  .icon-blue {
    color: #4379ff;
  }
  .icon-left {
    margin-right: 12px;
  }
  .icon-right {
    margin-left: 12px;
  }
  .image {
    height: 20px;
  }
}
</style>
