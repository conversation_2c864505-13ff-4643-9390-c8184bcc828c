declare module '*.vue' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}
declare module 'markdown-it'

// 添加类型声明文件（新建文件：types/json.d.ts）
declare module "*.json" {
  const value: any;
  export default value;
}
declare module '*.png' {
  const value: string;
  export default value;
}

declare const ZYJSBridge: {
  setEventListener: (event: string, callback: Function) => void;
  removeEventListener: (event: string, callback: Function) => void;
};

declare module '@/api/sdk'