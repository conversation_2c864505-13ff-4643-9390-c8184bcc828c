<template>
  <div class="data-table-wrap" :style="computedStyle">
    <LayoutHeader>
      <DataHeader :title="content.title" />
    </LayoutHeader>
    <div class="preview-chart-container">
      <TableContent
        :isPreview="isPreview"
        :data="JSON.parse(JSON.stringify(content))"
        @preview="preview"
        v-if="showTable"
        :style="{ height: contentHeight+'px' }"
      />
    </div>
  </div>
</template>
<script setup lang="ts">
import TableContent from './content.vue';
import DataHeader from '@/components/data-header/preview-header.vue';
import { ref, computed, inject, type ComponentPublicInstance } from 'vue';
import LayoutHeader from '@/components/layout/layout-header.vue';
import { ChartTableType } from '@/types';

const { content, isPreview, computedStyle } = defineProps<{
  isPreview: boolean;
  content: ChartTableType;
  computedStyle: Object;
}>();
const contentHeight = inject('contentHeight');
const dataHeaderRef = ref<ComponentPublicInstance<typeof DataHeader> | null>(null);
function preview() {
  if (dataHeaderRef.value) {
    dataHeaderRef.value?.$?.exposed?.hidePreviewTip();
  }
}
// 是否显示表
const showTable = computed(() => {
  return content?.data?.length > 0;
});
</script>
<style lang="scss">
.data-table-wrap {
  height: 100%;
  display: flex;
  flex-direction: column;
  width: 100%;
  overflow: hidden;
  .ai-header {
    padding-right: 4px !important;
    padding-left: 16px !important;
  }
  .preview-chart-container {
    padding: 0 16px;
    box-sizing: border-box;
  }
}
</style>
