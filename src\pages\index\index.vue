<template>
  <div id="homePage" :style="wrapStyle">
    <!-- <AiUIHeader></Ai<PERSON>Header> -->
    <!-- 公共头部 -->
    <!-- <ComiExternalExample></ComiExternalExample> -->
    <AiHeader
      class="padding-setting"
      :openSwitch="openSwitch"
      :showSquare="showSquare"
      @changeSwitch="changeSwitch"
      @initComi="initComiFn"
    />
    <!-- 对话 -->
    <Dialogue
      v-show="(showDialogue || state.historyModel) && !showSquare"
      :isChat="state.isChat"
      :onLoadMore="loadHistoryData"
      :hasMore="hasMore"
      ref="dialogueRef"
    />
    <!-- <VoiceCard></VoiceCard> -->
    <!-- 智能体广场 -->
    <Square v-show="showSquare" :showSquare="showSquare" />
  </div>
</template>

<script setup lang="ts">
import AiHeader from '@/components/ai-header/index.vue';
import Square from '@/components/ai-square/index.vue';
import API from '@/api/index';
import Dialogue from '@/components/ai-dialogue/index.vue';
import { getSystemUI, initSpeech, initWebPageParams, setHistorySessionID, showOrHideInput, setSystemUI } from '@/plugins/app-plugin';
import {
  getQueryString,
  getDeviceInfo,
  getAssistantId,
  getPagePaddingTopStyle,
  setLocalStorage
} from '@/utils/common';
import utils from '@/utils';
import { HistoryService } from '@/api/servers/history-service';

import { ref, reactive, computed, onMounted, onBeforeUnmount, provide, watch, nextTick } from 'vue';
import { AGENT, DIALOGUE } from '@/const/const';
import Voice from '@/api/voice';
import { AssistantInfoType } from '@/types';
import { useVueInstance } from '@/utils/vueInstance';


/**
 * ------------页面状态模块------------
 */

// 是否显示初始化背景图片,显示条件：新开对话或者没有历史记录的时候，并且不是对话页面时显示 只有对话页面，才展示背景图，智能体和单个助手页面以及搜索页面不展示背景图
const showDialogue = computed(function () {
    return API.store.state.currentHeaderKey === DIALOGUE;
});

// 是否显示智能体广场
const showSquare = computed(() => {
    return API.store.state.currentHeaderKey === AGENT && !state.single;
});

// 是否是开始发模式
const isDev = process.env.NODE_ENV === 'development';

/**
 * ------------数据模块------------
 */
// 初始化 state
let state = API.store.state;

// 代理一个 vue 实例
const proxy = useVueInstance();

// 设置是对话页面还是历史记录页面
API.store.action.setState('isChat', state.historyModel);



/**
 * ------------智能体以及用户信息模块------------
 */

// 获取 assistantId
let assistantId = getAssistantId(); 

// 助手信息
let assistantInfo = reactive<AssistantInfoType>({
  name: '',
  iconUrl: '',
  introduce: '',
  prologue: '',
  guideQuestions: [],
  id: '',
  code: '',
});

// 提供助手信息
provide('assistantInfo', assistantInfo);

// 判断是否单智能体
if (state.single) {
  let params = getQueryString('params');
  if (params) {
    params = JSON.parse(params);
  }
  Object.assign(assistantInfo, params);
}
/**
 * 初始化Comi智能体
 */
const initComiFn = () => {
  initComiData();
}
/**
 * 初始化Comi智能体
 */
const initComiData = () => {
  API.requests.getgGeneralAsInfo().then(res => {
    if (Number(res?.code) === 0 && res?.data) {
      setAssistantInfo(res.data);
    }
  });
  setWebviewBg();
}
// 获取助手信息
const getAssistantIdFn = () => {
  if (isApp || isDev) {
    if (assistantId) {
      API.requests.getAssistantInfo(assistantId).then(res => {
        if (Number(res?.code) === 0 && res?.data) {
          setAssistantInfo(res.data);
        }
      });
    } else {
      initComiData();
    }
  }
}
// 设置助手信息
const setAssistantInfo = (data: AssistantInfoType) => {
  const { introduce, name, iconUrl, prologue, prologuePreQuestions, code, id } = data;
  const aiSessionIdKey = `aiSessionId_${id}`;
  
  // 使用 Object.assign 确保响应式更新
  Object.assign(assistantInfo, {
    name,
    iconUrl,
    introduce,
    prologue,
    guideQuestions: prologuePreQuestions
      ? prologuePreQuestions?.length > 3
        ? prologuePreQuestions.slice(0, 3)
        : prologuePreQuestions
      : [],
    id,
    code
  });

  API.store.action.setState('assistantInfo', assistantInfo);
  sessionStorage.setItem('assistantId', id);
  API.store.action.setStaticData('assistantId', id);
  API.store.action.setStaticData('aiSessionIdKey', aiSessionIdKey);
  const params = {
    assistantId: id,
    chatSessionId: API.store.state.chatSessionId,
    sessionId: ''
  };
  initWebPageParams(params);
}
//获取个人设置的信息
const getPersonalSetting = () => {
  API.requests.getPersonalSetting().then(res => {
    if(Number(res?.code) === 0) {
      const data = res.data;
      if(typeof data.recommend_question === 'undefined') {
        data.recommend_question = '1';
      }
      setLocalStorage('personalSetting', data);
    }
  })
}

 /**
 * ------------历史数据模块------------
 */
// 初始化历史数据服务
const historyService = HistoryService.getInstance(state);
// 是否还有更多数据
const hasMore = computed(() => historyService.hasMoreData);
// 对话组件实例 
const dialogueRef = ref<InstanceType<typeof Dialogue> | null>(null);
// 加载历史数据
const loadHistoryData = async (isLoadMore = false, fromDigital = false) => {
  // 如果不是加载更多，则重置页码
  if (!isLoadMore) {
    historyService.reset(fromDigital);
  }
  await historyService.loadHistoryData(isLoadMore);
};

/**
 * ------------APP 相关模块------------
 */
// 语音播报控制态
let openSwitch = ref(false);
// 是否是APP
const { isApp } = getDeviceInfo();
// 根据 APP 和 非 APP 设置不同的样式
let wrapStyle = reactive<any>({
  paddingBottom: '0px',
  background: isApp ? 'transparent' : '#edf2fc',
  ...getPagePaddingTopStyle(),
});
// 初始化语音插件，避免第一次无法播放
initSpeech((data:any)=>{
  API.store.action.setStaticData('hasVoiceKey', true);
}, (err:any)=>{
  API.store.action.setStaticData('hasVoiceKey', false);
});

getSystemUI('windowMargin', setPageSize);

/**
 * 开启语音播报
 * @param {*} data true/false
 */
 const changeSwitch = (data: boolean) => {
  API.store.action.setState('isOpenVoice', data);
  let voiceContent = '已开启语音播报';
  if (!data) {
    voiceContent = '已关闭语音播报';
  }
  proxy?.$aiToast({
    content: voiceContent,
    // positionTop: '100px',
    timer: 1500,
  });
  openSwitch.value = data;
  //如果卡片上的操作正在播放并且是打开操作，则不执行stop
  if (API.store.staticData.longTouchCard?.data.isPlay && data) {
    return;
  }
  Voice.Play.stop();
}
/**
 * ------------APP事件监听------------
 */

/**
 * 监听原生历史记录点击操作
 * @param {*} data 
 * @param {*} calllback 
 */
 const quickHistoryCommands = (data:string, calllback:Function) => {
  const receiveData = JSON.parse(data);
  if(receiveData.action === 'open'){
    refreshPageData(receiveData);
    setHistorySessionID({
      historySessionId: receiveData.id
    });
    if(API.store.state.currentHeaderKey === DIALOGUE){
      setSystemUI({ webviewBg: 'webviewLight', webviewBgRgb: '#EDF2FC' }, 'setWebviewBg');
    }
  }else{
    if(state.historySessionId && (receiveData.id === state.historySessionId)){
      setHistorySessionID({
        historySessionId: receiveData.id
      });
      
      API.store.action.setState('isChat', false);
      API.store.action.setState('historySessionId', '');
      API.store.action.setState('historyModel', false);
      API.store.action.setState('single', false);
      sessionStorage.setItem('assistantId', '');
      API.store.action.setState('isNewView', false);
      API.action.clean();
      setWebviewBg();
      // 获取助手信息
      getAssistantIdFn();
      // 获取个人设置
      getPersonalSetting();
    }
  }
  calllback();
}

/**
 * ------------页面初始化模块------------
 */
// 监听 historySessionId 的变化
const pullHistorySessionId = async (fromDigital?:boolean) => {
  API.store.action.clearAllData();
  API.store.action.setState('isScrolling', true);
  await loadHistoryData(false, fromDigital);
}

// 设置webview背景
const setWebviewBg = () => {
  if (API.store.state.single) {
    setSystemUI({ webviewBg: 'webviewLight', webviewBgRgb: '#EDF2FC' }, 'setWebviewBg');
  } else {
    if(API.store.state.historyModel){
      if(API.store.state.currentHeaderKey === DIALOGUE){
        setSystemUI({ webviewBg: 'webviewDark', webviewBgRgb: '#EDF2FC' }, 'setWebviewBg');
      }else{
        setSystemUI({ webviewBg: 'webviewLight', webviewBgRgb: '#EDF2FC' }, 'setWebviewBg');
      }
    }else{
      setSystemUI({ webviewBg: 'webviewDark', webviewBgRgb: '#EDF2FC' }, 'setWebviewBg');
    }
  }
}


// 更新token
utils.getNewToken((token: string) => {
  API.store.action.setState('token', token);
  // 设置webview背景
  setWebviewBg();
  // 获取助手信息
  getAssistantIdFn();
  // 获取个人设置
  getPersonalSetting();
  // 确保 store 初始化完成后再加载历史数据
  const historySessionId = API.store.state.historySessionId;
  if (historySessionId) {
    sessionStorage.setItem(`aiSessionId_${historySessionId}`, historySessionId)
    pullHistorySessionId();
  }
},true);

// 刷新页面数据
 const refreshPageData = (data:any) => {
  const fromDigital = data.from === 'digital'; //是否来自数字人挂断请求
  API.store.action.setState('isChat', true);
  API.store.action.setState('historySessionId', data.id); 
  sessionStorage.setItem(`aiSessionId_${data?.assistantId}`, data.id)
  API.store.action.setState('historyModel', true);
  API.store.action.setState('single', data.superFlag !== '1');
  API.store.action.setState('isNewView', data.superFlag !== '1');
  if(data.superFlag === '1'){
    API.store.action.setState('currentHeaderKey',DIALOGUE);
  }
  showOrHideInput({
    visible: true
  });
  setWebviewBg();
  getSystemUI('windowMargin', setPageSize);
  assistantId = data.assistantId;
  // 获取助手信息
  getAssistantIdFn();
  // 获取历史数据
  pullHistorySessionId(fromDigital);
}


function setPageSize(data: any) {
  const info = JSON.parse(data);
  API.store.state.systemStyle.top = info.top;
  API.store.state.systemStyle.bottom = info.bottom;
  wrapStyle.paddingTop = info.top + 'px';
  wrapStyle.paddingBottom = info.bottom + 'px';
}
//移动端打电话需要的参数
function sendCallParams(params:string, callback?:Function) {
  // const callParams = JSON.parse(params);
  API.store.action.setState('selectedAgentInfo', null);
  let streamParams = API.action.initParams('');
  streamParams = {
    ...streamParams,
    citations: []
  }
  console.log(streamParams);
  callback && callback(streamParams);
  // initWebPageParams(streamParams);
}
//处理挂断电话的逻辑
function dealOnCallEnded(data: any) {
  const params = JSON.parse(data);
  const assistantId = getAssistantId();
  const refreshData = {
    id: params.sessionId,
    superFlag: API.store.state.single ? '0' : '1',
    isNewView: false,
    assistantId: assistantId,
    from: 'digital'
  }
  refreshPageData(refreshData)
}

// 页面挂载
onMounted(() => {
  //  监听tab页签，如果是智能体，则不设置paddingBottom
  watch(
    () => state.currentHeaderKey,
    newVal => {
      if (newVal === AGENT) {
        wrapStyle.paddingBottom = '0px';
      } else {
        wrapStyle.paddingBottom = state.systemStyle.bottom + 'px';
      }
    }
  );
  ZYJSBridge.setEventListener('sendHistoryItemData', quickHistoryCommands);
  ZYJSBridge.setEventListener('setPageSize', setPageSize);
  ZYJSBridge.setEventListener('clearComi',()=>{
    API.action.clean();
    setHistorySessionID({
        historySessionId: ''
      });
  })
  //点击移动端打电话需要用到，传递参数给移动端
  ZYJSBridge.setEventListener('fetchParamsFromWeb', sendCallParams);
  ZYJSBridge.setEventListener('onCallEnded', dealOnCallEnded);
});

// 移除监听
onBeforeUnmount(() => {
  ZYJSBridge.removeEventListener('sendHistoryItemData', quickHistoryCommands);
  ZYJSBridge.removeEventListener('clearComi',()=>{})
  ZYJSBridge.removeEventListener('setPageSize', setPageSize);
  ZYJSBridge.removeEventListener('fetchParamsFromWeb', sendCallParams);
  ZYJSBridge.removeEventListener('onCallEnded', dealOnCallEnded);
});
</script>

<style lang="scss" scoped>
#homePage {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  height: 100vh;
  display: flex;
  flex-direction: column;
  line-height: 20px;
  // line-height: var(--theme-line-height0, 20px);
  box-sizing: border-box;
  user-select: none;
  -webkit-user-select: none;
  // padding: 0 12px;
  .padding-setting {
    padding-left: 12px;
    padding-right: 12px;
  }

  .dialogue {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .ai-chat-container {
    height: auto;
    flex: 1;
    overflow-y: auto;
    overflow: hidden;
  }
  > .footer {
    height: auto;
    margin-bottom: 8px;
    flex: 0;
  }
  .ai-request-container {
    margin-bottom: 16px;
  }
}

body #homePage {
  padding-top: 44px;
}
</style>
<style>
body {
  overflow: hidden;
}
::-webkit-scrollbar {
  display: none;
}
body.cmp-iphone {
  height: 100vh;
}
</style>