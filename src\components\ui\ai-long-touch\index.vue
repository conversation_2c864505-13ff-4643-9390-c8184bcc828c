<template>
  <Transition name="long-touch" appear @afterLeave="onAfterLeave">
    <div v-if="renderArray.length && showLongTouch && needNotClose" class="long-touch touch-fead-in" :style="topDistance.top">
      <div class="operate-container" ref="operateContainer" :style="topDistance.left">
        <div class="operate-button"  v-for="operate in renderArray" :key="operate.id" @click="buttonClick(operate)">
          <img class="image" v-if="operate.type === 'image'" :src="imageSrcObj[operate.icon]" />
          <div v-else class="iconfont icon-size" :class="operate.icon"></div>
          <div class="text">{{operate.name}}</div>
        </div>
      </div>
      <div class="triangle-common" :class="[ topFlag ? 'triangle':'triangle-top']" :style="leftDistance"></div>
    </div>
  </Transition>
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue';
  import store from '@/api/store';
  import playWhite from '@/assets/images/play-white.gif';
import { OperateItemType } from '@/types';
  const _$emit = defineEmits(['clickOperate', 'update:isShowAction']);
  const props = defineProps({
    positionInfo: {
      type: Object,
      default: () => ({
        x: 0,
        y: 0
      })
    },
    operateArray: {
      type: Array as () => OperateItemType[],
      default: () => ([])
    },
    cardData: {
      type: Object,
      default: () => ({})
    },
    needNotClose: {
      type: Boolean,
      default: true
    }
  });
  console.log(props.needNotClose, "needNotClose")
  let topFlag = ref(false);
  let operateContainer = ref<HTMLElement | null>(null);
  //容器宽度
  let windowInnerWidth = window.innerWidth;
  const imageSrcObj: Record<string, string> = {
    reading: playWhite
  }
  let showLongTouch = ref(true);
  const topDistance = computed(()=>{
    //减去内容区的高度和三角形的高度再预留10px的距离
    const distance = props.positionInfo.y - 62 - 16 - 10;
    let top = props.positionInfo.y - 62 - 16 - 10 - store.state.systemStyle.top;
    //左右边距为12
    let paddingDistance = 12;
    topFlag.value = top > 0;
    if(top < 0) {
      top = props.positionInfo.y + 18;
    }else {
      top = distance;
    }
    const containerWidth = operateContainer.value?.offsetWidth || 0;
    let left = props.positionInfo.x - containerWidth / 2 + 8; //8为三角形的宽度
    left = Math.max(left, paddingDistance);
    if(left + containerWidth + paddingDistance > windowInnerWidth){
      left = windowInnerWidth - containerWidth - paddingDistance;
    }
    return {
      top: `top: ${top}px`,
      left: `left: ${left}px`
    };
  });
  const leftDistance = computed(()=>{
    const left = props.positionInfo.x;
    return `left: ${left}px`;
  });

  const renderArray = computed(()=>{
    return props.operateArray.map((item: OperateItemType)=>{
      if(item.id === 'read') {
        return {
          ...item,
          icon: props.cardData.data.isPlay ? 'reading' : 'ai-icon-bobao-xianbeifen',
          name: props.cardData.data.isPlay ? '停止朗读' : '朗读',
          type: props.cardData.data.isPlay ? 'image' : 'icon'
        }
      }else {
        return item;
      }
    })
  });
  function buttonClick(operate:OperateItemType ) {
    showLongTouch.value = false;
    _$emit('clickOperate', operate);
   
  }
  function onAfterLeave() {
     _$emit('update:isShowAction', false);
  }

</script>

<style lang="scss" scoped>
  .long-touch-enter-from, .long-touch-leave-to {
    opacity: 0;
  }
  .long-touch-enter-active, .long-touch-leave-active{
    transition: opacity 0.2s ease-in-out;
  }
  .long-touch {
    position: absolute;
    z-index: 9999;
    width: 100%;
    left: 0;
    .operate-container {
      position: relative;
      display: inline-flex;
      background-color: #4C4C4C;
      color: rgba(255, 255, 255, 1);
      color: var(--theme-font-color5,rgba(255, 255, 255, 1));
      padding: 8px;
      border-radius: 8px;
      top: 0;
      // left: 50%;
      // transform: translateX(-50%);
      .operate-button {
        flex: 1;
        flex-direction: column;
        display: flex;
        align-items: center;
        width: 48px;
        margin-right: 16px;
        &:last-child {
          margin-right: 0;
        }
        .icon-size {
          font-size: 24px;
          line-height: 24px;
        }
        .text {
          margin-top: 2px;
          font-size: 12px;
        }
        .image {
          height: 24px;
        }
      }
    }

    .triangle-common {
      position: absolute;
      height: 0;
      width: 0;
      border: 8px solid transparent;
    }
    .triangle {
      bottom: -15px;
      border-top: 8px solid #4C4C4C;
    }
    .triangle-top {
      top: -15px;
      border-bottom: 8px solid #4C4C4C;
    }
  }
</style>