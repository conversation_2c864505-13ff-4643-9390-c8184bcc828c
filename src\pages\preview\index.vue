<template>
  <div class="preview-container">
    <component :computedStyle="computedStyle" :is="componentName" isPreview :content="previewData" ></component>
  </div>
</template>

<script setup lang="ts">
import { getLocalStorage, getQueryString, setPagePadding } from '@/utils/common';
import DataChartPreview from '@/components/data-chart/data-preview.vue';
import DataTablePreview from '@/components/data-table/data-preview.vue';
import { ref, computed, provide,onMounted, onBeforeUnmount } from 'vue';
import "@seeyon/seeyon-comi-plugins-library/dist/seeyon-comi-plugins-library.css";
import { paddingType } from '@/types/api';
const contentHeight = ref('100%');
provide('contentHeight', contentHeight);

const previewType = ref('');
const computedStyle = ref<paddingType>({})
// 获取缓存的预览数据
const type = getQueryString('type');
previewType.value = type;
const layoutHeaderHeight = getQueryString('layoutHeaderHeight');

contentHeight.value = `${(window.innerHeight - (Number(layoutHeaderHeight) + 64))}`; // 64为头部高度44+设置距离底部高度
const previewData = getLocalStorage(type);

const componentName = computed(() => {
  switch (previewType.value) {
    case 'table':
      return DataTablePreview;
    case 'chart':
      return DataChartPreview;
    default:
  }
});
onMounted(() => {
  ZYJSBridge.setEventListener('setPageSize', setPageSize);
});
function setPageSize(data: any) {
  computedStyle.value = setPagePadding(data);
  contentHeight.value = `${(window.innerHeight - (Number(computedStyle.value?.paddingTop) + 64))}`;
}
onBeforeUnmount(()=>{
  ZYJSBridge.removeEventListener('setPageSize', setPageSize);
})
</script>

<style lang="scss" scoped>
.preview-container {
  box-sizing: border-box;
  height: 100%;
  width: 100%;
}
@media screen and (width: 768px) {
}
</style>
<style>
#app {
  height: 100%;
  width: 100%;
}
</style>
