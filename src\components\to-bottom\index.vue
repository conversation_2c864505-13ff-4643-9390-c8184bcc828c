<template>
  <div @click="toBottom" class="to-bottom">
    <!-- icon占位 -->
    <span class="iconfont ai-icon-double-arrow-down icon"></span>
  </div>
</template>

<script lang="ts" setup>
import { defineEmits } from 'vue';
const emit = defineEmits(['toBottom']);
function toBottom() {
  emit('toBottom');
}
</script>

<style lang="scss" scoped>
.to-bottom {
  height: 32px;
  width: 32px;
  background-color: #fff;
  border-radius: 50%;
  box-shadow: 0px 1px 12px 0px rgba(92, 108, 119, 0.2);
  text-align: center;
  line-height: 32px;
  .icon {
    height: 14px;
    width: 14px;
    border-radius: 50%;
    vertical-align: middle;
    color: #86aeff;
    color: var(--theme-color5, #86aeff);
  }
}
@keyframes fadeInScale {
  0% {
    opacity: 0;
    transform: scale(0.5);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.to-bottom {
  animation: fadeInScale 0.3s ease forwards;
}
</style>
