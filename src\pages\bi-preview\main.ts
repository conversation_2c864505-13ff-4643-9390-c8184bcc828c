import { createApp, defineAsyncComponent } from 'vue';
import App from './index.vue';
import '../../assets/base.css';
import '@/assets/icon/iconfont.css';
import API from '@/api';

API.init();


const app = createApp(App);
app.use(API);


// 引入远程组件
const ComiExternalBiCardDialog = defineAsyncComponent(() => import("remote_app/ComiExternalBiCardDialog"));

// 注册远程组件
app.component("ComiExternalBiCardDialog", ComiExternalBiCardDialog);




// 本地启动参数
app.mount('#app');
