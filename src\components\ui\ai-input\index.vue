<template>
  <div class="ai-input-wrapper">
    <input
      ref="inputRef"
      :class="inputClasses"
      :value="modelValue"
      :placeholder="placeholder"
      :disabled="disabled"
      :type="type"
      @input="handleInput"
      @focus="handleFocus"
      @blur="handleBlur"
      @keydown="handleKeydown"
    />
    <span v-if="showClear" class="ai-input-clear-icon" @click="handleClear">
      <svg
        viewBox="64 64 896 896"
        focusable="false"
        width="1em"
        height="1em"
        fill="currentColor"
        aria-hidden="true"
      >
        <path
          d="M563.8 512l262.5-312.9c4.4-5.2.7-13.1-6.1-13.1h-79.8c-4.7 0-9.2 2.1-12.3 5.7L511.6 449.8 295.1 191.7c-3-3.6-7.5-5.7-12.3-5.7H203c-6.8 0-10.5 7.9-6.1 13.1L459.4 512 196.9 824.9A7.95 7.95 0 00203 838h79.8c4.7 0 9.2-2.1 12.3-5.7l216.5-258.1 216.5 258.1c3 3.6 7.5 5.7 12.3 5.7h79.8c6.8 0 10.5-7.9 6.1-13.1L563.8 512z"
        ></path>
      </svg>
    </span>
    <span v-if="showCount" class="ai-input-count">
      {{ countDisplay }}
    </span>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';

const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: '',
  },
  placeholder: {
    type: String,
    default: '',
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  size: {
    type: String,
    default: 'middle', // 'large' | 'middle' | 'small'
    validator: (value: string) => ['large', 'middle', 'small'].includes(value),
  },
  type: {
    type: String,
    default: 'text',
  },
  showCount: {
    type: Boolean,
    default: false,
  },
  maxLength: {
    type: Number,
    default: null,
  },
  allowClear: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['update:modelValue', 'focus', 'blur', 'pressEnter', 'clear']);

const inputRef = ref<HTMLInputElement | null>(null);
const isFocused = ref<boolean>(false);

const inputClasses = computed(() => [
  'ai-input',
  `ai-input-${props.size}`,
  {
    'ai-input-disabled': props.disabled,
    'ai-input-focused': isFocused.value,
  },
]);

const countDisplay = computed(() => {
  if (!props.showCount) return '';
  const length = String(props.modelValue).length;
  return props.maxLength ? `${length}/${props.maxLength}` : length;
});

const showClear = computed(() => {
  return props.allowClear && !props.disabled && props.modelValue;
});

function handleClear() {
  clear();
  focus();
}

function handleInput(event) {
  let value = event.target.value;
  if (props.maxLength && value.length > props.maxLength) {
    value = value.slice(0, props.maxLength);
    if (inputRef.value) {
      inputRef.value.value = value;
    }
  }
  emit('update:modelValue', value);
}

function handleFocus(event) {
  isFocused.value = true;
  emit('focus', event);
}

function handleBlur(event) {
  isFocused.value = false;
  emit('blur', event);
}

function handleKeydown(event) {
  if (event.key === 'Enter') {
    emit('pressEnter', event);
  }
}

function focus() {
  if (inputRef.value) inputRef.value.focus();
}

function blur() {
  if (inputRef.value) inputRef.value.blur();
}

function clear() {
  emit('update:modelValue', '');
  emit('clear');
}

defineExpose({
  focus,
  blur,
  clear,
});
</script>

<style scoped>
.ai-input-wrapper {
  position: relative;
  display: inline-block;
  width: 100%;
}

.ai-input {
  box-sizing: border-box;
  margin: 0;
  padding: 4px 11px;
  color: rgba(0, 0, 0, 0.88);
  font-size: 14px;
  line-height: 1.5715;
  list-style: none;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
    sans-serif;
  position: relative;
  display: inline-block;
  width: 100%;
  min-width: 0;
  background-color: #ffffff;
  background-image: none;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  transition: all 0.2s;
}

/* .ai-input:hover {
  border-color: #1890ff;
} */
/* .ai-input:focus-visible:hover {
  border-color: #1890ff;
} */

.ai-input:focus {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(5, 145, 255, 0.1);
  outline: 0;
}

.ai-input-disabled {
  color: rgba(0, 0, 0, 0.25);
  background-color: rgba(0, 0, 0, 0.04);
  border-color: #d9d9d9;
  cursor: not-allowed;
  opacity: 1;
}

.ai-input-disabled:hover {
  border-color: #d9d9d9;
}

.ai-input-large {
  padding: 6.5px 11px;
  font-size: 16px;
}

.ai-input-small {
  padding: 0px 7px;
  font-size: 12px;
}

.ai-input-count {
  position: absolute;
  right: 11px;
  bottom: 4px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
  background: #fff;
  pointer-events: none;
}
.ai-input-clear-icon {
  position: absolute;
  right: 11px;
  top: 50%;
  transform: translateY(-50%);
  color: rgba(0, 0, 0, 0.25);
  cursor: pointer;
  transition: color 0.3s;
}

.ai-input-clear-icon:hover {
  color: rgba(0, 0, 0, 0.45);
}

.ai-input-wrapper:hover .ai-input-clear-icon {
  display: block;
}

.ai-input-count {
  right: 30px;
}
</style>
