// 生成uuId, 只有初次执行
const hexList: string[] = [];
for (let i = 0; i <= 15; i++) {
  hexList[i] = i.toString(16);
}

/**
 * 计算字符长度
 * @param {string} str
 * @returns
 */
export const calculateCharLength = (str: string) => {
  let length = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charAt(i);
    if (/[\u4e00-\u9fa5]/.test(char)) {
      length += 1; // 汉字算1个字符
    } else {
      length += 0.5; // 其他字符2个算1个字符
    }
  }
  return length;
};
/**
 * 获取各个agent的ID
 * @param
 * @returns
 */
export const getAssistantId = function () {
  return getQueryString('assistantId') || sessionStorage.getItem('assistantId'); // 2048555557967186661 超级助理CoMi
};
/**
 * 给助手头像加上/
 * @param
 * @returns
 */
export const getAgentIconUrl = (agentInfo: any) => {
  let iconUrl = '';
  if(agentInfo?.iconUrl) {
    if(agentInfo.iconUrl.startsWith('/')) {
      iconUrl = agentInfo.iconUrl;
    }else {
      iconUrl =  '/' + agentInfo.iconUrl;
    }
  }
  return iconUrl;
}
/**
 * 获取url上指定名称的参数值
 * @param {string} name
 * @returns
 */
export const getQueryString = (name: string) => {
  var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)');
  var r = window.location.search.substr(1).match(reg);
  if (r != null) return decodeURIComponent(r[2]);
  return '';
};

/**
 * 时间计算规则
 * @param {number} dateTime 时间戳
 * @returns
 */
export const customFormat = (dateTime: number) => {
  if (!dateTime) return '';
  const chineseDay = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
  const englishDay = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
  const dateTimeDay = new Date(dateTime).getDay();
  const {
    dateYear,
    dateMonth,
    dateDay,
    dateHours,
    dateMinutes
  } = formatDate(dateTime)
  const nowDate = new Date();
  //判断是不是今天的时间
  const todayStart = nowDate.setHours(0, 0, 0, 0);
  const todayEnd = nowDate.setHours(23, 59, 59, 999);
  //是不是本周的时间， 周一 - 周日
  const dayToday = nowDate.getDay();
  const oneDateTime = 24 * 60 * 60 * 1000;
  const subTime = dayToday === 0 ? 6 : dayToday - 1;
  const endDay = dayToday === 0 ? 7 : dayToday; //
  const thisWeekStart = new Date(nowDate.getTime() - subTime * oneDateTime).setHours(0, 0, 0, 0);
  const thisWeekEnd = new Date(nowDate.getTime() + (7 - endDay) * oneDateTime).setHours(
    23,
    59,
    59,
    999
  );
  //是不是本年的时间
  const thisYear = new Date().getFullYear();
  //今天
  if (todayStart < dateTime && dateTime < todayEnd) {
    return `${dateHours} : ${dateMinutes}`;
  }
  //本周
  if (thisWeekStart < dateTime && dateTime < thisWeekEnd) {
    return `${chineseDay[dateTimeDay]} ${dateHours}:${dateMinutes}`;
  }
  //是不是本年
  if (dateYear === thisYear) {
    return `${dateMonth}月${dateDay}日 ${dateHours}:${dateMinutes}`;
  } else {
    return `${dateYear}年${dateMonth}月${dateDay}日 ${dateHours}:${dateMinutes}`;
  }
};
export const formatDate = (dateTime: number) => { 
  const receiveDate = new Date(dateTime);
  const dateYear = receiveDate.getFullYear();
  const dateMonth = receiveDate.getMonth() + 1;
  const dateDay = receiveDate.getDate();
  const hours = receiveDate.getHours();
  const dateHours = hours < 10 ? '0' + hours : hours;
  const minutes = receiveDate.getMinutes();
  const dateMinutes = minutes < 10 ? '0' + minutes : minutes;
  return {
    formatResult: `${dateYear}-${dateMonth}-${dateDay}`,
    dateYear,
    dateMonth,
    dateDay,
    dateHours,
    dateMinutes,
  };
};
/**
 * 防抖函数
 * @param {function} fn
 * @param {number} delay
 */
export const debounce = (fn: Function, delay: number = 500) => {
  let timeout: null | number = null;
  return function (this: any, ...args: any[]) {
    if (timeout !== null) {
      clearTimeout(timeout);
    }
    timeout = window.setTimeout(() => {
      fn.apply(this, args);
    }, delay);
  };
};

/**
 * 按照规则生成UUID
 * @returns
 */
export const buildUUID = () => {
  let uuid = '';
  for (let i = 1; i <= 36; i++) {
    if (i === 9 || i === 14 || i === 19 || i === 24) {
      uuid += '-';
    } else if (i === 15) {
      uuid += 4;
    } else if (i === 20) {
      uuid += hexList[(Math.random() * 4) | 8];
    } else {
      uuid += hexList[(Math.random() * 16) | 0];
    }
  }
  return uuid.replace(/-/g, '');
};

/**
 * 判断是否在app内
 * @returns
 */
export const getDeviceInfo = () => {
  const userAgent = navigator.userAgent.toLowerCase();
  let isApp = false;
  let device = '';
  if (/comishell\/android/i.test(userAgent)) {
    isApp = true;
    device = 'Android';
  } else if (/comishell\/ios/i.test(userAgent)) {
    isApp = true;
    device = 'iOS';
  }
  return {
    isApp,
    device,
  };
};

/**
 * 设置localStorage
 * @returns
 */
export const setLocalStorage = (key: string, value: any) => {
  localStorage.setItem(key, JSON.stringify(value));
};

/**
 * 获取localStorage
 * @returns
 */
export const getLocalStorage = (key: string) => {
  const value = localStorage.getItem(key);
  if (value) {
    return JSON.parse(value);
  }
  return null;
};

/**
 * 移除localStorage
 * @returns
 */
export const removeLocalStorage = (key: string) => {
  localStorage.removeItem(key);
};

function isObject(item: any) {
  return item && typeof item === 'object' && !Array.isArray(item);
}
/**
 * 深拷贝
 * @returns
 */
export function mergeDeep(target: any, source: any) {
  if (!isObject(target) || !isObject(source)) {
    return target;
  }

  const output = { ...target };

  if (isObject(target) && isObject(source)) {
    Object.keys(source).forEach(key => {
      if (isObject(source[key])) {
        if (!(key in target)) {
          output[key] = source[key];
        } else {
          output[key] = mergeDeep({ ...target[key] }, source[key]);
        }
      } else {
        output[key] = source[key];
      }
    });
  }

  return output;
}
/**
 * 获取页面的paddingTop样式
 * @returns 
 */
export const getPagePaddingTopStyle = () => {
  return {
    paddingTop: (getQueryString('layoutHeaderHeight') || 0) + 'px',
    boxSizing: 'border-box',
  };
};
/**
 * 设置页面的padding样式
 * @param info
 */
export const setPagePadding  = (data:string) => {
  const info = JSON.parse(data);
  return {
    paddingTop: (info.top + 5) + 'px',
    paddingBottom: (info.bottom) + 'px',
    paddingLeft: (info.left) + 'px',
    paddingRight: (info.right) + 'px',
  };
};
