
import fetchStream from "./fetch-stream";
import store from '../store';
import { setInputState } from '@/plugins/app-plugin';
import type { CardDataType, StreamFnInstance } from "@/types/api";
type StreamManagerType = {
  streams: StreamFnInstance[],
  bindObj: any,
  create: (card: CardDataType, option?: any) => any,
  stopAllStreams: () => void,
  remove: (steam: StreamFnInstance) => void,
  bind: (card: CardDataType, stream: StreamFnInstance) => void,
  runBindStream: (card: CardDataType) => void,
}
let StreamManager = {} as StreamManagerType;
StreamManager.streams = [];
StreamManager.bindObj = {};
// 创建基础stream，后续都走通用
StreamManager.create = (card) => {
  store.action.setState('isLoading', true);
  const stream = new fetchStream(card);
  StreamManager.streams.push(stream);
  return stream;
};

// 停止
StreamManager.stopAllStreams = () => {
  store.action.setState('isLoading', false);
  StreamManager.streams.forEach((stream: StreamFnInstance) => {
    stream.stop();
  })
  StreamManager.streams = [];
}

// 移除
StreamManager.remove = (steam) => {
  // 移除steam
  StreamManager.streams = StreamManager.streams.filter(el => el._id != steam._id);
  // 全部移除完时，需要设置loading为false
  if (!StreamManager.streams.length) {
    // 恢复原生输入框状态
    const params = {
      canInput: true,
    };
    setInputState(params);
    store.action.setState('isLoading', false);
  }
}
StreamManager.bind = (card, stream) => {
  if (card._id)
    StreamManager.bindObj[card._id] = stream;
};
StreamManager.runBindStream = (card) => {
  if (card._id) {
    const stream = StreamManager.bindObj[card._id];
    if (stream) {
      stream.run();
    }
  }

};
export default StreamManager;