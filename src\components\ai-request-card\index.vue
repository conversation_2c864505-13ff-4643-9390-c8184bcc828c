<template>
  <div class="request-card-container">
    <AttachmentInfo class="attachment-setting" v-if="staticData.citations" :citations="staticData.citations">
    </AttachmentInfo>
    <div class="ai-request-card">
      <div v-if="data.isLoading" class="state">
        <AiStateLoading :iconStyle="iconStyle" />
      </div>
      <div v-if="data.isError" class="state">
        <AiStateFail @failureClick="onFailureReload" />
      </div>
      <div class="content">
        <div v-if="staticData.message" >{{staticData.message}}</div>
      </div>

      <!-- <VoiceCard class="voice-state" :staticData="testStaticData"></VoiceCard> -->
    </div>
    <!-- <div class="text-card">
      研发中心3月考勤研发中心3月考勤研发中心3月考勤发中心3月考勤发中心3月考勤发中心3月考勤发中心3月考勤发中心3月考勤发中心3月考勤发中心3月考勤
    </div> -->
  </div>
</template>

<script setup lang="ts">
import AiStateLoading from '../ui/ai-state/loading.vue';
import AiStateFail from '../ui/ai-state/failure.vue';
import StreamManager from '@/api/stream/manager';
import AttachmentInfo from './attachment-info.vue'
// import ShowTimeCard from "@/components/show-time-card/index.vue";
import VoiceCard from './voice-card.vue';
import { CardDataType } from '@/types/api';

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
  content: {
    type: Object as () => CardDataType,
    default: () => ({}),
  },
  staticData: {
    type: Object,
    default: () => ({}),
  },
});
const testStaticData = {
  ...props.staticData,
  timer: 60
}
import sending from '@/assets/images/<EMAIL>';
const iconStyle = {
  'background-image' : `url(${sending})`
}
function onFailureReload() {
  StreamManager.runBindStream(props.content);
}
</script>

<style lang="scss" scoped>
.request-card-container {
  display: flex;
  align-items: flex-end;
  flex-direction: column;
  .attachment-setting {
    margin-bottom: 8px;
    max-width: 80%;
    overflow: hidden;
  }
  .ai-request-card {
    display: flex;
    flex-direction: row;
    color: #fff;
    color: var(--theme-font-color5, rgba(255, 255, 255, 1));
    justify-content: flex-end;
    align-items: center;
    user-select: auto;
    -webkit-user-select: auto;
    // max-width: 80%;
    width: 100%;
    .state {
      margin-right: 12px;
    }

    .content {
      border-radius: 12px 12px 2px 12px;
      padding: 8px 16px;
      background: linear-gradient(135deg, #5973fe, #4379ff);
      font-size: 16px;
      font-size: var(--theme-font-size2, 16px);
      line-height: 24px;
      line-height: var(--theme-line-height2, 24px);
      max-width: 80%;
      box-sizing: border-box;
      white-space: pre-wrap;
      word-break: break-all;
    }
  }
  .text-card {
    margin-top: 8px;
    padding: 8px 16px;
    border-radius: 12px;
    color: rgba(0, 0, 0, 1);
    color: var(--theme-font-color0, rgba(0, 0, 0, 1));
    background: #fff;
  }
  .voice-state {
    border-radius: 12px 12px 2px 12px;
    // padding: 8px 16px;
    // background: linear-gradient(135deg, #5973fe, #4379ff);
    // max-width: 80%;
    font-size: 16px;
    font-size: var(--theme-font-size2, 16px);
    line-height: 24px;
    line-height: var(--theme-line-height2, 24px);
  }
}
</style>
