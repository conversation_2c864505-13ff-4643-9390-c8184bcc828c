import { createApp, h, ref, type App, type Component } from 'vue';
import { AiDataCard } from '@/types/api';

import AiLongTouch from '@/components/ui/ai-long-touch/index.vue';
import { OperateItemType } from '@/types';
type positionInfo = {x: number, y: number};
type ButtonInfo = {
    name: string,
    id: string,
    icon: string
}
type Options = {
    operateArray: Array<OperateItemType>,
    positionInfo: positionInfo,
    cardData: AiDataCard,
    clickOperate: (operate: ButtonInfo)=> void
  }
export const longTouchAction = (options: Options) => {
  // 创建容器
  const container: HTMLElement = document.createElement('div');
  document.body.appendChild(container);
  //来自其他地方触发关闭
  let needNotClose = ref(true);
  // 创建 longTouchAction 组件实例
  const longActionApp:App<Element> = createApp({
    setup() {
      const isShowAction = ref(true);
      //确认回调
      const clickOperate = (operate: ButtonInfo) => {
        if (options.clickOperate) {
          options.clickOperate(operate);
        }
      };

      return {
        isShowAction,
        clickOperate,
      };
    },
    render() {
      return h(AiLongTouch as Component, {
        // isShowAction: this.isShowAction,
        needNotClose: needNotClose.value,
        operateArray: options.operateArray,
        positionInfo: options.positionInfo,
        cardData: options.cardData,
        onClickOperate: this.clickOperate,
        'onUpdate:isShowAction': (value:boolean) => {
          if (!value) {
            realDestory();
          }
          this.isShowAction = value;
        },
      });
    },
  });

  // 渲染 longTouchAction
  longActionApp.mount(container);

  function realDestory() {
    longActionApp.unmount();
    document.body.removeChild(container);
  }

  // 外部销毁 longTouchAction
  function destroy() {
    needNotClose.value = false;
    // console.log(isShowAction, "isShowAction");
    // longActionApp.unmount();
    // document.body.removeChild(container);
  }
  return {
    destroy,
  };
};

export default {
  longTouchAction,
  install(app:App) {
    // 全局挂载方法
    app.config.globalProperties.$longTouchAction = longTouchAction;
  },
};
