<template>
  <div class="message-list-item data-card-margin" :class="{'agent-info-margin': content.componentKey === 'AgentInfo'}" v-if="content" :id="content._id">
    <component :is="componentName" :content="content" :data="content.data" :staticData="content.staticData"></component>
  </div>
</template>
<script setup lang="ts">
import AiRequestCard from '@/components/ai-request-card/index.vue';
import AiDataCard from '@/components/ai-data-card/index.vue';
import HistoryConversation from '@/components/history-conversation/index.vue';
import AiStateLoading from '@/components/ui/ai-state/loading.vue';
import AiLoadingAbort from '@/components/ai-loading-abort/index.vue';
import AiShowTimeCard from '@/components/show-time-card/index.vue';
import DataName from "@/components/ai-data-card/data-name.vue";
import PersonalInfo from "@/components/personal-info/index.vue";
import { computed } from 'vue';
// import NetworkError from '@/components/network-error/index.vue';
const { content } = defineProps({
  content: {
    type: Object,
    default: () => ({}),
  },
});
const componentName = computed(() => {
  const componentKey = content.componentKey;
  switch (componentKey) {
    case 'AiRequestCard':
      return AiRequestCard;
    case 'AiDataCard':
      return AiDataCard;
    case 'HistoryConversation':
      return HistoryConversation;
    case 'AiStateLoading':
      return AiStateLoading;
    case 'AiLoadingAbort':
      return AiLoadingAbort
    case 'AiShowTimeCard':
      return AiShowTimeCard
    case 'AgentInfo': 
      return DataName
    case 'PersonalInfo':
      return PersonalInfo
    default:
    // return NetworkError;
  }
});
</script>

<style lang="scss" scoped>
  .data-card-margin {
    margin-bottom: 16px;
  }
  .agent-info-margin {
    margin-bottom: 8px;
  }
</style>
