<template>
  <div class="digital-dialogue">
    <!-- <div class="dialogue-container" v-if="allCardData.length" @scroll="dealMask">
      <MessageListItem 
        v-for="content in allCardData"
        :content="content"
        :key="content._id">
      </MessageListItem>
    </div> -->

    <div class="dialogue-container" v-if="state.allCardData.length" @scroll="dealMask" @touchstart="stopScroll">
      <MessageListItem 
        v-for="content in state.allCardData"
        :content="content"
        :key="content._id">
      </MessageListItem>
      <div ref="messageBoxBottom" id="messageBoxBottom"></div>
    </div>
    <div v-show="showTopBlur" class="top-blur"></div>
    <div v-show="showBottomBlur" class="bottom-blur"></div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, onMounted } from 'vue';
import { AiRequestCard,CardContentType, AiLoadingCard, AgentInfo, AiDataCard, FileType } from '@/types/api';
import API from '@/api';
import { handleStreamMessage } from '@/utils';
import MessageListItem from '@/components/layout/message-list-item.vue';
const state = API.store.state;
const allCardData =[
    {
        "componentKey": "AiRequestCard",
        "staticData": {
            "message": "11444你好chee和电池和我结婚就五金交电就忘记我都觉得我得无金丸惧电话我经久不衰我就是睡觉111",
            "citations": null
        },
        "data": {
            "isLoading": false,
            "isError": false
        },
        "_id": 1748331941389,
        "_t": 1748331941389
    },
    {
        "componentKey": "AiDataCard",
        "data": {
            "cardData": [
                {
                    "index": 0,
                    "context": "你好！请问有什么可以帮您的吗？",
                    "finish": 1
                }
            ],
            "messageId": "-8758934146241639005",
            "isCompleted": true
        },
        "staticData": {
            "requestParams": {
                "type": "onmessage",
                "data": [],
                "aiRequestCard": {
                    "componentKey": "AiRequestCard",
                    "staticData": {
                        "message": "你好",
                        "citations": null
                    },
                    "data": {
                        "isLoading": false,
                        "isError": false
                    },
                    "_id": 1748331941389,
                    "_t": 1748331941389
                },
                "alreadyOnExecute": false,
                "agentInfo": {
                    "componentKey": "AgentInfo",
                    "data": {
                        "agentInfo": {
                            "name": "智能创作助手",
                            "iconUrl": "ai-manager/agent/file/image?assistantId=-*******************",
                            "introduce": "智能创作助手",
                            "prologue": "我可以帮您创作以下内容：大纲、发言稿、通知、新闻稿、邀请、PPT模板、邮件、岗位描述、需求文档、商业计划书、调研报告、广告文案、合同、培训方案、教案、安全方案、代码",
                            "guideQuestions": [],
                            "id": "-*******************",
                            "code": "assist7700621515307434204"
                        }
                    },
                    "_id": 1748331941406,
                    "_t": 1748331941406
                },
                "aiLoadingCard": {
                    "componentKey": "AiLoadingAbort",
                    "data": {
                        "content": "意图识别中",
                        "isLoading": true
                    },
                    "_id": 1748331941406,
                    "_t": 1748331941406
                },
                "stream": {
                    "card": {
                        "componentKey": "AiRequestCard",
                        "staticData": {
                            "message": "你好",
                            "citations": null
                        },
                        "data": {
                            "isLoading": false,
                            "isError": false
                        },
                        "_id": 1748331941389,
                        "_t": 1748331941389
                    },
                    "isStop": true,
                    "timer": null,
                    "_id": 1748331941389,
                    "startTime": 1748331941389,
                    "option": {
                        "url": "/ai-manager/assistant/info/call/stream",
                        "params": {
                            "assistantId": "-*******************",
                            "assistantCode": "assist7700621515307434204",
                            "chatSessionId": "c294717d9e924a2996c67341e22e57b5",
                            "citations": [],
                            "input": "你好",
                            "sessionId": "-7214988920839778499"
                        },
                        "agentInfo": {
                            "name": "智能创作助手",
                            "iconUrl": "ai-manager/agent/file/image?assistantId=-*******************",
                            "introduce": "智能创作助手",
                            "prologue": "我可以帮您创作以下内容：大纲、发言稿、通知、新闻稿、邀请、PPT模板、邮件、岗位描述、需求文档、商业计划书、调研报告、广告文案、合同、培训方案、教案、安全方案、代码",
                            "guideQuestions": [],
                            "id": "-*******************",
                            "code": "assist7700621515307434204"
                        }
                    },
                    "dataHistory": [
                        {
                            "index": 0,
                            "context": "你好！请问有什么可以帮您的吗？",
                            "finish": 1
                        }
                    ],
                    "isCancel": false,
                    "messageType": {
                        "1": true,
                        "5": true,
                        "undefined": true
                    },
                    "retry": 0,
                    "agentInfo": {
                        "name": "智能创作助手",
                        "iconUrl": "ai-manager/agent/file/image?assistantId=-*******************",
                        "introduce": "智能创作助手",
                        "prologue": "我可以帮您创作以下内容：大纲、发言稿、通知、新闻稿、邀请、PPT模板、邮件、岗位描述、需求文档、商业计划书、调研报告、广告文案、合同、培训方案、教案、安全方案、代码",
                        "guideQuestions": [],
                        "id": "-*******************",
                        "code": "assist7700621515307434204"
                    },
                    "authExpired": false
                },
                "aiDataCard": null,
                "messageId": ""
            }
        },
        "_id": 1748331944763,
        "_t": 1748331944763,
        "config": {
            "isLast": false
        }
    },
    {
        "componentKey": "AiRequestCard",
        "staticData": {
            "message": "111",
            "citations": null
        },
        "data": {
            "isLoading": false,
            "isError": false
        },
        "_id": 1748332748786,
        "_t": 1748332748786
    },
    {
        "componentKey": "AiDataCard",
        "data": {
            "cardData": [
                {
                    "index": 0,
                    "context": "您好，您的输入“111”不太明确，可能需要您提供更多的上下文或说明，以便我更好地理解您的需求并提供帮助。请您详细描述一下，谢谢您好，您的输入“111”不太明确，可能需要您提供更多的上下文或说明，以便我更好地理解您的需求并提供帮助。请您详细描述一下，谢谢您好，您的输入“111”不太明确，可能需要您提供更多的上下文或说明，以便我更好地理解您的需求并提供帮助。请您详细描述一下，谢谢您好，您的输入“111”不太明确，可能需要您提供更多的上下文或说明，以便我更好地理解您的需求并提供帮助。请您详细描述一下，谢谢您好，您的输入“111”不太明确，可能需要您提供更多的上下文或说明，以便我更好地理解您的需求并提供帮助。请您详细描述一下，谢谢您好，您的输入“111”不太明确，可能需要您提供更多的上下文或说明，以便我更好地理解您的需求并提供帮助。请您详细描述一下，谢谢您好，您的输入“111”不太明确，可能需要您提供更多的上下文或说明，以便我更好地理解您的需求并提供帮助。请您详细描述一下，谢谢您好，您的输入“111”不太明确，可能需要您提供更多的上下文或说明，以便我更好地理解您的需求并提供帮助。请您详细描述一下，谢谢您好，您的输入“111”不太明确，可能需要您提供更多的上下文或说明，以便我更好地理解您的需求并提供帮助。请您详细描述一下，谢谢您好，您的输入“111”不太明确，可能需要您提供更多的上下文或说明，以便我更好地理解您的需求并提供帮助。请您详细描述一下，谢谢您好，您的输入“111”不太明确，可能需要您提供更多的上下文或说明，以便我更好地理解您的需求并提供帮助。请您详细描述一下，谢谢您好，您的输入“111”不太明确，可能需要您提供更多的上下文或说明，以便我更好地理解您的需求并提供帮助。请您详细描述一下，谢谢您好，您的输入“111”不太明确，可能需要您提供更多的上下文或说明，以便我更好地理解您的需求并提供帮助。请您详细描述一下，谢谢！",
                    "finish": 1
                }
            ],
            "messageId": "7165905778103961637",
            "isCompleted": true
        },
        "staticData": {
            "requestParams": {
                "type": "onmessage",
                "data": [],
                "aiRequestCard": {
                    "componentKey": "AiRequestCard",
                    "staticData": {
                        "message": "111",
                        "citations": null
                    },
                    "data": {
                        "isLoading": false,
                        "isError": false
                    },
                    "_id": 1748332748786,
                    "_t": 1748332748786
                },
                "alreadyOnExecute": false,
                "agentInfo": {
                    "componentKey": "AgentInfo",
                    "data": {
                        "agentInfo": {
                            "name": "智能创作助手",
                            "iconUrl": "ai-manager/agent/file/image?assistantId=-*******************",
                            "introduce": "智能创作助手",
                            "prologue": "我可以帮您创作以下内容：大纲、发言稿、通知、新闻稿、邀请、PPT模板、邮件、岗位描述、需求文档、商业计划书、调研报告、广告文案、合同、培训方案、教案、安全方案、代码",
                            "guideQuestions": [],
                            "id": "-*******************",
                            "code": "assist7700621515307434204"
                        }
                    },
                    "_id": 1748332786211,
                    "_t": 1748332786211
                },
                "aiLoadingCard": {
                    "componentKey": "AiLoadingAbort",
                    "data": {
                        "content": "意图识别中",
                        "isLoading": true
                    },
                    "_id": 1748332786211,
                    "_t": 1748332786211
                },
                "stream": {
                    "card": {
                        "componentKey": "AiRequestCard",
                        "staticData": {
                            "message": "111",
                            "citations": null
                        },
                        "data": {
                            "isLoading": false,
                            "isError": false
                        },
                        "_id": 1748332748786,
                        "_t": 1748332748786
                    },
                    "isStop": true,
                    "timer": null,
                    "_id": 1748332750378,
                    "startTime": 1748332750378,
                    "option": {
                        "url": "/ai-manager/assistant/info/call/stream",
                        "params": {
                            "assistantId": "-*******************",
                            "assistantCode": "assist7700621515307434204",
                            "chatSessionId": "c294717d9e924a2996c67341e22e57b5",
                            "citations": [],
                            "input": "111",
                            "sessionId": "-7214988920839778499"
                        },
                        "agentInfo": {
                            "name": "智能创作助手",
                            "iconUrl": "ai-manager/agent/file/image?assistantId=-*******************",
                            "introduce": "智能创作助手",
                            "prologue": "我可以帮您创作以下内容：大纲、发言稿、通知、新闻稿、邀请、PPT模板、邮件、岗位描述、需求文档、商业计划书、调研报告、广告文案、合同、培训方案、教案、安全方案、代码",
                            "guideQuestions": [],
                            "id": "-*******************",
                            "code": "assist7700621515307434204"
                        }
                    },
                    "dataHistory": [
                        {
                            "index": 0,
                            "context": "您好，您的输入“111”不太明确，可能需要您提供更多的上下文或说明，以便我更好地理解您的需求并提供帮助。请您详细描述一下，谢谢！",
                            "finish": 1
                        }
                    ],
                    "isCancel": false,
                    "messageType": {
                        "1": true,
                        "5": true,
                        "undefined": true
                    },
                    "retry": 1,
                    "agentInfo": {
                        "name": "智能创作助手",
                        "iconUrl": "ai-manager/agent/file/image?assistantId=-*******************",
                        "introduce": "智能创作助手",
                        "prologue": "我可以帮您创作以下内容：大纲、发言稿、通知、新闻稿、邀请、PPT模板、邮件、岗位描述、需求文档、商业计划书、调研报告、广告文案、合同、培训方案、教案、安全方案、代码",
                        "guideQuestions": [],
                        "id": "-*******************",
                        "code": "assist7700621515307434204"
                    },
                    "authExpired": false
                },
                "aiDataCard": null,
                "messageId": ""
            }
        },
        "_id": 1748332787961,
        "_t": 1748332787961,
        "config": {
            "isLast": false
        }
    },
    {
        "componentKey": "AiRequestCard",
        "staticData": {
            "message": "帮我写首春天的诗",
            "citations": null
        },
        "data": {
            "isLoading": false,
            "isError": false
        },
        "_id": 1748332808734,
        "_t": 1748332808734
    },
    {
        "componentKey": "AiDataCard",
        "data": {
            "cardData": [
                {
                    "index": 0,
                    "context": "《春之韵》\n\n春风拂面暖如绸，  \n绿意盎然遍山丘。  \n桃花灼灼映日笑，  \n燕子呢喃绕枝头。  \n\n溪水潺潺诉情深，  \n柳丝轻舞伴云游。  \n万物复苏生机现，  \n人间春色最温柔。  ",
                    "finish": 1
                }
            ],
            "messageId": "-2095819021340489105",
            "isCompleted": true
        },
        "staticData": {
            "requestParams": {
                "type": "onmessage",
                "data": [],
                "aiRequestCard": {
                    "componentKey": "AiRequestCard",
                    "staticData": {
                        "message": "帮我写首春天的诗",
                        "citations": null
                    },
                    "data": {
                        "isLoading": false,
                        "isError": false
                    },
                    "_id": 1748332808734,
                    "_t": 1748332808734
                },
                "alreadyOnExecute": false,
                "agentInfo": {
                    "componentKey": "AgentInfo",
                    "data": {
                        "agentInfo": {
                            "name": "智能创作助手",
                            "iconUrl": "ai-manager/agent/file/image?assistantId=-*******************",
                            "introduce": "智能创作助手",
                            "prologue": "我可以帮您创作以下内容：大纲、发言稿、通知、新闻稿、邀请、PPT模板、邮件、岗位描述、需求文档、商业计划书、调研报告、广告文案、合同、培训方案、教案、安全方案、代码",
                            "guideQuestions": [],
                            "id": "-*******************",
                            "code": "assist7700621515307434204"
                        }
                    },
                    "_id": 1748332829123,
                    "_t": 1748332829123
                },
                "aiLoadingCard": {
                    "componentKey": "AiLoadingAbort",
                    "data": {
                        "content": "意图识别中",
                        "isLoading": true
                    },
                    "_id": 1748332829123,
                    "_t": 1748332829123
                },
                "stream": {
                    "card": {
                        "componentKey": "AiRequestCard",
                        "staticData": {
                            "message": "帮我写首春天的诗",
                            "citations": null
                        },
                        "data": {
                            "isLoading": false,
                            "isError": false
                        },
                        "_id": 1748332808734,
                        "_t": 1748332808734
                    },
                    "isStop": true,
                    "timer": null,
                    "_id": 1748332811157,
                    "startTime": 1748332811157,
                    "option": {
                        "url": "/ai-manager/assistant/info/call/stream",
                        "params": {
                            "assistantId": "-*******************",
                            "assistantCode": "assist7700621515307434204",
                            "chatSessionId": "c294717d9e924a2996c67341e22e57b5",
                            "citations": [],
                            "input": "帮我写首春天的诗",
                            "sessionId": "-7214988920839778499"
                        },
                        "agentInfo": {
                            "name": "智能创作助手",
                            "iconUrl": "ai-manager/agent/file/image?assistantId=-*******************",
                            "introduce": "智能创作助手",
                            "prologue": "我可以帮您创作以下内容：大纲、发言稿、通知、新闻稿、邀请、PPT模板、邮件、岗位描述、需求文档、商业计划书、调研报告、广告文案、合同、培训方案、教案、安全方案、代码",
                            "guideQuestions": [],
                            "id": "-*******************",
                            "code": "assist7700621515307434204"
                        }
                    },
                    "dataHistory": [
                        {
                            "index": 0,
                            "context": "《春之韵》\n\n春风拂面暖如绸，  \n绿意盎然遍山丘。  \n桃花灼灼映日笑，  \n燕子呢喃绕枝头。  \n\n溪水潺潺诉情深，  \n柳丝轻舞伴云游。  \n万物复苏生机现，  \n人间春色最温柔。  ",
                            "finish": 1
                        }
                    ],
                    "isCancel": false,
                    "messageType": {
                        "1": true,
                        "5": true,
                        "undefined": true
                    },
                    "retry": 0,
                    "agentInfo": {
                        "name": "智能创作助手",
                        "iconUrl": "ai-manager/agent/file/image?assistantId=-*******************",
                        "introduce": "智能创作助手",
                        "prologue": "我可以帮您创作以下内容：大纲、发言稿、通知、新闻稿、邀请、PPT模板、邮件、岗位描述、需求文档、商业计划书、调研报告、广告文案、合同、培训方案、教案、安全方案、代码",
                        "guideQuestions": [],
                        "id": "-*******************",
                        "code": "assist7700621515307434204"
                    },
                    "authExpired": false
                },
                "aiDataCard": null,
                "messageId": ""
            }
        },
        "_id": 1748332837082,
        "_t": 1748332837082,
        "config": {
            "isLast": false
        }
    },
    {
        "componentKey": "AiRequestCard",
        "staticData": {
            "message": "哈哈",
            "citations": null
        },
        "data": {
            "isLoading": false,
            "isError": false
        },
        "_id": 1748332849147,
        "_t": 1748332849147
    },
    {
        "componentKey": "AiDataCard",
        "data": {
            "cardData": [
                {
                    "index": 0,
                    "context": "哈哈！看起来您很开心呢！如果还有其他需要或者想聊的内容，随时告诉我哦！😊",
                    "finish": 1
                }
            ],
            "messageId": "*******************",
            "isCompleted": true
        },
        "staticData": {
            "requestParams": {
                "type": "onmessage",
                "data": [],
                "aiRequestCard": {
                    "componentKey": "AiRequestCard",
                    "staticData": {
                        "message": "哈哈",
                        "citations": null
                    },
                    "data": {
                        "isLoading": false,
                        "isError": false
                    },
                    "_id": 1748332849147,
                    "_t": 1748332849147
                },
                "alreadyOnExecute": false,
                "agentInfo": {
                    "componentKey": "AgentInfo",
                    "data": {
                        "agentInfo": {
                            "name": "智能创作助手",
                            "iconUrl": "ai-manager/agent/file/image?assistantId=-*******************",
                            "introduce": "智能创作助手",
                            "prologue": "我可以帮您创作以下内容：大纲、发言稿、通知、新闻稿、邀请、PPT模板、邮件、岗位描述、需求文档、商业计划书、调研报告、广告文案、合同、培训方案、教案、安全方案、代码",
                            "guideQuestions": [],
                            "id": "-*******************",
                            "code": "assist7700621515307434204"
                        }
                    },
                    "_id": 1748332849237,
                    "_t": 1748332849237
                },
                "aiLoadingCard": {
                    "componentKey": "AiLoadingAbort",
                    "data": {
                        "content": "意图识别中",
                        "isLoading": true
                    },
                    "_id": 1748332849237,
                    "_t": 1748332849237
                },
                "stream": {
                    "card": {
                        "componentKey": "AiRequestCard",
                        "staticData": {
                            "message": "哈哈",
                            "citations": null
                        },
                        "data": {
                            "isLoading": false,
                            "isError": false
                        },
                        "_id": 1748332849147,
                        "_t": 1748332849147
                    },
                    "isStop": true,
                    "timer": null,
                    "_id": 1748332849148,
                    "startTime": 1748332849148,
                    "option": {
                        "url": "/ai-manager/assistant/info/call/stream",
                        "params": {
                            "assistantId": "-*******************",
                            "assistantCode": "assist7700621515307434204",
                            "chatSessionId": "c294717d9e924a2996c67341e22e57b5",
                            "citations": [],
                            "input": "哈哈",
                            "sessionId": "-7214988920839778499"
                        },
                        "agentInfo": {
                            "name": "智能创作助手",
                            "iconUrl": "ai-manager/agent/file/image?assistantId=-*******************",
                            "introduce": "智能创作助手",
                            "prologue": "我可以帮您创作以下内容：大纲、发言稿、通知、新闻稿、邀请、PPT模板、邮件、岗位描述、需求文档、商业计划书、调研报告、广告文案、合同、培训方案、教案、安全方案、代码",
                            "guideQuestions": [],
                            "id": "-*******************",
                            "code": "assist7700621515307434204"
                        }
                    },
                    "dataHistory": [
                        {
                            "index": 0,
                            "context": "哈哈！看起来您很开心呢！如果还有其他需要或者想聊的内容，随时告诉我哦！😊",
                            "finish": 1
                        }
                    ],
                    "isCancel": false,
                    "messageType": {
                        "1": true,
                        "5": true,
                        "undefined": true
                    },
                    "retry": 0,
                    "agentInfo": {
                        "name": "智能创作助手",
                        "iconUrl": "ai-manager/agent/file/image?assistantId=-*******************",
                        "introduce": "智能创作助手",
                        "prologue": "我可以帮您创作以下内容：大纲、发言稿、通知、新闻稿、邀请、PPT模板、邮件、岗位描述、需求文档、商业计划书、调研报告、广告文案、合同、培训方案、教案、安全方案、代码",
                        "guideQuestions": [],
                        "id": "-*******************",
                        "code": "assist7700621515307434204"
                    },
                    "authExpired": false
                },
                "aiDataCard": null,
                "messageId": ""
            }
        },
        "_id": 1748332851569,
        "_t": 1748332851569,
        "config": {
            "isLast": false
        }
    },
    {
        "componentKey": "AiRequestCard",
        "staticData": {
            "message": "你是谁",
            "citations": null
        },
        "data": {
            "isLoading": false,
            "isError": false
        },
        "_id": 1748332882093,
        "_t": 1748332882093
    },
    {
        "componentKey": "AiDataCard",
        "data": {
            "cardData": [
                {
                    "index": 0,
                    "context": "我是您的智能助手，随时为您提供帮助和支持！无论是解答问题、创作内容，还是协助完成任务，我都在这里为您服务！😊",
                    "finish": 1
                }
            ],
            "messageId": "-8258276990865814428",
            "isCompleted": true
        },
        "staticData": {
            "requestParams": {
                "type": "onmessage",
                "data": [],
                "aiRequestCard": {
                    "componentKey": "AiRequestCard",
                    "staticData": {
                        "message": "你是谁",
                        "citations": null
                    },
                    "data": {
                        "isLoading": false,
                        "isError": false
                    },
                    "_id": 1748332882093,
                    "_t": 1748332882093
                },
                "alreadyOnExecute": false,
                "agentInfo": {
                    "componentKey": "AgentInfo",
                    "data": {
                        "agentInfo": {
                            "name": "智能创作助手",
                            "iconUrl": "ai-manager/agent/file/image?assistantId=-*******************",
                            "introduce": "智能创作助手",
                            "prologue": "我可以帮您创作以下内容：大纲、发言稿、通知、新闻稿、邀请、PPT模板、邮件、岗位描述、需求文档、商业计划书、调研报告、广告文案、合同、培训方案、教案、安全方案、代码",
                            "guideQuestions": [],
                            "id": "-*******************",
                            "code": "assist7700621515307434204"
                        }
                    },
                    "_id": 1748332882185,
                    "_t": 1748332882185
                },
                "aiLoadingCard": {
                    "componentKey": "AiLoadingAbort",
                    "data": {
                        "content": "意图识别中",
                        "isLoading": true
                    },
                    "_id": 1748332882185,
                    "_t": 1748332882185
                },
                "stream": {
                    "card": {
                        "componentKey": "AiRequestCard",
                        "staticData": {
                            "message": "你是谁",
                            "citations": null
                        },
                        "data": {
                            "isLoading": false,
                            "isError": false
                        },
                        "_id": 1748332882093,
                        "_t": 1748332882093
                    },
                    "isStop": true,
                    "timer": null,
                    "_id": 1748332882093,
                    "startTime": 1748332882093,
                    "option": {
                        "url": "/ai-manager/assistant/info/call/stream",
                        "params": {
                            "assistantId": "-*******************",
                            "assistantCode": "assist7700621515307434204",
                            "chatSessionId": "c294717d9e924a2996c67341e22e57b5",
                            "citations": [],
                            "input": "你是谁",
                            "sessionId": "-7214988920839778499"
                        },
                        "agentInfo": {
                            "name": "智能创作助手",
                            "iconUrl": "ai-manager/agent/file/image?assistantId=-*******************",
                            "introduce": "智能创作助手",
                            "prologue": "我可以帮您创作以下内容：大纲、发言稿、通知、新闻稿、邀请、PPT模板、邮件、岗位描述、需求文档、商业计划书、调研报告、广告文案、合同、培训方案、教案、安全方案、代码",
                            "guideQuestions": [],
                            "id": "-*******************",
                            "code": "assist7700621515307434204"
                        }
                    },
                    "dataHistory": [
                        {
                            "index": 0,
                            "context": "我是您的智能助手，随时为您提供帮助和支持！无论是解答问题、创作内容，还是协助完成任务，我都在这里为您服务！😊",
                            "finish": 1
                        }
                    ],
                    "isCancel": false,
                    "messageType": {
                        "1": true,
                        "5": true,
                        "undefined": true
                    },
                    "retry": 0,
                    "agentInfo": {
                        "name": "智能创作助手",
                        "iconUrl": "ai-manager/agent/file/image?assistantId=-*******************",
                        "introduce": "智能创作助手",
                        "prologue": "我可以帮您创作以下内容：大纲、发言稿、通知、新闻稿、邀请、PPT模板、邮件、岗位描述、需求文档、商业计划书、调研报告、广告文案、合同、培训方案、教案、安全方案、代码",
                        "guideQuestions": [],
                        "id": "-*******************",
                        "code": "assist7700621515307434204"
                    },
                    "authExpired": false
                },
                "aiDataCard": null,
                "messageId": ""
            }
        },
        "_id": 1748332884656,
        "_t": 1748332884656,
        "config": {
            "isLast": true
        }
    }
];
API.store.action.setStaticData('isDigtalChat', true);
const showTopBlur = ref(false);
const showBottomBlur = ref(false);
let aiDataCard: AiDataCard | null = null;
let isFirst = false;
let lastDataItem: CardContentType = {
  index: 0,
  context: ""
};
let dataHistory: any[] = [];
let messageType: Record<number, boolean> = {};
let recommandQuestion:Array<object> = [];
let aiRequestCard: AiRequestCard | null = null;
let agentInfo: AgentInfo | null = null;
let aiLoadingCard: AiLoadingCard|null = null;
let messageId = '';
let illageSessionType: string | number;
let stream = {};
let isStop = false;
let isCancel = false;
let needMarkBlueRunStepsArray: any[] = [];
const option = {
    eventHandel: (type: string, data: any) => {
      const result = API.action.dealCardData({
        type, data, aiRequestCard, agentInfo, aiLoadingCard, stream, aiDataCard, messageId, illageSessionType
      });
      if(result) {
        illageSessionType = result.illageSessionType;
        agentInfo = result.agentInfo;
        aiLoadingCard = result.aiLoadingCard;
        aiDataCard = result.aiDataCard; 
        messageId = result.messageId;
      }
    },
    // stop: (isNormal:boolean)=>{
    //   requests.logRecord({
    //     info: "isNormal:" + isNormal + ", aiDataCard: " + JSON.stringify(aiDataCard),
    //     type: 'info'
    //   });
      //看结束的时候有没有sessionId ，没有的话去后端拿一个
      // const sessionId = sessionStorage.getItem(`aiSessionId_${stream.agentInfo?.id}`);
      // if(!sessionId) {
      //   const params = {
      //     chatSessionId: stream.option.params.chatSessionId,
      //     sessionId: 0,
      //     assistantId: stream.agentInfo?.id,
      //   }
      //   requests.getSessionId(params).then((data:any)=>{
      //     if(Number(data.code) === 0) {
      //       const sessionId = data.data.sessionId;
      //       sessionStorage.setItem(`aiSessionId_${params.assistantId}`, sessionId);
      //     }
      //   })
      // }
      
    //   // 最后的兜底
    //   const lastAnswer = {
    //     finish: 1,
    //     index: 0,
    //     context: '对不起，CoMi暂时无法回答你的问题，我会努力学习的。'
    //   };
    //   if (!isNormal) {
    //     dealStopCard(aiDataCard, aiLoadingCard)
    //   } else if (isNormal && aiDataCard) {
    //     aiDataCard.data.illageSessionType = illageSessionType;
    //     aiDataCard.data.isCompleted = true;
    //     //如果没有返回数据，并且有执行过程
    //     if (!aiDataCard.data.cardData?.length) {
    //       store.card.remove(aiLoadingCard);
    //       aiDataCard.data.cardData.push(lastAnswer);
    //     }
    //   } else if (isNormal && !aiDataCard) {
    //     aiDataCard = store.card.add({
    //       componentKey: 'AiDataCard',
    //       data: {
    //         cardData: [lastAnswer]
    //       }
    //     }, aiLoadingCard);
    //   }
    //   // 绑定一个流，可以处理未完成的流式卡片语音播放
    //   if (store.state.isOpenVoice && isNormal && !store.staticData.longTouchCard?.data?.isPlay && aiDataCard) {
    //     if(store.staticData.hasVoiceKey) {
    //       aiDataCard.data.isPlay = true;
    //     }
    //     store.action.setStaticData('longTouchCard', aiDataCard);
    //     Voice.add(aiDataCard, stream).run(function () {
    //       aiDataCard && (aiDataCard.data.isPlay = false);
    //     });
    //   }
    // },
  }
  onMounted(() => {
  ZYJSBridge.setEventListener('callContent', dealMessage);
});
function createRequestCard(message: string): AiRequestCard {
  return API.store.card.add({
    componentKey: 'AiRequestCard',
    staticData: { 
      message,
      citations: null
    },
    data: {
      isLoading: false,
      isError: false
    }
  });
}
//新对话还原参数
function restoreParams() {
  aiDataCard = null;
  isFirst = false;
  lastDataItem = {
    index: 0,
    context: ""
  };
  dataHistory = [];
  messageType = {};
  recommandQuestion = [];
  aiRequestCard = null;
  agentInfo = null;
  aiLoadingCard = null;
  messageId = '';
  illageSessionType = '';
  stream = {};
  isStop = false;
  isCancel = false;
  needMarkBlueRunStepsArray = [];
}
function dealMessage(message:string) { 
  // console.log(JSON.parse(JSON.stringify(state.allCardData)), "allCardDataallCardData");
  const messageData = JSON.parse(decodeURIComponent(message));
  const type = Number(messageData.type);
  console.log(messageData, "messageData");
  if(!messageData.contentData){
    return;
  }
  API.store.scrollToBottom();
  if(type === 1) {//问的内容 type为1
    API.store.action.setState('isScrolling', true);
    createRequestCard(messageData.contentData)
  }else if(type === 2) {//答的内容 type为2
    const contentData = JSON.parse(messageData.contentData);
    const result = handleStreamMessage({data: contentData}, {
      isFirst,
      lastDataItem,
      dataHistory: dataHistory,
      messageType: messageType,
      recommandQuestion,
      option: option,
      isStop: isStop,
      isCancel: isCancel,
      httpStop: null,
      needMarkBlueRunStepsArray
    });
    // 更新上下文状态
    isFirst = result.newIsFirst;
    lastDataItem = result.newLastDataItem;
    dataHistory = result.updatedDataHistory;
    messageType = result.updatedMessageType;
    recommandQuestion = result.updatedRecommandQuestion;
    isCancel = result.updateCancel;
    needMarkBlueRunStepsArray = result.updatedNeedMarkBlueRunStepsArray;
    if(contentData.finish == 1) {
      restoreParams();
    }
    // console.log('contentData', contentData);
  }else if(type === 3) { //打断的逻辑
    const cardDataLength = state.allCardData.length;
    const contentData = JSON.parse(messageData.contentData);
    //打断的逻辑
    if(!contentData.content && contentData.finish === 1) {
      if(cardDataLength) {
        const lastCard = state.allCardData[cardDataLength - 1];
        const cardData = lastCard.data?.cardData;
        if(lastCard.componentKey === "AiDataCard") {
          const lastItem = cardData[cardData.length - 1];
          lastItem.finish = 1;
          lastCard.finish = 1;
        }
      }
      restoreParams();
      return;
    }

  }
  API.store.scrollToBottom();
  // console.log('dealMessage', messageData);
}
function setBlur(topBlurValue: boolean, bottomBlurValue: boolean) {
  showTopBlur.value = topBlurValue;
  showBottomBlur.value = bottomBlurValue;
}
function dealMask(e: Event) {
  const target = e.target as HTMLDivElement;
  const clientHeight = target?.clientHeight;
  const scrollHeight = target?.scrollHeight;
  const scrollTop = target?.scrollTop;
  const needScroll = scrollHeight > clientHeight;
  const distance = scrollHeight - scrollTop;
  if(!needScroll) {
    setBlur(false, false);
    return;
  }
  if(scrollTop === 0) {
    setBlur(false, true);
  }else if((distance - clientHeight) < 1) {
    setBlur(true, false);
    API.store.action.setState('isScrolling', true);
  }else if(scrollTop > 0 && distance > clientHeight) {
    setBlur(true, true);
  }
}

function stopScroll() {
  API.store.action.setState('isScrolling', false);
}

</script>
<style lang="scss" scoped>
.digital-dialogue{
  // margin-top: 10px;
  // background: grey;
  height: 100%;
  overflow: hidden;
  position: relative;
  .dialogue-container {
    scroll-behavior: smooth;
    height: 100%;
    overflow-y: auto;
    overscroll-behavior: none;
    -webkit-overflow-scrolling: touch;
  }
  .top-blur {
    background: linear-gradient(179.95deg, #1C3165 0.04%, rgba(28, 49, 100, 0.6) 47.97%, rgba(27, 47, 98, 0) 95.9%);
    width: 100%;
    height: 45px;
    position: absolute;
    top: 0;
    left: 0;
  }
  .bottom-blur {
    background: linear-gradient(0deg, rgba(19, 20, 52, 0.9) 0.04%, rgba(20, 22, 55, 0.5) 47.97%, rgba(27, 47, 98, 0) 95.9%);
    width: 100%;
    height: 45px;
    position: absolute;
    bottom: 0;
    left: 0;
  }
}
</style>
<style lang="scss">
html, body {
  background-color: transparent;
}
::-webkit-scrollbar {
  display: none;
}
.digital-dialogue {
  .request-card-container .ai-request-card {
    justify-content: flex-start;
    color: rgba(249, 251, 252, 0.75);
    .content {
      background: transparent;
      padding: 0;
      max-width: 100%;
      // font-size: 14px;
      // font-size: var(--theme-font-size1, 14px);
      // line-height: 22px;
      // line-height: var(--theme-line-height1, 22px);
      // font-weight: 400;
    }
  } 
  
  .data-shell {
    background: transparent;
    padding: 0;
    min-height: 22px;
    .data-chart-wrap {
      border-radius: 2px 12px 12px 12px;
      background-color: #fff
    }
    // :not(.bi-card-wrapper) .data-stream{
    //   color: #F9FBFC;
    //   font-size: 14px;
    //   font-size: var(--theme-font-size1, 14px);
    //   line-height: 22px;
    //   line-height: var(--theme-line-height1, 22px);
    //   font-weight: 600;
    //   p {
    //     font-size: 14px;
    //     font-size: var(--theme-font-size1, 14px);
    //     line-height: 22px;
    //     line-height: var(--theme-line-height1, 22px);
    //     color: #F9FBFC;
    //   }
    //   ul li::marker{
    //     color: #fff;
    //   }
    //   ol li::marker{
    //     color: #fff;
    //   }
    //   h1, h2, h3, h4, h5,h6 {
    //     color: #fff;
    //   }
    // }
    // .bi-card-wrapper {
    //   padding: 8px 16px;
    //   border-radius: 2px 12px 12px 12px;
    //   background-color: #fff;
    //   .card-header .iconScale {
    //     display: none;
    //   }
    // }
  }
}
</style>