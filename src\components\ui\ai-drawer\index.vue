<template>
<Transition appear name="drawer" @afterLeave="drawerLeave" >
<div v-if="visible" class="ai-drawer" :class=drawerClass>
  <div  class="ai-drawer-mask" @click="closeDrawer">
  </div>
    <div class="ai-drawer-container">
      <div class="header" :style="headerStyle.paddingLeft">
        <slot name="header">
          <div class="title" :style="headerStyle.textPosition">{{drawerConfig.title}}</div>
          <div @click="closeDrawer" class="iconfont close-icon ai-icon-cha"></div>
        </slot>
      </div>
      <div class="content">
        <slot name="content"></slot>
      </div>
      <div class="footer">
        <slot name="footer"></slot>
      </div>
    </div>
</div>
</Transition>
</template>

<script setup lang="ts">
  import { ref, computed} from 'vue';
  const props = defineProps({
    drawerConfig : {
      type: Object,
      default: () => ({
        title: "提示"
      })
    },
    visible: {
      type: Boolean,
      default: true
    },
    drawerClass: {
      type: String,
      default: ""
    }
  });
  const headerStyle = computed(()=>{
    if(props.drawerConfig.isTitleCenter) {
      return {
        paddingLeft: 'padding-left: 32px',
        textPosition: 'text-align:center'
      }
    }
    return {}
  })
  const _$emit = defineEmits(['closeDrawer', 'afterClose']);
  function closeDrawer() {
    _$emit('closeDrawer');
  }
  // 抽屉关闭动画完毕,才去销毁dom
  // function animationEnd() {
  //   _$emit('animationEnd');
  // }
  function drawerLeave() {
    // _$emit('animationEnd');
    _$emit('afterClose');
  }
</script>

<style lang="scss" scoped>
.drawer-enter-from, .drawer-leave-to {
  opacity: 0;
}
.drawer-enter-active, .drawer-leave-active {
  transition: opacity 300ms ease-in-out;
}
.drawer-enter-to, .drawer-leave-from {
  opacity: 1;
}
.drawer-enter-active .ai-drawer-container  {
  animation: showOrHideDrawer 300ms ease-in-out;
}
.drawer-leave-active .ai-drawer-container {
  animation: showOrHideDrawer 300ms ease-in-out reverse;
}
  .ai-drawer {
    position: relative;
    .ai-drawer-mask {
      position: fixed;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      z-index: 999;
      background: rgba(0, 0, 0, 0.5);
    }
    .ai-drawer-container {
      display: flex;
      flex-direction: column;
      position: fixed;
      // height: 50%;
      width: 100%;
      background: #fff;
      bottom: 0;
      left: 0;
      border-radius: 8px 8px 0 0;
      padding: 16px 16px 0;
      box-sizing: border-box;
      // animation: openDrawer 300ms ease-in-out forwards;
      z-index: 1000;
      .header {
        display: flex;
        line-height: 22px;
        margin-bottom: 16px;
      }
      .title {
        flex: 1;
        text-align: left;
        color: #000000;
        font-size: 16px;
        font-size: var(--theme-font-size2, 16px);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-weight: 600;
      }
      .close-icon {
        color: rgba(0, 0, 0, 0.6);
        color: var(--theme-font-color2, rgba(0, 0, 0, 0.6))
      }
      .content {
        flex: 1;
      }
      @keyframes openDrawer {
        0% {
          transform: translate3d(0, 100%, 0);
        }
        100% {
          transform: translate3d(0, 0, 0);
        }
      }
    }
  }
@keyframes showOrHideDrawer { 
  0% {
    transform: translate3d(0, 100%, 0);
  }
  100% {
    transform: translate3d(0, 0, 0);
  }
}
</style>