<template>
  <div class="empty-container">
    <img class="img" src="@/assets/images/empty.png" alt="" />
    <div class="text">{{ text }}</div>
  </div>
</template>
<script setup lang="ts">
defineProps({
  text: {
    type: String,
    default: '',
  },
});
</script>
<style scoped lang="scss">
.empty-container {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  height: 100%;
  .img {
    width: 120px;
    height: 120px;
  }
  .text {
    font-family: PingFang SC;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    letter-spacing: 0%;
    text-align: center;
    color: #00000066;
    margin-top: 20px;
  }
}
</style>
