<template>
  <transition name="fade">
    <div v-if="visible" class="ai-modal-mask" @click="handleMaskClick"></div>
  </transition>

  <transition name="zoom">
    <div v-if="visible" class="ai-modal-wrap" :style="wrapStyle">
      <div class="ai-modal" :style="modalStyle">
        <div class="ai-modal-content">
          <div class="ai-modal-header">
            <div class="ai-modal-title">{{ title }}</div>
            <!-- <button class="ai-modal-close" @click="handleCancel">
              <span class="ai-modal-close-x">×</span>
            </button> -->
          </div>
          <div class="ai-modal-body">
            <div class="login-item">
              <span class="login-item-label">用户名：</span>
              <AiInput class="login-item-input" placeholder="请输入用户名" v-model="username" />
            </div>
            <div class="login-item">
              <span class="login-item-label">密码：</span>
              <AiInput
                class="login-item-input"
                placeholder="请输入密码"
                type="password"
                v-model="password"
              />
            </div>
          </div>
          <div class="ai-modal-footer">
            <button v-if="showCancel" class="ai-btn" @click="handleCancel">取消</button>
            <button class="ai-btn ai-btn-primary" @click="handleOk">登录</button>
          </div>
        </div>
      </div>
    </div>
  </transition>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import AiInput from '@/components/ui/ai-input/index.vue';
import { aiToast } from '@/plugins/ai-toast';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: '账号密码登录',
  },
  width: {
    type: [String, Number],
    default: '80%',
  },
  centered: {
    type: Boolean,
    default: true,
  },
  maskClosable: {
    type: Boolean,
    default: true,
  },
  showCancel: {
    type: Boolean,
    default: true,
  },
});

const emits = defineEmits(['update:visible', 'ok', 'cancel']);
const modalStyle = computed(() => ({
  width: typeof props.width === 'number' ? `${props.width}px` : props.width,
}));

const wrapStyle = computed(() => ({
  display: 'flex',
  justifyContent: 'center',
  alignItems: props.centered ? 'center' : 'flex-start',
  paddingTop: props.centered ? 0 : '100px',
}));

const username = ref('');
const password = ref('');

function handleOk() {
  if (!username.value) {
    aiToast({
      content: '请输入用户名',
    });
    return;
  }
  if (!password.value) {
    aiToast({
      content: '请输入密码',
    });
    return;
  }
  const params = {
    loginName: username.value,
    password: password.value,
  };
  emits('ok', params);
  emits('update:visible', false);
}

function handleCancel() {
  emits('cancel');
  emits('update:visible', false);
}

function handleMaskClick() {
  if (props.maskClosable) {
    handleCancel();
  }
}
</script>

<style scoped lang="scss">
.ai-modal-mask {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 10;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.45);
}

.ai-modal-wrap {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow: auto;
  outline: 0;
  z-index: 11;
}

.ai-modal {
  position: relative;
  margin: 0 auto 100px;
  background: #fff;
  border-radius: 2px;
  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08),
    0 9px 28px 8px rgba(0, 0, 0, 0.05);
}

.ai-modal-content {
  position: relative;
  background-color: #fff;
  border: 0;
  border-radius: 2px;
  background-clip: padding-box;
}

.ai-modal-header {
  padding: 16px 24px;
  color: rgba(0, 0, 0, 0.85);
  background: #fff;
  border-bottom: 1px solid #ccc;
  border-radius: 2px 2px 0 0;
}

.ai-modal-title {
  margin: 0;
  color: rgba(0, 0, 0, 0.85);
  font-weight: 500;
  font-size: 16px;
  line-height: 22px;
}

.ai-modal-close {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 10;
  padding: 0;
  color: rgba(0, 0, 0, 0.45);
  font-weight: 700;
  line-height: 1;
  text-decoration: none;
  background: transparent;
  border: 0;
  outline: 0;
  cursor: pointer;
  transition: color 0.3s;
}

.ai-modal-close-x {
  display: block;
  width: 56px;
  height: 56px;
  font-size: 16px;
  font-style: normal;
  line-height: 56px;
  text-align: center;
  text-transform: none;
  text-rendering: auto;
}

.ai-modal-body {
  padding: 24px;
  font-size: 14px;
  line-height: 1.5715;
  word-wrap: break-word;
  .login-item {
    height: 32px;
    line-height: 32px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin-bottom: 16px;
    &:last-child {
      margin-bottom: 0;
    }

    .login-item-label {
      width: 60px;
      text-align: right;
    }
    .login-item-input {
      flex: 1;
    }
  }
}

.ai-modal-footer {
  padding: 10px 16px;
  text-align: right;
  background: transparent;
  border-top: 1px solid #ccc;
  border-radius: 0 0 2px 2px;
}

.ai-btn {
  line-height: 1.5715;
  position: relative;
  display: inline-block;
  font-weight: 400;
  white-space: nowrap;
  text-align: center;
  background-image: none;
  border: 1px solid transparent;
  box-shadow: 0 2px 0 rgba(0, 0, 0, 0.015);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  user-select: none;
  touch-action: manipulation;
  height: 32px;
  padding: 4px 15px;
  font-size: 14px;
  border-radius: 2px;
  color: rgba(0, 0, 0, 0.85);
  background: #fff;
  border-color: #d9d9d9;
  margin-left: 8px;
}
.ai-btn:focus-visible {
  outline: none
}

.ai-btn-primary {
  color: #fff;
  background: #1890ff;
  border-color: #1890ff;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.12);
  box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
}

/* 动画效果 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.zoom-enter-active,
.zoom-leave-active {
  transition: transform 0.3s cubic-bezier(0.78, 0.14, 0.15, 0.86);
}
.zoom-enter-from,
.zoom-leave-to {
  transform: scale(0.5);
  opacity: 0;
}
</style>
