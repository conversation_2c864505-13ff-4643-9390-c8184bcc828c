import type { CardDataType } from '@/types/api';
type QueueItem = {
  card: CardDataType;
  loaded: boolean;
}
export default class Queue {
  static items: QueueItem[] = [];

  constructor() {
  }
  // 入队
  static enqueue(card: CardDataType) {
    Queue.items.push({
      card,
      loaded: false,
    });
  }

  // 出队
  static dequeue() {
    if (Queue.isEmpty()) {
      return null;
    }
    return Queue.items.shift();
  }

  // 查看队首元素
  static peek() {
    if (Queue.isEmpty()) {
      return '队列为空';
    }
    return Queue.items[0];
  }

  // 检查队列是否为空
  static isEmpty() {
    return Queue.items.length === 0;
  }

  // 获取队列长度
  static size() {
    return Queue.items.length;
  }

  // 清空队列
  static clear() {
    Queue.items = [];
  }
}

// 使用示例
// const myQueue = new Queue();
// myQueue.enqueue('元素1');
// myQueue.enqueue('元素2');
// console.log(myQueue.dequeue()); // 输出: '元素1'
// console.log(myQueue.peek()); // 输出: '元素2'
// console.log(myQueue.size()); // 输出: 1
// myQueue.clear();
// console.log(myQueue.isEmpty()); // 输出: true
