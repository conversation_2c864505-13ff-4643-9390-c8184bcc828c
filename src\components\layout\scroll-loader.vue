<template>
  <div class="scroll-loader" @scroll="handleScroll">
    <slot></slot>
    <div v-if="isLoading" class="loading-indicator">
      <AiStateLoading />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue';
import AiStateLoading from '@/components/ui/ai-state/loading.vue';
import type { HistoryParams } from '@/types/chat';

const props = defineProps<{
  historyParams: HistoryParams;
  hasMore: boolean;
  onLoadMore: (isLoadMore: boolean) => void;
  historyLoading: boolean;
}>();

const isLoading = ref(false);

const handleScroll = (e: Event) => {
  const target = e.target as HTMLElement;
  if (target.scrollTop === 0 && props.hasMore && !props.historyLoading) {
    loadMore();
  }
};

const loadMore = async () => {
  if (isLoading.value) return;
  
  try {
    isLoading.value = true;
    await props.onLoadMore(true);
  } finally {
    isLoading.value = false;
  }
};
</script>

<style lang="scss" scoped>
.scroll-loader {
  position: relative;
  height: 100%;
  
  .loading-indicator {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style> 