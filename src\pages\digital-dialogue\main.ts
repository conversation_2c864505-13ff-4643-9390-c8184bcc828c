import { createApp, defineAsyncComponent } from "vue";
import App from './index.vue';
import '@/assets/icon/iconfont.css';
import '@/assets/variables.css';
import '../../assets/base.css';
import "@seeyon/seeyon-comi-plugins-library/dist/seeyon-comi-plugins-library.css";


const app = createApp(App);
// 引入远程组件
const ComiExternalBiCard = defineAsyncComponent(() => import("remote_app/ComiExternalBiCard"));

// 注册远程组件
app.component("ComiExternalBiCard", ComiExternalBiCard);
app.mount('#app');
