import {createApp, ref, h, App} from 'vue';
import KnowledgeDrawer from '@/components/knowledge-drawer/index.vue';

export const showKnowledgeDrawer = (options: any):void =>{
  //创建容器
  const knowledgeDom: HTMLElement = document.createElement('div');
  document.body.appendChild(knowledgeDom);
  const knowledgeApp: App = createApp({
    setup() {
      const showKnowledge = ref(true);
      //回调函数，该组件只有取消
      function handleCancel():void {
        if(options.cancel) {
          options.cancel();
        }
        showKnowledge.value = false;
      }

      return {
        showKnowledge,
        handleCancel
      }
    },
    render() {
      return h(KnowledgeDrawer as  Component, {
        // showKnowledge: this.showKnowledge,
        knowledgeData: options.knowledgeData,
        onClose: this.handleCancel,
        "onUpdate:showKnowledge": (val:boolean):void => {
          if(!val) {
            destroy();
          }
          this.showKnowledge = val;
        }
      })
    }
  });
  knowledgeApp.mount(knowledgeDom);
  function destroy() {
    knowledgeApp.unmount();
    document.body.removeChild(knowledgeDom);
  }
}

export default{
  showKnowledgeDrawer,
  install(app: App) {
    app.config.globalProperties.$showKnowledgeDrawer = showKnowledgeDrawer;
  }
}