<template>
  <div class="guide-card" v-if="guideQuestions.prologue || guideQuestions?.guideQuestions?.length">
    <div class="title">{{guideQuestions.prologue}}</div>
    <div v-for="question in guideQuestions.guideQuestions" :key="question.id" @click="handleGuideQuestion(question)" class="guide-question-item">
      <div class="question ellipsis">{{question.name}}</div>
      <!-- 图标，先占位 -->
      <img class="question-icon" src="../../assets/images/arrow.png">
    </div>
  </div>
</template>

<script setup lang="ts">
  import API from '@/api/index';
import { ProloguePreQuestionsItem } from '@/types';

  const {guideQuestions} = defineProps({
    guideQuestions: {
        type: Object,
        default: () => ({})
      }
  });

  /**
   * 点击开场白或引导问题
   * @param {*} data 
   */
  function handleGuideQuestion(data: ProloguePreQuestionsItem){
    // API.store.action.setState("selectedAgentInfo", null);
    API.action.sendMessage(data.name);
  }
</script>

<style lang="scss" scoped>
  .guide-card {
    border-radius: 12px;
    background-color: #fff;
    box-shadow: 0 4px 7px rgba(0, 0, 0, 0.04);
    padding: 12px;

    .title {
      font-weight: 400;
      font-size: 14px;
      font-size: var(--theme-font-size1, 14px);
      line-height: 20px;
      line-height: var(--theme-line-height0, 20px);
      color: rgba(0, 0, 0, 0.6);
    }
    .guide-question-item {
      background: #F3F6FF;
      border-radius: 4px;
      display: flex;
      padding: 10px 12px;
      align-items: center;
      margin-top: 8px;
      .question {
        flex: 1;
        font-size: 14px;
        font-size: var(--theme-font-size1, 14px);
        margin-right: 12px;
      }
      .question-icon {
        width: 14px;
        height: 14px;
        // background-color: #5973FE;
        // border-radius: 50%;
        // background: url('../../assets/images/arrow.png');
      }
    }
  }
</style>