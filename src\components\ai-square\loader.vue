<template>
  <div class="loader-container">
    <AiStateLoading />
    <div class="text">加载中...</div>
  </div>
</template>
<script setup lang="ts">
import AiStateLoading from '@/components/ui/ai-state/loading.vue';
</script>
<style lang="scss" scoped>
.loader-container {
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  .loader {
    border: 1px solid #f3f3f3; /* Light grey */
    border-top: 1px solid #4379ff; /* Blue */
    border-top: 1px solid var(--theme-color5, #4379ff);
    font-size: 12px;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    animation: spin 2s linear infinite;
  }

  .ai-state-loading {
    width: 20px;
    height: 20px;
  }

  .text {
    margin-top: 5px;
    color: #4379ff;
    color: var(--theme-color5, #4379ff);
    font-size: 12px;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
}
</style>
