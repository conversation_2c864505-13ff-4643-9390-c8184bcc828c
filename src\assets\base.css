html,body{
    margin: 0;
    padding: 0;
    width: 100%;
    height: 100%;
    background: transparent;
    /* -webkit-text-size-adjust: 100%; */
    -webkit-tap-highlight-color: transparent;
    -webkit-text-size-adjust: none;
}

html,body,dl,dt,dd,ul,ol,li,h1,h2,h3,h4,h5,h6,form,input,label,textarea,p,div {
      margin: 0;
      padding: 0;
      -webkit-text-size-adjust: none;
      font-family: <PERSON><PERSON>, "PingFang SC", "Microsoft YaHei", Helvetica, sans-serif, "SimSun";
}

ol {
    padding-left: 16px; /* 调整缩进 */
}
ul {
    padding-left: 10px;
    list-style: disc;
}
input[type=text],input[type=password],input[type=number],input[type=email],input[type=search],input[type=tel],input[type=radio],input[type=checkbox], textarea {
    outline: 0;
    -webkit-appearance: none;
    -webkit-user-select: text;
    -webkit-tap-highlight-color: transparent;
}

input::-webkit-input-placeholder,textarea::-webkit-input-placeholder {
    color: #999
}
.ellipsis {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
#app {
    height: 100%;
    width: 100%;
}   
