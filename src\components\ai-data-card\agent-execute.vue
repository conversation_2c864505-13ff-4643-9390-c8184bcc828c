<template>
  <div class="agent-execute">
    <div v-for="(item, index) in agentProcess" :key="item.id">
      <div class="exec-stage">
        <div class="left gray-circle" v-if="!item.complete && !item.loading"></div>
        <AiStateLoading
          v-if="item.loading"
          :inline="true"
          class="left agent-loading"
        ></AiStateLoading>
        <div class="iconfont left circle ai-icon-duihao-yuanxing-mianxing" v-if="item.complete"></div>
        <div class="right">{{ item.title }}</div>
      </div>
      <div
        v-if="index !== agentProcess.length - 1"
        class="line"
        :class="{ 'complete-line': item.complete }"
      ></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import AiStateLoading from '@/components/ui/ai-state/loading.vue';
import {AgentProcessItem} from '@/types/api'
const props = defineProps({
  agentProcess: {
    type: Array<AgentProcessItem>,
    default: () => ([]),
  },
});
</script>

<style lang="scss" scoped>
.agent-execute {
  background-color: #f7f8f8;
  padding: 4px 8px;
  border-radius: 4px;
  margin-bottom: 8px;
  .exec-stage {
    display: flex;
    align-items: center;
    line-height: 22px;
    line-height: var(--theme-line-height1, 22px);
    .gray-circle {
      width: 12px;
      box-sizing: border-box;
      height: 12px;
      border: 1.5px solid #d8dadf;
      border-radius: 50%;
      margin-right: 8px;
    }
    .agent-loading {
      width: 12px;
      height: 12px;
    }
  }

  .left {
    width: 14px;
    margin-right: 8px;
  }
  .circle {
    font-size: 14px;
    color: #4379FF;
    margin-right: 8px;
  }
    
  .line {
    height: 12px;
    width: 1px;
    background: #d8dadf;
    margin: 0 6px;
  }
  .complete-line {
    background: #4379ff;
  }
}
</style>
