<template>
  <div class="data-header">
    <div class="data-left">
      <div class="title" :title="title">
        {{ title }}
      </div>
    </div>
    <div class="data-right">
      <slot class="wrap-right-main"></slot>
      <div class="content iconfont ai-icon-suoxiao" @click="closeWebView('normal')" />
    </div>
  </div>
</template>
<script setup lang="ts">
import { closeWebView } from '@/plugins/app-plugin';

const { title } = defineProps<{
  title: string;
}>();
</script>
<style lang="scss" scoped>
.data-header {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .data-left {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    // width: calc(100% - 40px);
    flex: 1;
  }

  .data-right {
    // flex: 1;
    display: flex;
    font-weight: 400;
    // .wrap-right-main {
    //   display: flex;
    // }
  }

  .title {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    word-break: keep-all;
  }
  .content {
    color: rgba(0, 0, 0, 0.4);
    color: var(--theme-font-color3, rgba(0, 0, 0, 0.4));
    font-size: 18px;
    font-size: var(--theme-font-size3, 18px);
  }
}
</style>
