import type { ChatMessage } from '@/types/chat';
import utils from '@/utils';
import { CardDataType } from '@/types/api';

interface MessageFormatterOptions {
  assistantInfo?: Record<string, any>;
}

/**
 * 格式化用户消息
 */
const formatUserMessage = (item: ChatMessage) => ({
  _id: item.id,
  _t: item.messageTime,
  _index: item.index,
  componentKey: 'AiRequestCard',
  data: {
    isLoading: false,
    isError: false,
    isHistory: true,
    isPlay: false,
  },
  staticData: {
    citations: item.citationsJson ||'',
    message: item.content
  }
});

/**
 * 格式化 AI 回复消息
 */
const formatAIMessage = (item: ChatMessage, assistantInfo?: Record<string, any>) => ({
  _id: item.id,
  _t: item.messageTime,
  _index: item.index,
  componentKey: 'AiDataCard',
  data: {
    isLoading: false,
    isError: false,
    illageSessionType:false,
    isHistory: true,
    isPlay: false,
    isLast: true,
    isCompleted: true,
    messageId: item.id,
    isUnlike: false,
    cardData: item.cardData,
    isKnowledgeData: typeof item.citationsJson === 'string' &&  item.citationsJson,
    knowledgeData: item.knowledgeData || [],
    assistantInfo: item.assistant
  },
  staticData: {
    citations: item.citationsJson || '',
    message: item.content,
    requestParams: {
      assistantCode: item.assistantCode,
      assistantId: item.assistantId,
      chatSessionId: item.chatSessionId,
      citations: [],
      input: item.input || '',
      sessionType: item.sessionType,
    }
  }
});

/**
 * 格式化消息列表
 */
export const formatMessageList = (list: ChatMessage[], options: MessageFormatterOptions = {}) => {
  return list.map((item: ChatMessage, index: number) => {
    item.cardData = utils.formatHistoryContent(item.content,item.citationsJson || '',item.messageType,item.needMarkBlueRunSteps || [],[]);
    if(item.hitKnowledgeRunSteps?.length) {
      const knowledgeSource = item.hitKnowledgeRunSteps[0].content;
      item.knowledgeData = utils.dealKnowledgeData(knowledgeSource);
    }
    return item.messageType === 0 
      ? formatUserMessage(item)
      : formatAIMessage(item);
  });
};