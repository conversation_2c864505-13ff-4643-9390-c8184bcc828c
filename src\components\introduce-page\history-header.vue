<template>
  <div class="history-header" :class="{ 'with-img-header': assistantImg?.img, 'signel-header-background': newBackground}">
    <div class="content" :class="{ 'with-img': assistantImg?.img }">
      <div class="title">{{ title }}</div>
      <div class="description">{{ content }}</div>
    </div>
    <!-- 图片先占位 -->
    <img
      v-if="assistantImg?.img"
      :src="assistantImg?.img"
      alt=""
      style="width: 110px; height: 110px"
      class="assistant-img"
    />
    <div v-else class="header-icon-box">
      <img class="header-icon" :src="headerIconUrl" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { getAssistantId, getQueryString } from '@/utils/common';
import { getAssistantImg } from '@/utils/getAssistantImg';
import { computed } from 'vue';
const newBackground = getQueryString('newBackground') === '1';
const assistantId = getAssistantId() as string;
const props = defineProps({
  title: {
    type: String,
    default: '',
  },
  content: {
    type: String,
    default: '',
  },
});

const assistantImg = computed(() => {
  const obj = getAssistantImg(assistantId);
  console.log(obj);
  if (obj && obj.img) {
    return obj;
  }
  return null;
});

import headerSingle from '@/assets/images/header-single.gif';
const headerIconUrl = computed(() => {
  return headerSingle + '?t=' + Date.now();
});
</script>

<style lang="scss" scoped>
.history-header {
  display: flex;
  padding: 12px;
  background: linear-gradient(180deg, #548cff 0%, #4a7be0 100%);
  box-sizing: border-box;
  border-radius: 12px 12px 0 0;
  align-items: flex-start;
  min-height: 86px;
  margin-bottom: -12px;
  position: relative;
  .content {
    display: flex;
    flex: 1;
    // min-height: 50px;
    flex-direction: column;
    justify-content: center;
    min-width: 100px;
    .title {
      font-size: 16px;
      font-size: var(--theme-font-size2, 16px);
      line-height: 24px;
      line-height: var(--theme-line-height2, 24px);
      font-weight: 600;
      color: #fff;
      margin-bottom: 4px;
    }

    .description {
      font-size: 14px;
      font-size: var(--theme-font-size1, 14px);
      line-height: 20px;
      line-height: var(--theme-line-height0, 20px);
      color: rgba(255, 255, 255, 0.85);
      min-height: 28px;
      width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .with-img {
    max-width: calc(100% - 110px);
  }
  .header-icon-box{
    height: 72px;
    width: 72px;
    // background: #fff;
    border-radius: 50%;
    margin-left: 8px;
    position: relative;
    overflow: hidden;
    bottom: 20px;
    .header-icon {
      width: 100%;
      height: 100%;
      transform: scale(1.43);
    }

  }
  .assistant-img {
    width: 110px;
    height: 110px;
    position: absolute;
    top: -30px;
    right: 12px;
  }
}
.with-img-header {
  margin-top: 40px;
  background: linear-gradient(226.31deg, #D9ECFC 0%, #D8D1F7 100%);

  .content {
    .title {
      color: #000;
    }
    .description {
      color: #000;
    }
  }
}
.signel-header-background {
  background: linear-gradient(226deg, #C9F4EF 0%, #CBDEF8 49.66%, #D8D1F7 100%);
  .content {
    .title {
      color: #000;
    }
    .description {
      color: rgba(0, 0, 0, 0.9);
    }
  }
}
</style>
