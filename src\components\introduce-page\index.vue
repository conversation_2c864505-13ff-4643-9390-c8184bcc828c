<template>
  <div class="introduce-page">
    <HistoryHeader
      v-if="state.single"
      :title="introduceTitle"
      :content="assistantInfo.introduce"
      class="history-header-margin"
    />
    <IntroduceHeader
      v-else
      class="header-margin"
      :title="introduceTitle"
      :content="assistantInfo.introduce"
    />
    <!-- v-if="assistantInfo.prologue || assistantInfo?.guideQuestions.length" -->
    <div class="guide-card-layout" :class="[state.single ? 'set-zindex' : '']">
      <GuideCard
        :guideQuestions="assistantInfo"
        :class="[state.single ? 'no-shadow' : '']"
      />
    </div>
    <!-- <QuickQuestions :quickQuestions="quickQuestions" @handleQuickQestion="handleQuickQestion" /> -->
  </div>
</template>

<script setup lang="ts">
import IntroduceHeader from './header.vue';
import GuideCard from './guide-card.vue';
import QuickQuestions from './quick-questions.vue';
import HistoryHeader from './history-header.vue';
import { computed, inject } from 'vue';
import API from '@/api';
import { AssistantInfoType } from '@/types';

const state = API.store.state;
const assistantInfo = inject('assistantInfo') as AssistantInfoType;
const introduceTitle = computed(() => {
  if (state.single) {
    return '您好，我是' + assistantInfo.name;
  }
  return '您好，我是';
});
</script>

<style lang="scss" scoped>
.introduce-page {
  display: flex;
  flex-direction: column;
  margin-top: 8px;
  .header-margin {
    margin-bottom: 24px;
  }
  .set-zindex {
    z-index: 10;
  }
  .guide-card-layout {
    flex: 1;
    .no-shadow {
      box-shadow: none;
    }
  }

  .minus-margin {
    margin-top: -10px;
  }
}
</style>
