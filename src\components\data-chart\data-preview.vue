<!-- 图表预览 -->
<template>
  <div class="data-chart-wrap" :style="computedStyle">
    <LayoutHeader>
      <DataHeader :title="content.title">
        <SwitchChart :chartType="chartType" @changeChartType="handleChartType" />
      </DataHeader>
    </LayoutHeader>
    <div class="preview-chart-container">
      <ComiEchart
        :chartType="chartType"
        :isPreview="isPreview"
        :content="JSON.parse(JSON.stringify(content))"
        v-if="showChart"
        :contentHeight="contentHeight"
      />
    </div>
  </div>
</template>
<script setup lang="ts">
import ChartContent from './data-chart.vue';
import DataHeader from '@/components/data-header/preview-header.vue';
import { computed, inject,ref } from 'vue';
import LayoutHeader from '@/components/layout/layout-header.vue';
import { ChartTableType } from '@/types';
import SwitchChart from './switch-chart.vue';
import { ComiEchart } from '@seeyon/seeyon-comi-plugins-library';
const { isPreview, content, computedStyle } = defineProps<{
  isPreview: boolean;
  content: ChartTableType;
  computedStyle: Object;
}>();

const contentHeight = inject('contentHeight');
let changeType = ref('');
// 是否显示图
const showChart = computed(() => {
  return content?.data?.length > 0;
});
const chartType = computed(() => {
  return changeType.value || content?.type;
});
function handleChartType(type: string) {
  changeType.value = type;
}
</script>
<style lang="scss">
.data-chart-wrap {
  height: 100%;
  display: flex;
  flex-direction: column;
  .data-chart-inner {
    width: 100%;
    height: 100%;
  }
  .chart-preview {
    flex: 1;
    height: auto;
  }
  .ai-header {
    padding-right: 4px !important;
    padding-left: 16px !important;
  }
  .preview-chart-container {
    padding-right: 16px;
    box-sizing: border-box;
  }
}
</style>
