<template>
  <div class="ai-header">
    <div class="ai-header-content">
      <div class="left-title" v-if="!state.single">
        <!-- <div>CoMi</div> -->
        <div
          v-for="item in headerItems"
          :key="item.key"
          @touchstart="changeHeader(item.key)"
          :class="{ 'comi-img-wrap': item.key === DIALOGUE }"
        >
          <div v-if="item.key === SIDEBAR" :class="item.icon"></div>
          <img
            v-else-if="item.key === DIALOGUE"
            :src="comiPng"
            alt=""
            :class="{ 'comi-img': true, active: item.key === activeKey }"
          />
          <span
            v-else
            :class="{
              'header-item': true,
              active: item.key === activeKey,
              'is-comi': item.key === DIALOGUE,
            }"
          >
            {{ item.name }}
          </span>
        </div>
      </div>
      <div v-else class="left-title assistant-title">
        <div class="iconfont ai-icon-zuo back-btn" @click="goBack"></div>
        <img
          v-if="assistantInfo?.iconUrl"
          :src="getAgentIconUrl(assistantInfo)"
          alt=""
          class="assistant-icon"
        />
        <div v-else class="assistant-icon-name">{{ assistantInfo?.name?.slice(0, 2) }}</div>
        <div class="assistant-name">{{ assistantInfo?.name }}</div>
      </div>
      <div class="right-icons" v-if="!showSquare">
        <div
          @click="changeSwitch"
          class="iconfont voice-icon icon-common"
          :class="[openSwitch ? 'ai-icon-bobao-mian' : 'ai-icon-a-bobaobeifen2']"
        ></div>
        <div
          @click="openNewConversation"
          class="iconfont ai-icon-a-xinjianduihua12 conversation-icon icon-common"
        ></div>
      </div>
      <div v-else @click="openSearch" class="iconfont ai-icon-m3-search search-icon"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, inject, watch } from 'vue';
import API from '@/api/index';
import { DIALOGUE, AGENT, SIDEBAR } from '@/const/const';
import {
  closeWebView,
  showOrHideInput,
  openWebView,
  setSystemUI,
  getSystemUI,
  setHistorySessionID,
} from '@/plugins/app-plugin';
import { getQueryString, getAgentIconUrl } from '@/utils/common';
import comiActived from '@/assets/images/comi_actived.png';
import comiNormal from '@/assets/images/comi_normal.png';
import { AssistantInfo } from '@/types/api';

const { openSwitch, isSearch } = defineProps({
  openSwitch: {
    type: Boolean,
    default: false,
  },
  showSquare: {
    type: Boolean,
    default: false,
  },
  isSearch: {
    type: Boolean,
    default: false,
  },
});
const state = API.store.state;
let assistantInfo: AssistantInfo | undefined = inject('assistantInfo');
const _$emit = defineEmits(['changeSwitch', 'initComi']);
const openType = getQueryString('openType');
const headerItems = [
  {
    name: 'sideBar',
    key: SIDEBAR,
    icon: 'iconfont ai-icon-cehuacaidan sidebar-setting',
  },
  {
    name: 'CoMi',
    key: DIALOGUE,
  },
  {
    name: '智能体',
    key: AGENT,
  },
];

const activeKey = computed(() => {
  return API.store.state.currentHeaderKey;
});

const comiPng = computed(() => {
  return activeKey.value === DIALOGUE ? comiActived : comiNormal;
});

/**
 * 首页tab页签切换
 * @param {*} key 对应页签key
 */
const changeHeader = (key: string) => {
  if (key === SIDEBAR) {
    if (state.isNewView) {
      goBack();
    } else {
      if(API.store.state.allCardData.length > 0 && activeKey.value === DIALOGUE){
        cleanHistorySessionIdFn(API.store.state.historySessionId);
      }else{
        cleanHistorySessionIdFn();
      }
      getSystemUI('openSlidingPane', (data: any) => {
        console.log(data, 'data');
      });
    }
    return;
  }
  API.store.action.setState('currentHeaderKey', key);
  API.store.action.setState('currentTabKey', '');

  if (key === DIALOGUE) {
    // 如果有数据不允许切换到深色
    if (!API.store.state.allCardData.length) {
      // 主页是深色
      setSystemUI({ webviewBg: 'webviewDark', webviewBgRgb: '#EDF2FC' }, 'setWebviewBg');
    } else {
      setSystemUI({ webviewBg: 'webviewLight', webviewBgRgb: '#EDF2FC' }, 'setWebviewBg');
    }
    if (state.historyModel || API.store.state.allCardData.length.length > 0) {
      cleanHistorySessionIdFn(API.store.state.historySessionId);
    } else {
      cleanHistorySessionIdFn();
    }
  } else if (key === AGENT) {
    setSystemUI({ webviewBg: '', webviewBgRgb: '#EDF2FC' }, 'setWebviewBg');
    // 设置历史记录id
    cleanHistorySessionIdFn();
  }

  // 显示隐藏原生输入框
  const params = {
    visible: key === DIALOGUE ? true : false,
  };
  showOrHideInput(params);
};
/**
 * y语音开关
 */
function changeSwitch() {
  _$emit('changeSwitch', !openSwitch);
}
/**
 * 清除历史记录 id
 */
function cleanHistorySessionIdFn(id?: string) {
  setHistorySessionID({
    historySessionId: id || '',
  });
}
/**
 * 开启新话题
 */
function openNewConversation() {
  cleanHistorySessionIdFn();
  API.action.openNewConversation();
}
/**
 * 打开搜索页面
 */
const openSearch = () => {
  const params = {
    url: '/pages/square-search/square-search.html?openType=normal&type=squareSearch',
    screenOrientation: 'portrait',
    openType: 'normal',
    webviewBg: '',
    webviewBgRgb: '#EDF2FC',
  };
  openWebView(params);
};

/**
 * 页面回退
 */
const goBack = () => {
  if (state.historyModel) {
    if (state.isNewView) {
      closeWebView(openType);
    } else {
      cleanHistorySessionIdFn();
      API.store.action.setState('single', false);
      API.action.clean();
      _$emit('initComi');
      if (activeKey.value === DIALOGUE) {
        setSystemUI({ webviewBg: 'webviewDark', webviewBgRgb: '#EDF2FC' }, 'setWebviewBg');
      } else if (activeKey.value === AGENT) {
        setSystemUI({ webviewBg: '', webviewBgRgb: '#EDF2FC' }, 'setWebviewBg');
      }
    }
    API.store.action.setState('historyModel', false);
  } else {
    setHistorySessionID({
      historySessionId: '',
    });
    closeWebView(openType);
  }
};
</script>

<style lang="scss" scoped>
.ai-header {
  padding: 0 12px;
}
.ai-header-content {
  height: 44px;
  box-sizing: border-box;
  // padding: 8px 0;
  display: flex;
  flex-direction: row;
  align-items: center;
  .left-title {
    font-family: PingFang SC;
    font-weight: 400;
    font-size: 16px;
    height: 32px;
    display: flex;
    flex: 1;
    align-items: center;
    min-width: 1px;
    .sidebar-setting,
    .back-btn {
      padding-right: 8px;
      margin-right: 12px;
      font-size: 20px;
    }

    .comi-img-wrap {
      display: flex;
      align-items: center;
    }
    .comi-img {
      height: 14px;
      margin-right: 20px;
      transform-origin: 50% 100%; /* 等价于 center bottom */
      transition: transform 0.2s;
      &.active {
        transform: scale(1.143); /* 以文字底部为中心放大 */
      }
    }
    .header-item {
      margin-right: 20px;
      transform-origin: 50% 100%; /* 等价于 center bottom */
      transition: transform 0.2s;
      color: #00000099;
      font-size: 16px;

      &.active {
        color: rgba(0, 0, 0, 1);
        color: var(--theme-font-color0, rgba(0, 0, 0, 1));
        font-weight: 600;
        font-size: 18px;
        transform: scale(1.1); /* 以文字底部为中心放大 */
      }
    }
  }
  .assistant-title {
    font-weight: 600;
    align-items: center;
    height: 28px;
    > .back-btn {
      // width: 16px;
      color: #333;
      font-size: 20px;
      font-weight: 400;
    }
    .assistant-icon {
      margin-right: 8px;
      vertical-align: middle;
      height: 28px;
      width: 28px;
      border-radius: 50%;
    }
    .assistant-icon-name {
      margin: 0 8px;
      vertical-align: middle;
      height: 28px;
      width: 28px;
      line-height: 28px;
      text-align: center;
      background-color: #4379ff;
      background-color: var(--theme-color5, #4379ff);
      color: #fff;
      text-align: center;
      border-radius: 50%;
      font-size: 10px;
    }
    .assistant-name {
      line-height: 25px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      word-break: keep-all;
      flex: 1;
      min-width: 1px;
    }
  }
  .right-icons {
    display: flex;
    flex-direction: 1;
    align-items: center;
    .icon-common {
      width: 20px;
      height: 20px;
      overflow: hidden;
      text-align: right;
      font-size: 20px;
    }
    .voice-icon {
      margin: 0 12px;
    }
    .conversation-icon {
      color: rgba(0, 0, 0, 1);
      color: var(--theme-font-color0, rgba(0, 0, 0, 1));
    }
  }
  .ai-icon-bobao-mian {
    color: #4379ff;
    color: var(--theme-color5, #4379ff);
  }
  .ai-icon-a-bobaobeifen2,
  .search-icon {
    color: rgba(0, 0, 0, 0.9);
    color: var(--theme-font-color1, rgba(0, 0, 0, 0.9));
  }
  .search-icon {
    font-size: 20px;
  }
}
</style>
