import { RequestDataType } from '@/types/api';
import http from './http';

// 获取平台所有助手
function getAssistants(params: object): Promise<RequestDataType> {
  const url = '/ai-manager/assistant/info/app/page';
  const newParams = {
    pageInfo: {
      pageNumber: 1,
      pageSize: 1000,
      needTotal: true,
    },
    params,
  };

  return http.post(url, newParams, {});
}
// 获取应用广场
function getAISquare(): Promise<RequestDataType> {
  const url = '/ai-manager/label/info/type/0/used';
  const params = {};
  return http.get(url, params);
}
// 中断请求
function interruptRequest(params: object): Promise<RequestDataType> {
  const url = '/ai-manager/assistant/info/call/cancel';
  return http.post(url, params, {});
}

//获取欢迎页和推荐问题
function getRecommandData(id: string): Promise<RequestDataType> {
  let url = `/ai-manager/assistant/info/${id}/prologue`;
  // const url = '/ai-manager/assistant/info/4723361644406721673/prologue';
  const params = {};
  return http.get(url, params);
}

//获取历史记录的信息
function getHistoryData(params: object, options: object): Promise<RequestDataType> {
  const url = '/ai-manager/assistant/info/session/runStep/page';
  return http.post(url, params, options);
}

/**
 * 获取助手详情
 * @param {*} id 助手id
 * @returns
 */
function getAssistantInfo(id: string): Promise<RequestDataType> {
  const url = `/ai-manager/assistant/info/${id}`;
  const params = {};
  return http.get(url, params);
}
/**
 * 点踩点赞
 * @param {*} type 0 点赞 1 点踩, messageId  消息id content
 * @returns
 */

function evaluate(params: object): Promise<RequestDataType> {
  const url = '/ai-manager/assistant/info/evaluate';
  return http.post(url, params, {});
}
/**
 * 帮助反馈
 * @param {*} content 反馈内容，contactMethod 联系方式
 * @returns
 */

function feedbackInfo(params: object): Promise<RequestDataType> {
  const url = '/api/biz/feedback';
  return http.post(url, params, {});

}

/**
 * 获取登录的token
 * @param {*} clientUid string客户端唯一标识 loginName password terminal loginType
 * @returns
 */

function getLoginInfo(params: object): Promise<RequestDataType> {
  const url = '/api/auth/login';
  return http.post(url, params, {});

}
/**
 * 获取当前登录用户
 * @returns
 */

function getCurrentUser(params = {}): Promise<RequestDataType> {
  const url = '/api/auth/current-user';
  return http.get(url, params);

}

/**
 * 获取超级助手信息
 * @returns 
 */
function getgGeneralAsInfo(): Promise<RequestDataType> {
  const url = '/ai-manager/assistant/info/type/general';
  return http.get(url, {});
}
/**
 * 日志记录
 * @param params 
 * @returns 
 */
function logRecord(params = {}): Promise<RequestDataType>  {
  const url = `/api/front-log/save`;
  return http.post(url, params, {});
}

/**
 * 保存或更新我的设置
 * @param params 
 * @returns 
 */

function saveOrUpdateSetting(params = {}): Promise<RequestDataType>  {
  const url = `/api/config/personal-settings/save-or-update`;
  return http.post(url, params, {});
}

/**
 * 获取我的设置
 * @param params 
 * @returns 
 */
function getPersonalSetting(params = {}): Promise<RequestDataType>{
  const url = `/api/config/personal-settings/find-my`;
  return http.get(url, params);
}
/**
 * 获取三方token
 */

function getThirdToken(params={}): Promise<RequestDataType> {
  const url = `/api/auth/get-third-token`;
  return http.get(url, params);
}
/**
 * 重新生成及复制记录日志接口
 */
function regenerateAssistant(params={}): Promise<RequestDataType> {
  const url = `/ai-manager/assistant/info/regenerateAssistant`;
  return http.post(url, params);
}

/**
 * 获取历史会话记录列表
 */
function getHistortChatList(params={}): Promise<RequestDataType> {
  const url = `/ai-manager/assistant/info/session/record/page`;
  return http.post(url, params);
}

/**
 * 获取历史会话单条记录详情
 * @param {*} params.sessionId 单条历史会话记录 ID
 * @returns
 */
function getHistortChatById(params={}): Promise<RequestDataType> {
  const url = `/ai-manager/assistant/info/session/runStep/page`;
  return http.post(url, params);
}

/**
 * 知识源下载接口
 */
function downloadFile(fileId='', shortToken:string ,params={}): Promise<BlobPart> {
  const url = `/ai-manager/repository/export/download/file/${fileId}?token=${shortToken}`;
  return http.get(url, params);
}

/**
 * 协同融合获取人员信息
 */

function getCollaborationUser(params={}): Promise<RequestDataType> {3
  const url = `/api/biz/coll/data`;
  return http.post(url, params);
}
/**
 * 获取公文的affaridId
 */
function getEdocAffairId(id: string): Promise<RequestDataType> {
  const url = `/seeyon/rest/seeyon-comi/index/get-link?categorys=4&ids=${id}`;
  return http.get(url, {});
}
function getShortToken() {
  const url = `/api/auth/get-short-token`;
  return http.get(url, {});
}
function getSessionId (params: any){
  return http.post(`ai-manager/assistant/info/getCurrentSessionId`, params)
}

/**
 * 获取知识源内容详情
 * @returns
 */
function searchKnowledgeSource(params: object): Promise<RequestDataType> {
  const url = '/ai-manager/repository/knowledge/source/search';
  return http.post(url, params);
}

function classificationAll(): Promise<RequestDataType> {
  const url = '/seeyon/rest/seeyon-ai/comi/pending-section/classificationAll?option.n_a_s=1';
  return http.get(url, {});
}

function pendingDataByCategory(pageNumber: number = 1, pageSize: number = 20, portletParams: object): Promise<RequestDataType> {
  const url = `/seeyon/rest/seeyon-ai/comi/pending-section/portlet/${pageNumber}/${pageSize}?option.n_a_s=1`;
  return http.post(url, portletParams);
}

export default {
  getAssistants,
  interruptRequest,
  getRecommandData,
  getHistoryData,
  getAISquare,
  getAssistantInfo,
  evaluate,
  feedbackInfo,
  getLoginInfo,
  getCurrentUser,
  getgGeneralAsInfo,
  logRecord,
  saveOrUpdateSetting,
  getPersonalSetting,
  getThirdToken,
  regenerateAssistant,
  getHistortChatList,
  getHistortChatById,
  downloadFile,
  getCollaborationUser,
  getEdocAffairId,
  getShortToken,
  getSessionId,
  searchKnowledgeSource,
  classificationAll,
  pendingDataByCategory
};
