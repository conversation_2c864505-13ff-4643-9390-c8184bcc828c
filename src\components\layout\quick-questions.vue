
<template>
  <div class="quick-question">
    <!-- 图标，暂时用div占位 -->
    <div class="header">
      <div class="icon"></div>
      <div class="title">更多能力等你探索:</div>
    </div>

    <div class="question-container">
      <div class="question-scrollbar">
        <div v-for="item in quickQuestions" :key="item.id" @click="handleQuickQestion(item)" class="question-item">
          <div class="item-title">
            <div class="text ellipsis">{{item.subject}}</div>
            <div class="item-title-icon"></div>
          </div>

          <div class="item-content">{{item.content}} </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  type quickQuestionsItem =  {
    id: string;
    subject: string;
    content: string;
  }
  const props = defineProps({
    quickQuestions: {
      type: Array as () => quickQuestionsItem [],
      default: () => ([])
    }
  });
  function handleQuickQestion(item: quickQuestionsItem ) {
    console.log(item);
    
  }
</script>

<style lang="scss" scoped>
  .quick-question {
    padding-bottom: 8px;
    .header {
      display: flex;
      align-items: center;
      font-size: 14px;
      font-size: var(--theme-font-size1, 14px);
      margin-bottom: 8px;

      .icon {
        height: 16px;
        width: 16px;
        border-radius: 50%;
        background: #2a69fe;
        margin-right: 2px;
      }

      .title {
        flex: 1;
        color: #666;
        color: var(--theme-font-color3, #666);
      }
    }

    .question-container {
      // overflow-x: auto;
      // display: flex;
      // flex-wrap: nowrap;
      //隐藏滚动条方案兼容性测试
      overflow: hidden;
      height: 92px;
      .question-scrollbar {
        overflow-x: auto;
        display: flex;
        flex-wrap: nowrap;
        padding-bottom: 10px;
      }
      .question-item {
        padding: 8px 12px 12px;
        border-radius: 8px;
        width: 128px;
        height: 92px;
        box-sizing: border-box;
        border: 1px solid rgba(255, 255, 255, 0.6);
        background: linear-gradient(151.14deg, rgba(108, 174, 255, 0.2) 8.74%, rgba(239, 243, 255, 0) 76.96%),linear-gradient(0deg, #FFFFFF, #FFFFFF);
        margin-right: 8px;
        &:last-child {
          margin-right: 0;
        }
      }

      .item-title {
        display: flex;
        margin-bottom: 4px;
        align-items: flex-end;

        .text {
          flex: 1;
          color: #000;
          color: var(--theme-font-color0, #000);
          font-size: 14px;
          font-size: var(--theme-font-size1, 14px);
          font-weight: 600;
          line-height: 22px;
          line-height: var(--theme-line-height1, 22px);
        }
        .item-title-icon {
          height: 32px;
          width: 32px;
          margin-left: 5px;
          background-color: #2a69fe;
          border-radius: 50%;
        }
      }

      .item-content {
        color: #666;
        color: var(--theme-font-color3, #666);
        font-size: 12px;
        font-size: var(--theme-font-size0, 12px);
        line-height: 20px;
        line-height: var(--theme-line-height0, 20px);
        font-weight: 400;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
      }

    }
  }

  // ::-webkit-scrollbar {
  //   display: none;
  // }
</style>