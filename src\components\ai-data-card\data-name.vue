<template>
  <div class="data-name">
  <!-- 头像，先用一个圆代替 -->
    <img v-if="agentInfo?.iconUrl" :src="iconUrl" class="name-icon"/> 
    <div v-else class="name-icon default-name">{{defaultName}}</div>
    <div class="name">{{agentInfo.name}}</div>
  </div>
</template>

<script setup lang="ts">
  import {ref} from 'vue';
  import {AssistantInfo} from '@/types/api';
  import { getAgentIconUrl } from '@/utils/common';
  // import store from '@/api/store';
  const props = defineProps<{
    data: any,
  }>();
  const agentInfo: AssistantInfo  = props.data.assistantInfo || props.data.agentInfo;
  const defaultName = agentInfo.name?.slice(0, 2) || 'Comi';
  
  const iconUrl = getAgentIconUrl(agentInfo);
</script>

<style lang="scss" scoped>
  .data-name {
    display: flex;
    margin-bottom: 8px;
    user-select: none;
    .default-name {
      width: 22px;
      height: 22px;
      background-color: #2a69fe;
      text-align: center;
    }
    .name-icon{
      height: 22px;
      // background-color: #2a69fe;
      margin-right: 6px;
      border-radius: 50%;
      font-size: 10px;
      color: #fff;
      line-height: 22px;
      width: 22px;
    }

    .name {
      flex: 1;
      color: rgba(0, 0, 0, 0.6);
      color: var(--theme-font-color2, rgba(0, 0, 0, 0.6));
      font-size: 14px;
      font-size: var(--theme-font-size1, 14px);
      line-height: 22px;
      line-height: var(--theme-line-height1, 22px);
    }
  }
</style>
