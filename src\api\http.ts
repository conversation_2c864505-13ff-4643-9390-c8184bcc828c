import axios from 'axios';
import utils from '@/utils';
import { MAX_RETRY_COUNT, BASEURL } from '@/const/const';
import { aiToast } from '@/plugins/ai-toast';

interface RequestItem {
    fn: () => Promise<any>;
    resolve: (value: any) => void;
    reject: (reason?: any) => void;
}

const requestQueue: RequestItem[] = [];


// 修改现有axios实例的baseURL
axios.defaults.baseURL = BASEURL;
const runRequestQueue = () => {
    if (requestQueue.length > 0) {
        const newAxiosFn = requestQueue.shift();
        if (newAxiosFn) {
            newAxiosFn.fn().then(response => {
                newAxiosFn.resolve(response);
            }).catch(error => {
                newAxiosFn.reject(error);
            }).finally(() => {
                runRequestQueue();
            })
        }
    }

};

axios.interceptors.request.use(config => {
    if(localStorage.getItem('tokenStr')){
        config.headers['Authorization'] = 'Bearer ' + localStorage.getItem('tokenStr');
    }
    return config;
}, function (error) {
    return Promise.reject(error);
});
axios.interceptors.response.use(res => {
    const { status, data } = res;
    const { code } = data;
    // if (code == '0' || code == 0){
    //     if(data.data && data.data.hasOwnProperty('success')){
    //         if(data.data.success){
    //             return data.data.data;
    //         }else{
    //             if(data.data.errorMsg){
    //                 alert(data.data.errorMsg.msg);
    //             }else{
    //                 alert('请求错误！');
    //             }
    //         }
    //     }else{
    //        return data.data;
    //     }
    // }
    return data;
}, (err) => {
    let config = err.config;
    const status = Number(err.response?.status);
    config.retryCount = (config.retryCount || 0);
    if (status === 401 && config.retryCount < MAX_RETRY_COUNT) {
        config.retryCount++;
        return new Promise((resolve, reject) => {
            const request = {
                fn: function () {
                    return axios(config);
                },
                resolve,
                reject
            };
            requestQueue.push(request);
            if (requestQueue.length === 1) {
                //重新获取token, 重新请求
                utils.getNewToken(runRequestQueue);
            }
        });
    } else {
        if (status === 500 && err.response.data?.message) {
            aiToast({
                content: err.response.data?.message
            });
        } else if (!navigator.onLine) {
            aiToast({
                content: '网络连接失败，请检查网络设置'
            });
        }else {
            aiToast({
                content: 'comi不小心发生错误啦，请稍后重试'
            });
        }
        Promise.reject(err);
    }
});

export default axios;
