<template>
  <div class="square-container" ref="squareContainer">
    <div
      ref="squareList"
      class="square-list"
      @touchstart="squareTouchStart"
      @touchmove="squareTouchMove"
      @touchend="squareTouchEnd"
      @scroll="squareTouchMove"
    >
      <div
        :class="['square-item', API.store.state.currentTabKey === item.id ? 'active' : '']"
        v-for="(item, index) in squareData"
        :key="item.id"
        ref="square_items"
        @click="selectAgent(item.id, index)"
      >
        {{ item.name }}
      </div>
    </div>
    <div v-show="isShowBlur && showLeft" class="blur left-position"></div>
    <div v-show="isShowBlur && showRight" class="blur right-position"></div>
    <div class="square-content">
      <template v-if="!loading">
        <template v-if="assistantList.length">
          <AssistantListItem
            v-for="(item, index) in assistantList"
            :key="item.id"
            :item="item"
            :isLast="index === assistantList.length - 1"
          />
        </template>
        <Empty v-else text="这里空空如也" />
      </template>
      <Loader v-else />
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, reactive, watch, onMounted, nextTick } from 'vue';
import API from '@/api/index';
import AssistantListItem from './assistant-list-item.vue';
import Empty from './empty.vue';
import Loader from './loader.vue';
import { getLocalStorage } from '@/utils/common';
import { mainWebPageSlide } from '@/plugins/app-plugin';
import { AssistantInfoType } from '@/types';

type SquareDataType = {
  id: string;
  name: string;
};
const props = defineProps({
  showSquare: {
    type: Boolean,
    default: false,
  },
});
const squareData = ref<SquareDataType[]>([]);
const assistantList = ref<AssistantInfoType[]>([]);
const square_items = ref<HTMLElement[]>([]);
const squareList = ref<HTMLElement | null>(null);
// 数据加载
const loading = ref<boolean>(false);
const squareContainer = ref<HTMLElement | null>(null);
let isShowBlur = ref<boolean>(false);
let showLeft = ref<boolean>(false);
let showRight = ref<boolean>(false);
let slideFlag: boolean = false;
onMounted(() => {
  // 显示智能体页签的时候加载数据
  watch(
    () => props.showSquare,
    val => {
      if (val) {
        getAISquareData();
      }
    }
  );
});

/**
 * 请求缓存的最近点击助手列表
 * @param {*} type recent || ''
 */
function queryDBData(type = '') {
  const data = getLocalStorage('recentAssistantList');
  loading.value = false;
  if (data) {
    if (type === 'recent') {
      assistantList.value = data;
    } else {
      if (!squareData.value.some(item => item.id === 'recent')) {
        squareData.value.unshift({
          id: 'recent',
          name: '最近',
        });
      }
    }
  }
}

/**
 * 获取平台页签
 */
async function getAISquareData() {
  squareData.value = [];
  loading.value = true;
  API.requests.getAISquare().then(async res => {
    loading.value = false;
    if (Number(res?.code) === 0 && res?.data?.length) {
      // queryDBData();
      const oData = res.data.filter((item: any) => !item.name.includes('/'))
      squareData.value = squareData.value.concat(oData);
      if (!API.store.state.currentTabKey) {
        selectAgent(squareData.value[0].id);
      }
      await nextTick;
      const squareListScroll = squareList.value?.scrollWidth || 0;
      const clientWidth = squareContainer.value?.clientWidth || 0;
      if (squareListScroll > clientWidth - 24) {
        isShowBlur.value = true;
        showRight.value = true;
      }
    }
  });
}

/**
 * 选择智能体页签, 请求对应的智能体
 * @param {*} id 页签id
 */
function selectAgent(id: string, idx?: number) {
  API.store.action.setState('currentTabKey', id);
  // 点击页签，滚动到可视区域
  if (square_items.value?.length && idx !== undefined && square_items.value[idx]) {
    square_items.value[idx].scrollIntoView({
      behavior: 'smooth',
      block: 'center',
      inline: 'center',
    });
  }
  // activeKey.value = id;
  loading.value = true;
  // if (id === 'recent') {
  //   queryDBData(id);
  //   return;
  // }
  const params = {
    labelId: id,
    keyword: '',
    assistantType: 0,
  };
  // 获取助手列表
  API.requests.getAssistants(params).then(res => {
    loading.value = false;
    if (Number(res?.code) === 0 && res?.data?.content?.length) {
      assistantList.value = res.data.content;
    }
  });
}

function squareTouchStart() {
  mainWebPageSlide({ canSlide: false });
  slideFlag = false;
}

function squareTouchMove(e: any) {
  const squareListDom = squareList.value;
  const scrollLeft = squareListDom?.scrollLeft || 0;
  const clientWidth = squareListDom?.clientWidth || 0;
  const caculateScroll = (squareListDom?.scrollWidth || 0) - (scrollLeft || 0);
  //避免重复多次调用
  const isNeedSlide = function () {
    if (slideFlag) {
      mainWebPageSlide({ canSlide: false });
      slideFlag = false;
    }
  };
  const setShow = function (showLeftValue: boolean, showRightValue: boolean) {
    showLeft.value = showLeftValue;
    showRight.value = showRightValue;
  };
  //滑到最左边
  if (scrollLeft === 0) {
    setShow(false, true);
    if (!slideFlag) {
      mainWebPageSlide({ canSlide: true });
      slideFlag = true;
    }
  } else if (Math.abs(caculateScroll - clientWidth) < 1) { //滑动到右边，兼容有些渲染小数的问题
    setShow(true, false);
    isNeedSlide();
  } else if (scrollLeft > 0 && caculateScroll > clientWidth) {
    setShow(true, true);
    isNeedSlide();
  }
}
function squareTouchEnd() {
  mainWebPageSlide({ canSlide: true });
  slideFlag = true;
}
</script>
<style lang="scss" scoped>
.square-container {
  padding: 0 12px 12px 12px;
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 1px;
  position: relative;

  .square-list {
    display: flex;
    height: 48px;
    align-items: center;
    overflow-x: auto;
    width: auto;
    max-width: 100%;
    .square-item {
      word-break: keep-all;
      text-align: center;
      line-height: 22px;
      border-radius: 8px;
      background: #ffffff;
      margin-right: 8px;
      font-size: 14px;
      font-family: PingFang SC;
      font-weight: 400;
      letter-spacing: 0%;
      padding: 5px 8px;
      white-space: nowrap;
      &:last-child {
        margin-right: 0;
      }

      &.active {
        color: #2962f0;
        background: #d1e0ff;
        font-weight: 600;
      }
    }
  }
  .blur {
    position: absolute;
    width: 20px;
    height: 48px;
    top: 0;
  }
  .right-position {
    background: linear-gradient(270deg, #edf2fc 0%, rgba(237, 242, 252, 0) 100%);
    right: 11px;
  }
  .left-position {
    background: linear-gradient(90deg, #edf2fc 0%, rgba(237, 242, 252, 0) 100%);
    left: 11px;
  }
  .square-content {
    margin-top: 12px;
    overflow: auto;
    flex: 1;
    // max-height: calc(100% - 104px);
    // height: calc(100% - 120px);
    // height: 200px;
  }
}
</style>
