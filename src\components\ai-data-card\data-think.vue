<template>
  <div class="data-think">
    <div v-for="(process, index) in processData" :key="index" class="think-item">
      <ProcessHeader :process="process" @expendOrCollapse="expendOrCollapse" />
      <div v-if="showThink(process)" class="process-content">
        <div class="left"></div>
        <div class="right">{{process?.content}}</div>
      </div>
      <div v-if="showExec(process)" class="exec-process">
        <AgentExecute :agentProcess="process?.content"/>
      </div>
      <div v-if="process?.type === 'exec' && !process?.loading" class="process-answer-split"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import AgentExecute from '@/components/ai-data-card/agent-execute.vue';
  import ProcessHeader from '@/components/ai-data-card/process-header.vue';
  import {ProcessDataItem} from '@/types/api'
  const props = defineProps({
    processData: {
      type: Array<ProcessDataItem>,
      default: () => ([])
    }
  });

  function expendOrCollapse(process:ProcessDataItem) {
    process.expand = !process.expand;
  }
  
  function showThink(process: ProcessDataItem) {
    return process && process.content && process.expand && process.type === 'think';
  }

  function showExec(process: ProcessDataItem) {
    return process && process.content && process.expand && process.type === 'exec';
  }
</script>

<style lang="scss" scoped>
.data-think {
  color: rgba(0, 0, 0, 0.6);
  color: var(--theme-font-color2, 0, 0, 0, 0.6);
  font-size: 14px;
  font-size: var(--theme-font-size1, 14px);
  line-height: 22px;
  line-height: var(--theme-line-height1, 22px);
  .think-item:not(:first-child) {
    margin: 8px 0;
  }
  .process-content {
    display: flex;
    margin-top: 8px;
    .left {
      width: 14px;
      margin-right: 4px;
      &::after {
        content: '';
        display: block;
        width: 2px;
        background: #D8DADF;
        height: 100%;
        position: relative;
        left: 6px;
      }
    }
    .right{
      flex: 1;
    }
  }
  .process-answer-split {
    width: 100%;
    margin-bottom: 12px;
    margin-top: 12px;
    height: 1px;
    background-color: #D9D9D9;
    transform: scaleY(0.5);
  }
  .exec-process {
    margin-top: 8px;
  }
}
</style>