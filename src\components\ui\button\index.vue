<template>
  <div class="ai-button">
    <button
         @click="buttonClick" 
         class="common"
         :class="type"
         :disabled="disabled">
      {{ buttonText }}
    </button>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  buttonText: {
    type: String,
    default: '',
  },
  type: {
    type: String,
    default: 'default',
  },
  disabled: {
    type: Boolean,
    default: false,
  },
});
const $emit = defineEmits(['buttonAction']);
function buttonClick() {
  $emit('buttonAction');
}
</script>

<style lang="scss" scoped>
.ai-button {
  .common {
    width: 100%;
    height: 44px;
    text-align: center;
    border-radius: 22px;
    line-height: 44px;
    font-size: 16px;
    box-sizing: border-box;
  }
  .primary {
    color: #fff;
    background-color: #4379ff;
    border: none;
  }
  .default {
    color: #4379ff;
    border: 1px solid #4379ff;
    background: transparent;
  }
  button:disabled {
    opacity: 0.45 !important;
  }
  button.primary:not(:disabled):active{
    background-color: #154BD4;
  }
  button.default:not(:disabled):active{
    border: 1px solid #154BD4;
    color: #154BD4;
  }
}
</style>