import { createApp, h, ref, type App, type Component } from 'vue';

import UnlikeFeedback from '@/components/unlike-feedback/index.vue';
type feedBackData = {
  feedbackMessage: string, 
  type: number|string
}
type Options = {
  onConfirm?: (data: feedBackData) => void;
  onClose?: () => void;
}

export const feedbackDrawer = (options: Options): void => {
  // 创建容器
  const container: HTMLElement = document.createElement('div');
  document.body.appendChild(container);

  // 创建 feedbackDrawer 组件实例
  const drawerApp: App<Element> = createApp({
    setup() {
      const isShowDrawer = ref(true);
      //确认回调
      const handleConfirm = (feedbackData:feedBackData): void => {
        if (options.onConfirm) {
          options.onConfirm(feedbackData);
        }
      };
      //取消回调
      const handleClose = (): void => {
        if (options.onClose) {
          options.onClose();
        }
      };

      return {
        isShowDrawer,
        handleConfirm,
        handleClose,
      };
    },
    render() {
      return h(UnlikeFeedback as Component, {
        isShowDrawer: this.isShowDrawer,
        onConfirm: this.handleConfirm,
        onClose: this.handleClose,
        'onUpdate:isShowDrawer': (value:boolean) => {
          if (!value) {
            destroy();
          }
          this.isShowDrawer = value;
        },
      });
    },
  });

  // 渲染 feedbackDrawer
  drawerApp.mount(container);

  // 销毁 feedbackDrawer
  function destroy() {
    drawerApp.unmount();
    document.body.removeChild(container);
  }
};

export default {
  feedbackDrawer,
  install(app: App) {
    // 全局挂载方法
    app.config.globalProperties.$feedbackDrawer = feedbackDrawer;
  },
};
