<template>
  <div class="switch-chart">
    <div
      class="switch-item"
      v-for="item in renderSwitchList"
      :key="item.type"
      @click="checkedChart(item)"
      :class="{ 'checked-switch': item.checked }"
    >
      <i class="switch-icon iconfont" :class="item.icon"></i>
      <span>{{ item.name }}</span>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
type SwitchItem = {
  name: string;
  type: string;
  checked: boolean;
};
const props = defineProps({
  chartType: {
    type: String,
    default: 'bar_chart',
  },
});
const _$emit = defineEmits(['changeChartType']);
const switchList = [
  {
    name: '柱状图',
    type: 'bar_chart',
    checked: true,
    icon: 'ai-icon-zhuxingtu'
  },
  {
    name: '折线图',
    type: 'line_chart',
    checked: false,
    icon: 'ai-icon-zhexiantu'
  },
  {
    name: '饼图',
    type: 'pie_chart',
    checked: false,
    icon: 'ai-icon-bingtu'
  },
];
let lastItem: SwitchItem = {
  name: '',
  type: '',
  checked: false,
};
const renderSwitchList = computed(() => {
  return switchList.map((item) => {
    if (item.type === props.chartType) {
      lastItem = item;
      item.checked = true;
    } else {
      item.checked = false;
    }
    return item;
  });
});

function checkedChart(item: SwitchItem) {
  if (lastItem.type === item.type) {
    return;
  }
  _$emit('changeChartType', item.type)
}
</script>

<style lang="scss" scoped>
.switch-chart {
  padding: 0 12px;
  .switch-item {
    // border: 1px solid #e3e3e3;
    border-radius: 4px;
    padding: 0px 4px;
    color: rgba(0, 0, 0, 0.9);
    color: var(--theme-font-color1, rgba(0, 0, 0, 0.9));
    font-size: 14px;
    font-size: var(--theme-font-size2, 14px);
    margin-right: 8px;
    // margin-bottom: 8px;
    height: 22px;
    line-height: 22px;
    box-sizing: border-box;
    display: inline-block;
    &:last-child {
      margin: 0;
    }
  }
  
  .switch-icon {
    margin-right: 4px;
  }
  .checked-switch {
    // border-color: #aac6ff;
    background: #edf2fc;
    color: #4379ff;
  }
}
</style>
