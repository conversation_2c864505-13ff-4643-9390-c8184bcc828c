// 卡片数据类型
export interface CardDataType {
  _id?: number | string;
  _index: number | string;
  componentKey: string
  staticData?: any
  config?: any
  data: any
}
// 数据流类型
export interface StreamFnInstance {
  card: any;
  isStop: boolean;
  timer: any;
  _id: number;
  startTime: number;
  option: any;
  dataHistory: any[];
  isCancel: boolean;
  messageType: Record<string, boolean>;
  retry: number;
  run: (option: any) => Promise<void>;
  stop: (isNormal?: boolean) => void;
}

// 返回的数据结构
export interface RequestDataType {
  code: string | number;
  data: any;
  message: string | null;
  status?: number;
  success?: boolean;
}

export interface CardContentType {
  isCard?: boolean;
  finish?: undefined | number,
  context: any;
  index?: number
  isIframe?: boolean,
  json?: any,
  isKnowledgeData?: any
  tagData?: any
}
export interface AgentInfo extends CardDataType {
  data: {
    agentInfo: object
  }
}
export interface AiRequestCard extends CardDataType {
  data: {
    isLoading: boolean,
    isError: boolean,
  }
}

export interface AiLoadingCard extends CardDataType {
  data: {
    content: string,
    isLoading: boolean
  }
}

export interface AiDataCard extends CardDataType {
  data: {
    cardData: CardContentType[]
    messageId?: string | number,
    illageSessionType?: string | number,
    isCompleted?: boolean,
    isPlay?: boolean,
    isStop?: boolean,
    isUnlike?: boolean,
    knowledgeData?: Array<any>,
    recommandQuestion?: Array<object>,
    processData: any [],
  }
}

export interface AgentProcessItem {
  id: number | string,
  complete: boolean,
  loading: boolean,
  title: string,
}
export interface ProcessDataItem {
  content?: Array<AgentProcessItem>,
  expand?: boolean,
  type?: string,
  loading?: boolean,
}
export interface AssistantInfo {
  name?: string,
  iconUrl?: string,
}

export interface BaseType {
  [key:string]: string
}

export interface FileType{
  fileNumber: string,
  fileUrl: string,
  name: string,
  size: string,
}

export interface paddingType {
  paddingTop?: string,
  paddingBottom?: string,
  paddingLeft?: string,
  paddingRight?: string,
}
export interface KnowledgeDataItem {
  id: string;
  label: string;
  type: string;
  number: number;
  knowledgeArr: Array<{ // 添加knowledgeArr属性定义
    id: number;
    title: string;
    type: string;
    iconClass: string;
    accessoryName: string;
    author: string;
    resourceId: null | string;
    similarity: number;
    entityId: null | string;
    accessory: null | string;
    createDate: string;
    content: string;
    appTypeDsc: string;
  }>;
}