<template>
  <div class="ai-footer">
    <div v-if="showDefault" 
         @click="buttonAction">
      <AiButton :disabled="disabled" :buttonText="buttonText" :type="type">
      </AiButton>
    </div>
    <div class="ai-footer-content">
      <slot></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import AiButton from '@/components/ui/button/index.vue'
const props = defineProps({
  buttonText: {
    type: String,
    default: '',
  },
  showDefault: {
    type: Boolean,
    default: true,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  type: {
    type: String,
    default: 'default',
  },
});
const _$emit = defineEmits(['buttonAction']);
function buttonAction() {
  if(props.disabled) return;
  _$emit('buttonAction');
}
</script>

<style lang="scss" scoped>
.ai-footer {
  padding-bottom: 34px;

  .default-button {
    width: 100%;
    height: 44px;
    text-align: center;
    border-radius: 22px;
    color: #4379ff;
    border: 1px solid #4379ff;
    font-size: 16px;
    // margin: 8px 0;
    box-sizing: border-box;
    line-height: 44px;
  }
}
</style>
