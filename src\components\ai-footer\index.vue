<template>
  <div class="message-input-container">
    <div
      :class="['text-input-container', { 'text-input-container-has-message': hasMessage }]"
      :disabled="true"
    >
      <div class="quick-input-btn" v-if="!hasMessage" @click="quickInput">
        <span class="quick-icon iconfont ai-icon-xingxing"></span>
      </div>
      <textarea
        :class="['text-input', { 'has-message': hasMessage }]"
        placeholder="给CoMi发送消息"
        v-model="message"
        @input="adjustHeight"
        ref="textarea"
        @pressEnter="send"
        rows="1"
        v-if="!isVoiceInput"
        :disabled="isLoading"
      />
      <div class="voice-input" v-else @click="requestVoiceInput()">按住 说话</div>
      <div class="send-btn iconfont ai-icon-fasong" @click="send" v-if="hasMessage" />
      <div
        v-else
        :class="[
          'shuru-btn iconfont',
          ` ${isVoiceInput ? 'ai-icon-wenzishuru' : 'ai-icon-yuyinshuru'}`,
        ]"
        @click="changeVoiceIcon"
      />
    </div>
    <div class="call-btn" v-if="!hasMessage && !isLoading" @click="call">
      <div class="iconfont ai-icon-dianhua-mian" />
    </div>
    <div class="pause-btn" v-if="isLoading" @click="pauseRequest">
      <div class="pause-btn-inner" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import API from '@/api/index';
const { disabled } = defineProps({
  disabled: {
    type: Boolean,
    default: false,
  },
});

let message = ref('');
let textarea = ref(null);
let isVoiceInput = ref(false);

const hasMessage = computed(() => message.value.trim().length > 0);

const isLoading = computed(() => {
  return API.store.state.isLoading;
});

function adjustHeight() {
  if (textarea.value) {
    let textareaDom:HTMLElement = textarea.value;
    const computedStyle = getComputedStyle(textareaDom);
    const lineHeight = parseInt(computedStyle.lineHeight, 10);
    textareaDom.style.height = 'auto';

    if (parseInt(computedStyle.height, 10) < textareaDom.scrollHeight) {
      textareaDom.style.height = `${textareaDom.scrollHeight}px`;
    }
    textareaDom.scrollTop = Math.ceil(textareaDom.scrollTop / lineHeight) * lineHeight;
  }
}
function send() {
  if (hasMessage.value) {
    API.action.sendMessage(message.value);
    message.value = '';
    requestAnimationFrame(() => {
      adjustHeight();
    });
  }
}

function quickInput() {
  console.log('quickInput');
}
function requestVoiceInput() {
  if (isLoading.value) {
    return;
  }
  console.log('调用原生插件');
}

async function pauseRequest() {
  API.action.interruptAction();
}

function changeVoiceIcon() {
  if (isLoading.value) {
    return;
  }
  isVoiceInput.value = !isVoiceInput.value;
}

function call() {
  // TODO: call
}
</script>

<style lang="scss" scoped>
.message-input-container {
  display: flex;
  box-sizing: border-box;
  align-items: center;

  padding: 8px 14px;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.5) 0%, rgba(255, 255, 255, 0) 36%);
  gap: 14px;
  border-radius: 8px 8px 0 0;

  .text-input-container {
    display: flex;
    box-sizing: border-box;
    flex-direction: row;
    flex: 1;
    min-height: 44px;
    background: white;
    padding: 6px 12px 6px 6px;
    border-radius: 22px;
    align-items: center;
    max-height: 144px;
    .quick-input-btn,
    .send-btn {
      display: flex;
      width: 36px;
      height: 36px;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      background: #eff3ff;
      cursor: pointer;
      flex-shrink: 0;
      .iconfont {
        margin: auto;
        color: #333333;
      }

      .quick-icon {
        color: #8692ff;
      }
    }
    .send-btn {
      background: linear-gradient(99.3deg, #2a69fe 0%, #8692ff 52.29%, #a1e8ff 99.29%);
      color: #ffffff;
    }
    .shuru-btn {
      display: flex;
      align-items: center;
      font-size: 24px;
      color: #333333;
    }

    .text-input,
    .voice-input {
      border: none;
      outline: none;
      background: transparent;
      font-size: 14px;
      resize: none;
      height: 28px;
      line-height: 28px;
      width: 100%;
      // max-height: calc(100vh - 250px);
      max-height: 132px;
      caret-color: #7559f8;
    }
    .text-input {
      margin-left: 12px;
      &::placeholder {
        color: #d4d4d4;
      }

      &::selection {
        background: rgba(117, 89, 248, 0.8);
      }
    }
    .has-message {
      padding: 0 12px;
      margin-left: 0px;
    }
    .voice-input {
      text-align: center;
    }
  }
  .text-input-container-has-message {
    padding: 6px;
  }
  .call-btn,
  .pause-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    line-height: 40px;
    text-align: center;
    background: linear-gradient(142.7deg, #f5f8ff 14.86%, #ffffff 85.51%);
    color: #4379ff;
  }
  .call-btn {
    .iconfont {
      font-size: 19px;
    }
  }
  .pause-btn {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .pause-btn-inner {
    width: 16px;
    height: 16px;
    border-radius: 4px;
    background: linear-gradient(144.3deg, #2a69fe 0%, #8692ff 44.68%, #a1e8ff 84.85%);
  }
}
</style>
