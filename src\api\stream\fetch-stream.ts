// 状态管理
import { fetchEventSource } from '@microsoft/fetch-event-source';
// import { fetchEventSource } from './test-fetchEventSource';
import { setStreamString, formatContent, handleStreamMessage } from '@/utils';
import { MAX_RETRY_COUNT, BASEURL } from '@/const/const';
import StreamManager from './manager'
import { setInputState } from '@/plugins/app-plugin';
import { CardContentType, CardDataType, StreamFnInstance } from '@/types/api';
import Requests from '@/api/requests'
import utils from '@/utils';
// 观察者
class streamFn implements StreamFnInstance {
  card: CardDataType;
  isStop: boolean;
  timer: any;
  _id: number;
  startTime: number;
  option: any;
  dataHistory: any[];
  isCancel: boolean;
  messageType: Record<number, boolean>;
  retry: number;
  callId?: string;
  notNormalStop? : boolean;
  agentInfo?: any;
  authExpired?: boolean;
  needMarkBlueRunStepsArray: Array<any>

  constructor(card: CardDataType) {
    this.card = card;
    this.isStop = false;
    this.timer = null;
    this._id = new Date().getTime();
    this.startTime = this._id;
    this.option = null;
    this.dataHistory = [];
    this.isCancel = false;
    this.messageType = {};
    this.retry = 0;
    this.needMarkBlueRunStepsArray = []
  }

  async run(option: any) {
    const canInputParams = {
      canInput: false
    };
    setInputState(canInputParams);
    if (!option) {
      this.isStop = false;
      this.isCancel = false;
      this.messageType = {};
    }
    this.option = option || this.option;
    let { url, params, agentInfo } = this.option;
    this.agentInfo = agentInfo;
    if (this.option.init) {
      await this.option.init();
    }
    let isFirst = true;
    this.dataHistory = [];
    let lastDataItem: CardContentType = {
      index: 0,
      context: ""
    };
    let list: any[] = [];
    let recommandQuestion:Array<object> = []
    this.needMarkBlueRunStepsArray = []
    fetchEventSource(BASEURL + url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Accept: 'text/event-stream',
        Authorization: 'Bearer ' + localStorage.getItem("tokenStr")
      },
      body: JSON.stringify(params),
      onopen: async (e:any) => {
        this.authExpired = false;
        const status = Number(e.status);
        if (status === 401 && this.retry < MAX_RETRY_COUNT) {
          this.retry++;
          this.authExpired = true;
          await utils.getNewToken(this.run.bind(this));
        }else if(status !== 200) { //不等于200就是异常结束了
          this.notNormalStop = true;
        }
        console.log("onopen：this.retry", this.retry);
      },
      onmessage:(data:any)  => {
        list.push(data);
        // console.log(list, "list")
        const result = handleStreamMessage(data, {
          isFirst,
          lastDataItem,
          dataHistory: this.dataHistory,
          messageType: this.messageType,
          recommandQuestion,
          option: this.option,
          isStop: this.isStop,
          isCancel: this.isCancel,
          httpStop: this.option.httpStop,
          needMarkBlueRunStepsArray: this.needMarkBlueRunStepsArray
        });
        
        // 更新上下文状态
        isFirst = result.newIsFirst;
        lastDataItem = result.newLastDataItem;
        this.dataHistory = result.updatedDataHistory;
        this.messageType = result.updatedMessageType;
        recommandQuestion = result.updatedRecommandQuestion;
        this.needMarkBlueRunStepsArray = result.updatedNeedMarkBlueRunStepsArray;
        this.isCancel = result.updateCancel;
        return;
        if (typeof data.data === 'string') {
          data.data = JSON.parse(data.data);
        }
        let sourceData: any = data.data;
        if (this.isStop) {
          this.callId = sourceData.callId;
          if (this.option.httpStop && this.callId && !this.isCancel) {
            this.option.httpStop(this.callId);
            this.isCancel = true;
          }
          return;
        }
        let citationsJson = sourceData.citationsJson;
        if (Number(sourceData?.status) === 500) {
          this.dealError(data);
        }
        if (isFirst) {
          this.option.eventHandel('connecting', data);
          isFirst = false;
        } else {
          if (Number(sourceData.sessionType) === 999) {
            this.option.eventHandel('illegal', sourceData.sessionType);
          }
          switch (sourceData.messageType) {
            case 1:
              if (!this.messageType[sourceData.messageType]) {
                this.option.eventHandel("onInit", sourceData);
              } else {
                if (sourceData.content) {
                  this.dataHistory = formatContent(sourceData.content,this.dataHistory);
                  if(citationsJson) {
                    this.dealCitationsJson(citationsJson);
                  }
                }
                // this.option.eventHandel('onmessage', [...this.dataHistory]);
                // this.dealCitationsJson(citationsJson);
              }
              if (sourceData.id) {
                this.option.eventHandel('onmessageId', sourceData.id);
              }
              break;
            case 2:
              this.option.eventHandel("onExecute", sourceData);
              break;
            case 5:
              let content = sourceData.content;
              let context = "";
              
              if (sourceData.id) {
                this.option.eventHandel('onmessageId', sourceData.id);
              }
              if (typeof content === 'string' && content) {
                try {
                  content = JSON.parse(content);
                  context = content.choices[0].delta.content;
                } catch (error) {
                  console.log('error', error);
                  context = "";
                }
              }
              if (context) {
                let items = setStreamString(context, lastDataItem);
                lastDataItem = items[items.length - 1];
                if (items.length == 1 && lastDataItem.isCard) {
                  if (lastDataItem.finish === undefined || lastDataItem.finish === 1) {
                    lastDataItem.finish = 0;
                    items.forEach((item, idx) => {
                      this.dataHistory[item.index] = { ...item };
                    });
                    this.option.eventHandel('onmessage', [...this.dataHistory]);
                  }
                } else {
                  items.forEach((item, idx) => {
                    this.dataHistory[item.index] = { ...item };
                  });
                  this.option.eventHandel('onmessage', [...this.dataHistory]);
                }
              }
              this.dealCitationsJson(citationsJson);
              this.option.eventHandel('onmessage', [...this.dataHistory])
              break;
            case 6:  //为6的时候是推荐问题
              if(sourceData.content && sourceData.content.startsWith("<questions>")) {
                const regex = /<question>(.*?)<\/question>/g;
                const matches = sourceData.content.match(regex);
                matches.forEach((match:string) => {
                  const question = match.replace(/<question>|<\/question>/g, '');
                  recommandQuestion.push({
                    index: recommandQuestion.length,
                    subject: question
                  });
                });
              }
            default:
              break;
          }
          this.messageType[sourceData.messageType] = true;
          if (sourceData.finish === 1) {
            if (this.dataHistory.length) {
              lastDataItem = this.dataHistory[this.dataHistory.length - 1];
              lastDataItem.finish = 1;
              this.dataHistory[this.dataHistory.length - 1] = { ...lastDataItem };
              this.option.eventHandel('onmessage', [...this.dataHistory]);
            }
            if(recommandQuestion.length) {
              this.option.eventHandel('onquestion', recommandQuestion);
            }
            if (this.isStop) {
              return;
            }
            this.stop(true);
            this.option.eventHandel('onclose', event);
          }
        }
      },
      onclose: () => {
        console.log("onclose--->");
        //过期会重新请求，不走关闭里面的逻辑
        if (this.isStop || this.authExpired) {
          return;
        }
        if(this.notNormalStop) {
          const errData = {
            data: {
              message: 'comi不小心发生错误啦，请稍后重试'
            }
          }
          this.dealError(errData);
        }else {
          this.stop(true);
        }
        this.option.eventHandel('onclose', event);
      },
      onerror: (err:any) => {
        console.log('err', err);
        this.dealError(err);
        throw err;
      },
    });
  }

  stop(isNormal?: boolean) {
    if (!this.isStop) {
      this.isStop = true;
      StreamManager.remove(this);
      Requests.logRecord({
        info: this.dataHistory,
        type: 'info'
      });
      this.option?.stop(isNormal);
    }
  }
  dealError(data: any) {
    StreamManager.remove(this);
    const canInputParams = {
      canInput: true
    }
    setInputState(canInputParams);
    Requests.logRecord({
      info: data,
      type: 'error'
    });
    this.option.eventHandel('onerror', data);
  }
  dealCitationsJson(citationsJson: string) {
    if(typeof citationsJson === 'string' && citationsJson) {
      try {
        citationsJson = JSON.parse(citationsJson)
      } catch (error) {
        console.log('error', error);
      }
    }
    if(citationsJson?.length) {
      const lastDataItem = this.dataHistory[this.dataHistory.length - 1];
      lastDataItem.finish = 1;
      this.dataHistory.push({
        index: this.dataHistory.length,
        isKnowledgeData: true,
        context: citationsJson,
      });
      // this.option.eventHandel('onmessage', [...this.dataHistory]);
    }
  }
};

export default streamFn;