<template>
  <div class="voice-card">
    <div class="voice-block">
      <div @click="continuePlay" v-if="isPause" class="continue-play">继续播放</div>
      <div @click="playOrPuase" class="voice" :style="voiceWidth">
        <div class="text">11 "</div>
        <div class="iconfont" :class="[isPlay? 'play-image' : 'ai-icon-yinpin-bofang']"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {computed, ref} from 'vue';
import {defineOptions} from 'vue';

defineOptions({
  name: 'VoiceCard'
});

const props = defineProps({
  staticData: {
    type: Object,
    default: () => ({}),
  },
});
// 长度规则： 最大宽度=页面宽度*70% 语音气泡宽度随时长逐渐增加，前10秒内，语音气泡每秒宽度增加屏幕宽度的1%，10秒到60秒时每秒宽度增加0.8%
const voiceWidth = computed(() => {
  // return {width: `20%` }
  const timer = props.staticData?.timer;
  const containerWidth = window.innerWidth - 24; //减去两边的padding
  if(timer < 10) {
    return {width: `${containerWidth * (0.2 + timer*0.01)}px` }
  }else if(timer > 10 && timer < 60) {
    return {width: `${containerWidth*(0.2 + 0.1 + (timer-10)*0.08)}px` }
  }else {
    return {width: `${containerWidth * 0.7}px` }
  }
});
let isPause = ref(false);
let isPlay = ref(false);
function playOrPuase() {
  // isPause.value = !isPause.value;
  isPlay.value = !isPlay.value;
  if(!isPlay.value) {
    isPause.value = true;
  }else {
    isPause.value = false;
  }
}

function continuePlay() {
  isPause.value = false;
  isPlay.value = true;
}
</script>

<style lang="scss" scoped>
.voice-card {
  .voice-block {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    .state {
      margin-right: 12px;
    }
  }
  .continue-play {
    background: #d1e0ff;
    margin-right: 8px;
    white-space: nowrap;
    color: #4379ff;
    padding: 2px 8px;
    font-size: 12px;
    font-size: var(--theme-font-size0, 12px);
    height: 24px;
    line-height: 24px;
    border-radius: 24px;
  }
  .voice {
    color: #fff;
    display: flex;
    overflow: hidden;
    white-space: nowrap;
    justify-content: flex-end;
    border-radius: 12px 12px 2px 12px;
    padding: 8px 16px;
    background: linear-gradient(135deg, #5973fe, #4379ff);
    font-size: 16px;
    font-size: var(--theme-font-size2, 16px);
    line-height: 24px;
    line-height: var(--theme-line-height2, 24px);
    align-items: center;
    box-sizing: border-box;
  }
  .play-image {
    background-image: url("@/assets/images/play-voice.gif");
    background-size: cover;
    width: 16px;
    height: 16px;
  }
  .text {
    margin-right: 6px;
    flex: 1;
    overflow: hidden;
    text-align: right;
  }
  .text-card {
    max-width: 80%;
    padding: 8px 16px;
    border-radius: 12px;
    color: rgba(0, 0, 0, 1);
    color: var(--theme-font-color0, rgba(0, 0, 0, 1));
    background: #fff;
  }
}
</style>
