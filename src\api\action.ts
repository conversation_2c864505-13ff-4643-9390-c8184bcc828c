import store from './store';
import requests from "./requests";
import StreamManager from './stream/manager';
import Voice from './voice';
import { aiToast } from '../plugins/ai-toast';
import { aiConfirm } from '../plugins/ai-confirm';
import { feedbackDrawer } from '../plugins/feedback-drawer';
import { longTouchAction } from '../plugins/long-touch-action';
import { showDialog, setSystemUI, showOrHideInput, cleanInputInfo, setHistorySessionID, initWebPageParams, setInputState, appExeededLimit } from '../plugins/app-plugin';
import { getLocalStorage, getAssistantId, setLocalStorage, buildUUID, getDeviceInfo } from '@/utils/common';
import { AiRequestCard, AiLoadingCard, AgentInfo, AiDataCard, FileType } from '@/types/api';
import {nextTick} from 'vue';
const { isApp } = getDeviceInfo();
import { DIALOGUE } from '@/const/const';
/**
 * 消息主入口
 * @param {string} message
 * @returns
 */
async function sendMessage(message: string, citations?: Array<FileType>) {
  if (!message) {
    return;
  }
  const citationsParams = citations || [];
  store.staticData.streamParams.citations = citationsParams;
  initState();
  let aiRequestCard = createRequestCard(message);
  const params = initParams(message);

  let agentInfo: AgentInfo |  null = null;
  let aiLoadingCard: AiLoadingCard | null = null;
  let aiDataCard: AiDataCard | null = null;
  const url = '/ai-manager/assistant/info/call/stream';
  const stream = StreamManager.create(aiRequestCard);
  let messageId = '';
  let illageSessionType: string | number;
  const currentAgentInfo = store.state.selectedAgentInfo || store.state.assistantInfo;
  stream.run({
    url,
    params,
    agentInfo: currentAgentInfo,
    init: () => initializeStream(aiRequestCard, agentInfo, aiLoadingCard, aiDataCard),
    eventHandel: (type: string, data: any) => {
      const result = dealCardData({
        type, data, aiRequestCard, agentInfo, aiLoadingCard, stream, aiDataCard, messageId, illageSessionType
      });
      if(result) {
        illageSessionType = result.illageSessionType;
        agentInfo = result.agentInfo;
        aiLoadingCard = result.aiLoadingCard;
        aiDataCard = result.aiDataCard; 
        messageId = result.messageId;
      }
      store.scrollToBottom();
    },
    stop: (isNormal:boolean)=>{
      if(!store.staticData.isDigtalChat) {
        dealProcessData(aiDataCard, false, isNormal);
      }
      requests.logRecord({
        info: "isNormal:" + isNormal + ", aiDataCard: " + JSON.stringify(aiDataCard),
        type: 'info'
      });
      //看结束的时候有没有sessionId ，没有的话去后端拿一个
      const sessionId = sessionStorage.getItem(`aiSessionId_${stream.agentInfo?.id}`);
      if(!sessionId) {
        const params = {
          chatSessionId: stream.option.params.chatSessionId,
          sessionId: 0,
          assistantId: stream.agentInfo?.id,
        }
        requests.getSessionId(params).then((data:any)=>{
          if(Number(data.code) === 0) {
            const sessionId = data.data.sessionId;
            sessionStorage.setItem(`aiSessionId_${params.assistantId}`, sessionId);
          }
        })
      }
      
      // 最后的兜底
      const lastAnswer = {
        finish: 1,
        index: 0,
        context: '对不起，CoMi暂时无法回答你的问题，我会努力学习的。'
      };
      if (!isNormal) {
        dealStopCard(aiDataCard, aiLoadingCard)
      } else if (isNormal && aiDataCard) {
        aiDataCard.data.illageSessionType = illageSessionType;
        aiDataCard.data.isCompleted = true;
        //如果没有返回数据，并且有执行过程
        if (!aiDataCard.data.cardData?.length) {
          store.card.remove(aiLoadingCard);
          aiDataCard.data.cardData.push(lastAnswer);
        }
      } else if (isNormal && !aiDataCard) {
        aiDataCard = store.card.add({
          componentKey: 'AiDataCard',
          data: {
            cardData: [lastAnswer],
            isCompleted: true
          }
        }, aiLoadingCard);
      }
      // 绑定一个流，可以处理未完成的流式卡片语音播放
      if (store.state.isOpenVoice && isNormal && !store.staticData.longTouchCard?.data?.isPlay && aiDataCard) {
        if(store.staticData.hasVoiceKey) {
          aiDataCard.data.isPlay = true;
        }
        store.action.setStaticData('longTouchCard', aiDataCard);
        Voice.add(aiDataCard, stream).run(function () {
          aiDataCard && (aiDataCard.data.isPlay = false);
        });
      }
    },
    httpStop: (callId: string) => stopRequest(callId)
  })
  //发送后就清除
  store.action.setState('selectedAgentInfo', null);
  store.action.setStaticData('uploadFileInfo', null);
}

// 处理思考过程数据
function dealProcessData(aiDataCard: AiDataCard | null, isError: boolean = false, isNormalStop: boolean) {
  if(!aiDataCard) {
    return;
  }
  const thinkData = aiDataCard.data.processData?.find((item: any) => item.type === 'think');
  const executeData = aiDataCard.data.processData?.find((item: any) => item.type === 'exec');
  // 如果已经产生了数据，就不需要更改思考过程数据
  if (aiDataCard.data.cardData?.length) {
    return
  }

  let thinkDataTitle = '已完成意图分析';
  let executeDataTitle = '已执行Agent';
  // 错误时
  if (isError) {
    if (thinkData && !executeData) {
      thinkDataTitle = '未能识别意图';
    }
    if (executeData) {
      executeDataTitle = '未能成功执行Agent';
    }
  }
  // 非正常停止时
  if (!isNormalStop) {
    if (thinkData && !executeData) {
      thinkDataTitle = '已停止意图识别';
    }
    if (executeData) {
      executeDataTitle = '已停止执行Agent';
    }
  }


  if (thinkData && !executeData) {
    thinkData.loading = false;
    thinkData.title = thinkDataTitle || thinkData.title;
    thinkData.isError = isError;
    thinkData.isStop = !isNormalStop;
  }
  if (executeData) {
      executeData.loading = false;
      executeData.title = executeDataTitle || executeData.title;
      const executeDataContent = executeData.content;
      if(executeDataContent.length) {
        const lastContent = executeDataContent[executeDataContent.length - 1];
        lastContent.loading = false;
        lastContent.isError = isError;
        lastContent.isStop = !isNormalStop;
        lastContent.content = !isNormalStop ? '已停止' : lastContent.content || '';
      }
      executeData.isError = isError;
      executeData.isStop = !isNormalStop;
  }
}
function dealCardData(params: any) {
  let {type, data, aiRequestCard, agentInfo, aiLoadingCard, stream, aiDataCard, messageId, illageSessionType} = params;
  if(aiRequestCard?.data.isError) {
    return;
  }
  //是否发送成功
  let handleConnecting: (null | (()=>void)) = () => { 
    if (data.data.success) {
      aiRequestCard.data.isLoading = false;
      //AgentName
      const agentInfoParam = {
        agentInfo: stream.agentInfo
      }
      agentInfo = createBaseCard('AgentInfo', agentInfoParam);
      // 替换loaidng卡片
      if (!aiDataCard) {
        aiDataCard = store.card.add({
          componentKey: 'AiDataCard',
          data: {
            cardData: [],
            processData: [
              {
                content: '',
                expand: true,
                loading: true,
                title: '',
                type: 'think'
              }
            ]
          },
          staticData: {
            requestParams: params
          }
        }, aiLoadingCard);
        StreamManager.bind(aiDataCard, stream);
      }
      // 加载loading
      const loadingParam = {
        content: '意图识别中',
        isLoading: true
      }
      // aiLoadingCard =  createBaseCard('AiLoadingAbort', loadingParam);
    } else {
      aiRequestCard.data.isLoading = false;
      aiRequestCard.data.isError = true;
      // 要重试Stream
      StreamManager.bind(aiRequestCard, stream);
    }
  };
  //初始化处理
  let handleOnInit: (null | (()=>void)) = () => {
    if(aiDataCard && !store.staticData.isDigtalChat){
      aiDataCard.data.processData = [
        {
          content: data.content || '',
          expand: true,
          loading: false,
          title: '已完成意图分析',
          type: 'think'
  
        },
        {
          expand: true,
          type: 'exec',
          loading: true,
          title: 'Agent执行中',
          content: []
        }
      ];
    }
    // 缓存aiSessionId, 用于连续对话使用，新开会话的时候清除
    if (data?.aiSessionId && !store.state.historyModel) {
      store.action.setState('historyModel', false);
      sessionStorage.setItem(`aiSessionId_${data?.assistant?.id}`, data.aiSessionId)
      setHistorySessionIdFn(data.aiSessionId);
    }
  }
  //处理返回回来的信息
  let handleOnMessage: (null | ((knowledge?:string)=>void)) = (knowledge?:string) => {
    if(!aiDataCard?.data?.processCompleted && !store.staticData.isDigtalChat) {
      const processData = aiDataCard.data.processData;
      const lastProcess = processData[processData.length - 1];
      lastProcess.loading = false;
      lastProcess.title = '已成功执行Agent';
      aiDataCard.data.processCompleted = true;
    }
    if(!data?.length && store.staticData.isDigtalChat) {
      return;
    }
    const result = data;
      // 替换loaidng卡片
      if (!aiDataCard) {
        aiDataCard = store.card.add({
          componentKey: 'AiDataCard',
          data: {
            cardData: []
          },
          staticData: {
            requestParams: params
          }
        }, aiLoadingCard);
        StreamManager.bind(aiDataCard, stream);
      }
      if (messageId && !aiDataCard.data.messageId) {
        aiDataCard.data.messageId = messageId;
      }
    if(knowledge === 'knowledge') {
      aiDataCard.data.knowledgeData = result;
    }else {
      aiDataCard.data.cardData = result;
    }
  }
  //处理错误信息
  let handleError: (null | (()=>void)) = () => {
    if(!navigator.onLine) {
      aiToast({
        content: '网络连接失败，请检查网络设置'
      });
    }
    // 初始化加载，最后一张卡片是消息卡片，添加重试
    const lastCard = store.state.allCardData[store.state.allCardData.length - 1];
    if (lastCard.componentKey === 'AiRequestCard') {
      aiRequestCard.data.isError = true;
      aiRequestCard.data.isLoading = false;
      StreamManager.bind(aiRequestCard, stream);
      // store.action.saveCardToHistory(aiRequestCard);
    } else {
      if(!store.staticData.isDigtalChat){
        dealProcessData(aiDataCard, true, true);
      }
      dealStopCard(aiDataCard, aiLoadingCard);
      if(navigator.onLine) {
        aiToast({
          content: data.data?.message || 'comi不小心发生错误啦，请稍后重试',
        });
      }
    }
  }
  let handleExeededLimit: (null | (()=>void)) = () => {
    appExeededLimit();
  }
  let eventHandlers: Record<string, (data: any)=> void> | null = {
    connecting: handleConnecting,
    onInit: handleOnInit,
    onExecute: () => {
      if(store.staticData.isDigtalChat) {
        return;
      }
      const execData = aiDataCard.data.processData.find((item:any)=> item.type === 'exec');
      execData.title = `根据您的问题，正在为您执行Agent`;
      const agentExecContent = execData.content;
      agentExecContent.push({
        complete: true,
        loading: false,
        title: data.stepName,
        content: data.content
      });
    },
    onmessage: handleOnMessage,
    onclose: () => store.action.setState('isLoading', false),
    onmessageId: () => messageId = data,
    onquestion: () => aiDataCard && (aiDataCard.data.recommandQuestion = data),
    onknowledge:() => {
      handleOnMessage && handleOnMessage('knowledge')
    },
    illegal: () => illageSessionType = data,
    error: handleError,
    onExeededLimit: handleExeededLimit
  };
  eventHandlers[type] && eventHandlers[type](params);
  // 清除事件，避免内存泄漏
  eventHandlers = null;
  handleConnecting = null;
  handleOnInit = null;
  handleOnMessage = null;
  handleError = null;
  return {
    illageSessionType,
    agentInfo, 
    aiLoadingCard,
    aiDataCard, 
    messageId,
  }
}


function createBaseCard(componentKey:string,data:object) {
  return store.card.add({
    componentKey: componentKey,
    data: data
  })
}
//初始化一些状态
function initState() {
  store.action.setState('isLoading', true);
  store.action.setState("isScrolling", true);
  if (!store.state.isChat) {
    store.state.isChat = true;
  }
  if(!store.state.single){
    setSystemUI( {"webviewBg":"webviewLight", "webviewBgRgb":"#EDF2FC"},'setWebviewBg')
  }
  store.scrollToBottom();
  // 停止播放语音
  cardStopVoice(true);
}
//处理请求参数
function initParams( message: string) {
  const selectAssistantId = store.state.selectedAgentInfo?.id;
  const assistantId = selectAssistantId || getAssistantId() || '';
  const sessionIdKey = selectAssistantId ? `aiSessionId_${selectAssistantId}` : store.staticData.aiSessionIdKey;
  const aiChatSessionId = sessionStorage.getItem(sessionIdKey) || '';// 连续对话id
  let params = store.staticData.streamParams;
  return {
    ...params,
    assistantId,
    // assistantCode: 'edaf36638d254ae2b940348e4330c182',
    assistantCode: store.state.selectedAgentInfo?.code ||store.state.assistantInfo?.code,
    sessionId: aiChatSessionId || '',
    // chatSessionId: 'edaf36638d254ae2b940348e4330c182',
    chatSessionId: store.state.chatSessionId,
    input: message || '',
  };
}
function createRequestCard(message: string): AiRequestCard {
  return store.card.add({
    componentKey: 'AiRequestCard',
    staticData: { 
      message,
      citations: store.staticData.uploadFileInfo
    },
    data: {
      isLoading: true,
      isError: false
    }
  });
}

function initializeStream(aiRequestCard: AiRequestCard, agentInfo: AgentInfo | null, aiLoadingCard: AiLoadingCard | null, aiDataCard: AiDataCard | null) {
  aiRequestCard.data.isLoading = true;
  aiRequestCard.data.isError = false;
  store.card.remove(agentInfo);
  store.card.remove(aiLoadingCard);
  store.card.remove(aiDataCard);
}
//处理停止回答或异常停止共部分的逻辑
function dealStopCard(aiDataCard: AiDataCard | null, aiLoadingCard: AiLoadingCard | null) {
  //非正常停止并且有生成数据
  const stopCard = {
    componentKey: 'AiLoadingAbort',
    data: {
      content: '已停止回答',
      isLoading: false
    }
  };
  if (aiDataCard) {
    aiDataCard.data.isStop = true;
    aiDataCard.data.isCompleted = true;
    const dataCard = aiDataCard.data?.cardData;
    if (dataCard.length || aiDataCard.data.processData?.length) {
      const lastCard = dataCard[dataCard.length - 1];
      lastCard && (lastCard.finish = 1);
    } else {
      store.card.add(stopCard, aiDataCard);
    }
  } else {
    store.card.add(stopCard, aiLoadingCard);
  }
}
async function sendCard(message: string) {
  if(!store.state.single){
    setSystemUI( {"webviewBg":"webviewLight", "webviewBgRgb":"#EDF2FC"},'setWebviewBg')
  }
  if (!store.state.isChat) {
    store.state.isChat = true;
  }
  const aiDataCardParam = {
    assistantName: 'Agent',
    cardData: [{
      isCard: true,
      finish: 1,
      json: {
        type: 'application'
      }
    }]
  };
  createBaseCard('AiDataCard', aiDataCardParam);
  store.scrollToBottom();
}
function interruptAction() {
  StreamManager.stopAllStreams();
}

function clean() {
  store.action.clearAllData();
  store.state.isChat = false;
  store.action.setState('isLoading', false)
  store.action.setState('chatSessionId', buildUUID());
  store.action.setState('showScrollBtn', false);
  // store.action.setState('selectedAgentInfo', null);
  // 新开对话移除连续对话的id
  const sessionStorageLength = sessionStorage.length;
  for(let i = 0; i < sessionStorageLength ; i++) {
    const keyStr = sessionStorage.key(i) || '';
    if(keyStr?.indexOf("aiSessionId_") > -1) {
      sessionStorage.removeItem(keyStr);
      i--;
    }
  }
  
  const params = {
    assistantId: getAssistantId(),
    chatSessionId: store.state.chatSessionId,
    sessionId: ''
  };
  initWebPageParams(params);
  if(!store.state.single){
    if(store.state.currentHeaderKey === DIALOGUE){
      setSystemUI( {"webviewBg":"webviewDark", "webviewBgRgb":"#EDF2FC"},'setWebviewBg');
    }else{
      setSystemUI( {"webviewBg":"webviewLight", "webviewBgRgb":"#EDF2FC"},'setWebviewBg');
    }
  }
  cleanInputInfo({attachment:"true",assist:"true"});
  const canInputParams = {
    canInput: true
  }
  setInputState(canInputParams);
  cardStopVoice(true);
}
function openNewConversation() {
  //如果不是对话页，就已经是最新会话了
  if(!store.state.isChat) {
    aiToast({
      content: '已是最新对话',
      timer: 1500,
    });
    return;
  }
  const dontAskAgain = getLocalStorage('dontAskAgain');
  if (dontAskAgain) {
    clean();
    return;
  }
  if (!isApp) {
    // h5对话确认
    aiConfirm({
      content: '新开会话，将会清空现有对话记录，是否继续？',
      onConfirm: () => {
        clean();
      },
      onCancel: () => {
        console.log('cancel');
      },
    });
  } else {
    showDialog(
      {
        dialogName: 'confirmDialog',
        message: '新开会话，将会清空现有对话记录，是否继续？',
      },
      'showDialog',
      (data: string) => {
        if (data) {
          const result = JSON.parse(data);
          // 确认弹窗勾选后，缓存到本地
          if (result.confirmResult === 'sure') {
            if (result.dontAskAgain) {
              setLocalStorage('dontAskAgain', true)
            }
            clean();
          }
        }
      }
    );
  }
}
function stopRequest(callId: string):void {
  const params = {
    callId: callId,
    sessionId: sessionStorage.getItem(store.staticData.aiSessionIdKey),
    chatSessionId: store.state.chatSessionId,
  };
  requests.interruptRequest(params).then((data:any) => {
    if (Number(data.code) === 0 && data?.data) {
      console.log('终止成功');
      // setInputState();
      // voiceAnnouncements('', 'stopSpeech', () => {});
      // 终止成功后操作
    } else {
      console.log('终止失败');
    }
  });
};
/**
 * 点踩调起drawer
 * @param {*} targetCard 目标卡片的信息
 */
function feedbackDrawerFn(targetCard: AiDataCard) {
  // 显示隐藏原生输入框
  showOrHideInputFn(false);
  feedbackDrawer({
    onConfirm: (feedbackData:{type: string|number, feedbackMessage: string}) => {
      showOrHideInputFn(true);
      const params = {
        messageId: targetCard?.data?.messageId,
        type: feedbackData.type, //点踩
        content: feedbackData.feedbackMessage
      }
      requests.evaluate(params).then((data:any) => {
        let content = '';
        if (Number(data?.code) === 0) {
          targetCard.data.isUnlike = true;
          // store.action.saveCardToHistory(targetCard);
          content = '感谢您的反馈，我们会持续改进～';
        } else if (data?.message) {
          content = data.message;
        }
        aiToast({
          content: content
        });
      }, err => {
        console.log(err)
      })
    },
    onClose: () => {
      showOrHideInputFn(true);
    }
  })
}

/**
 * 长按展示操作
 * @returns 
 */

function showTouchAction(targetCard: AiDataCard, positionInfo:{x: number, y: number}) {
  const actionButton = [
    // {
    //   name: '复制',
    //   id: 'copy',
    //   icon: 'ai-icon-fuzhi'
    // },
    {
      name: '复制',
      id: 'copyAll',
      icon: 'ai-icon-quanwenfuzhi'
    },
    // {
    //   name: '分享',
    //   id: 'share',
    //   icon: 'ai-icon-fenxiang'
    // },

  ];
  let readItem = {
    name: '朗读',
    id: 'read',
    type: 'icon',
    icon: 'ai-icon-bobao-xianbeifen'
  };
  actionButton.push(readItem);
  //停止朗读的图标是正在播放
  if (targetCard.data.isPlay) {
    readItem.name = '停止朗读';
    readItem.type = 'image';
    readItem.icon = 'reading';
  }
  if (targetCard.data.messageId) {
    let unlikeItem = {
      name: '不喜欢',
      id: 'dislike',
      icon: 'ai-icon-diancai-xian'
    };
    if (targetCard.data.isUnlike) {
      unlikeItem.icon = 'ai-icon-diancai-mian';
    }
    actionButton.push(unlikeItem);
  }

  store.staticData.instanceLongTouch = longTouchAction({
    operateArray: actionButton,
    positionInfo: positionInfo,
    cardData: targetCard,
    clickOperate: (operate:{
      name: string,
      id: string,
      icon: string
  }) => {
      store.action.setStaticData('instanceLongTouch', null);
      clickOperateFn(operate, targetCard);
    }
  });
}
function clickOperateFn(operate: {
  name: string,
  id: string,
  icon: string
}, targetCard:AiDataCard) {
  let currentSelectText = store.card.getCardToText(targetCard.data.cardData);
  switch (operate.id) {
    // case 'copy': 
    //   copyText(currentSelectText); 
    //   break;
    case 'copyAll':
      const params  = targetCard.staticData?.requestParams;
      if(params) {
        regenerateAssistant(params);
      }
      copyText(currentSelectText);
      break;
    case 'read':
      playVoiceAction(targetCard);
      break;
    // case 'share':
    case 'dislike':
      dislike(targetCard);
      break;
    default:
  }
}
function playVoiceAction(targetCard: AiDataCard) {
  if (targetCard.data.isPlay) {
    store.action.setStaticData('longTouchCard', targetCard);
    //终止本次的
    cardStopVoice(true);
  } else {
    playVoice(targetCard);
  }
}
function dislike(targetCard: AiDataCard) {

  if (targetCard.data.isUnlike) {
    targetCard.data.isUnlike = false;
    //  TODO: 此处需要后续增加取消不喜欢的接口对接
    // store.action.saveCardToHistory(targetCard);
  } else {
    feedbackDrawerFn(targetCard);
  }
}

function closeLongtouch() {
  if (store.staticData.instanceLongTouch) {
    store.staticData.instanceLongTouch.destroy();
    store.action.setStaticData('instanceLongTouch', null);
  }
}

function showOrHideInputFn(isVisible:boolean) {
  const showInputParams = {
    visible: isVisible,
  };
  // 显示隐藏原生输入框
  showOrHideInput(showInputParams);
}
function playVoice(targetCard: AiDataCard) {
  //播之前先终止上一次的
  cardStopVoice(true);
  store.action.setStaticData('longTouchCard', targetCard);
  const stopPlay = function () { //结束的回调，此时不用再手动调用了
    cardStopVoice();
  }
  const tips = '正在生成语音朗读...';
  Voice.init(targetCard).run(stopPlay, tips);
  if(store.staticData.hasVoiceKey) {
    store.action.changePlayState(true);
  }
}

function cardStopVoice(needStop?: boolean) {
  store.action.changePlayState(false);
  if (needStop) {
    Voice.stop();
  }
}
//TODO: 周报和合同演示数据
function demoData() {
  const THINKSTEP = {
    id: '1',
    type: 'think',
    loading: true,
    title: '正在推理中',
    content: '',
    expand: true,
  };

  const EXECUTESTART = {
    id: '2',
    type: 'exec',
    loading: true,
    title: 'Agent执行中',
    content: [
      {
        id: 'process1',
        loading: true,
        complete: false,
        title: '自动采集Agent',
      },
      {
        id: 'process2',
        loading: false,
        complete: false,
        title: '内容整合Agent',
      },
      {
        id: 'process3',
        loading: false,
        complete: false,
        title: '内容生成Agent',
      },
    ],
    expand: true,
  };

  const EXECUTEEND = {
    id: '2',
    type: 'exec',
    loading: false,
    title: '已执行Agent',
    content: [
      {
        id: 'process1',
        loading: false,
        complete: true,
        title: '自动采集Agent',
      },
      {
        id: 'process2',
        loading: false,
        complete: true,
        title: '内容整合Agent',
      },
      {
        id: 'process3',
        loading: false,
        complete: true,
        title: '内容生成Agent',
      },
    ],
    expand: true,
  };
  const CONSTRACTSTART = {
    id: '3',
    type: 'exec',
    loading: true,
    title: '已执行Agent',
    content: [
      {
        id: 'process1',
        loading: false,
        complete: true,
        title: '查询Agent',
      },
      {
        id: 'process2',
        loading: true,
        complete: false,
        title: '分析Agent',
      },
      {
        id: 'process3',
        loading: false,
        complete: false,
        title: '预测Agent',
      },
      {
        id: 'process4',
        loading: false,
        complete: false,
        title: '风险监测Agent',
      },
      {
        id: 'process5',
        loading: false,
        complete: false,
        title: '诊断Agent',
      },
      {
        id: 'process6',
        loading: false,
        complete: false,
        title: '决断Agent',
      },
    ],
    expand: true,
  };
  const CONSTRACTEND = {
    id: '4',
    type: 'exec',
    loading: false,
    title: '已执行Agent',
    content: [
      {
        id: 'process1',
        loading: false,
        complete: true,
        title: '查询Agent',
      },
      {
        id: 'process2',
        loading: false,
        complete: true,
        title: '分析Agent',
      },
      {
        id: 'process3',
        loading: false,
        complete: true,
        title: '预测Agent',
      },
      {
        id: 'process4',
        loading: false,
        complete: true,
        title: '风险监测Agent',
      },
      {
        id: 'process5',
        loading: false,
        complete: true,
        title: '诊断Agent',
      },
      {
        id: 'process6',
        loading: false,
        complete: true,
        title: '决断Agent',
      },
    ],
    expand: true,
  };
  const KNOWLEDGEDATA = [
    {
      id: '3477686019822993727',
      type: 'bulletin',
      appId: '7',
      name: '测试公告',
      metaData: 1,
    },
    {
      id: '-3544971429423004306',
      type: 'news',
      appId: '8',
      name: '测试新闻',
      metaData: 2,
    },
    // {
    //   id: '3477686019822993727',
    //   type: 'news',
    //   name: '测试公告'
    // },
    {
      id: '-1607394957558657574',
      type: 'collaboration',
      appId: '1',
      name: '测试协同',
      metaData: 3,
    }]
  const EXECUTESTEPSTART = {
    id: '5',
    type: 'exec',
    loading: true,
    title: 'Agent执行中',
    content: [
      {
        id: 'process4',
        loading: false,
        complete: true,
        title: '内容生成Agent',
      },
      {
        id: 'process5',
        loading: true,
        complete: false,
        title: '智能填单Agent',
      },
      {
        id: 'process6',
        loading: false,
        complete: false,
        title: '发起协同Agent',
      },
    ],
    expand: true,
  };

  const EXECUTESTEPEND = {
    id: '6',
    type: 'exec',
    loading: false,
    title: '已执行Agent',
    content: [
      {
        id: 'process4',
        loading: false,
        complete: true,
        title: '内容生成Agent',
      },
      {
        id: 'process5',
        loading: false,
        complete: true,
        title: '智能填单Agent',
      },
      {
        id: 'process6',
        loading: false,
        complete: true,
        title: '发起协同Agent',
      },
    ],
    expand: true,
  };

  return {
    THINKSTEP,
    EXECUTESTART,
    EXECUTEEND,
    CONSTRACTSTART,
    CONSTRACTEND,
    KNOWLEDGEDATA,
    EXECUTESTEPSTART,
    EXECUTESTEPEND
  }
}

//复制功能
async function copyText(textValue:string) {
  // const textValue = cardDataToText(props?.data.cardData);
  if (navigator.clipboard) {
    await navigator.clipboard.writeText(textValue);
    aiToast({
      content: '复制成功',
    });
  } else {
    execCommandCopy(textValue);
  }
  // regenerateAssistant();
}

function regenerateAssistant(params:any) {
  params.type = 1;
  requests.regenerateAssistant(params).then((data)=>{
    console.log("请求成功");
  })
}

function setHistorySessionIdFn(currentSessionId: string) {
  if(!store.state.historySessionId || (store.state.historySessionId && (store.state.historySessionId !== currentSessionId))){
      setHistorySessionID({
        historySessionId: currentSessionId
      });
      store.action.setState('historySessionId', currentSessionId);
  }
}

function execCommandCopy(val:string) {
  const textArea = document.createElement('textarea');
  textArea.value = val;
  textArea.style.width = '0';
  textArea.style.position = 'fixed';
  textArea.style.left = '-999px';
  textArea.style.top = '10px';
  textArea.setAttribute('readonly', 'readonly');
  document.body.appendChild(textArea);

  textArea.select();
  document.execCommand('copy');
  document.body.removeChild(textArea);
  aiToast({
    content: '复制成功',
  });
}

function getUserList(targetData: any) {
  store.action.setState("isScrolling", true);
  store.scrollToBottom();
  const params = {
    type: "Member",
    queryParams: {
      pageSize: "5",
      pageNo: "1"
    },
    bodyParams: {
      name: targetData.matched_word
    }
  }
  requests.getCollaborationUser(params).then(async (res)=>{
    if(Number(res.code) === 0) {
      const userList = res.data.data;
      if(userList.length) {
        const userParam = {
          userList: userList
        }
        createBaseCard('PersonalInfo', userParam);
        await nextTick();
        //TODO: 滚动到底部
        setTimeout(()=>{
          store.scrollToBottom();
        }, 200);
      }
    }
  })
}



export default {
  sendMessage,
  sendCard,
  interruptAction,
  openNewConversation,
  copyText,
  feedbackDrawerFn,
  showTouchAction,
  closeLongtouch,
  playVoice,
  cardStopVoice,
  clean,
  getUserList,
  regenerateAssistant,
  initParams,
  dealCardData
};
