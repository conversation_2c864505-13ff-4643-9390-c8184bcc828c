<template>
  <div class="data-chart-wrap">
    <DataHeader dataType="chart" :data="JSON.parse(JSON.stringify(content))" />
    <ComiEchart
      :chartType="content.type"  
      :isPreview="isPreview"
      :content="JSON.parse(JSON.stringify(content))"
      v-if="showChart"
    />
  </div>
</template>
<script setup lang="ts">
import { ComiEchart } from '@seeyon/seeyon-comi-plugins-library';
import ChartContent from './data-chart.vue';
import DataHeader from '@/components/data-header/index.vue';
import { ChartTableType } from '@/types';
import { computed } from 'vue';

const { isPreview, content } = defineProps<{
  isPreview: boolean;
  content: ChartTableType;
}>();

// 是否显示图
const showChart = computed(() => {
  return content?.data?.length > 0;
});
</script>
<style lang="scss">
.data-chart-wrap {
  height: 100%;
  display: flex;
  flex-direction: column;
}
</style>
