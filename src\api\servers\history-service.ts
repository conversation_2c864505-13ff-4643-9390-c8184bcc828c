import API from '@/api/index';
import { formatMessageList } from '@/utils/messageFormatter';
import type { HistoryParams } from '@/types/chat';
import { ref } from 'vue';

class HistoryService {
  private static instance: HistoryService;
  private state: any;
  private historyParams: HistoryParams;
  private hasMore: boolean;
  private historyLoading = ref(false);
  private isInitialLoad = ref(false);

  private constructor(state: any) {
    this.state = state;
    this.historyParams = {
      pageNumber: 1,
      pageSize: 10,
      total: 0,
    };
    this.hasMore = true;
  }

  public static getInstance(state: any): HistoryService {
    if (!HistoryService.instance) {
      HistoryService.instance = new HistoryService(state);
    }
    return HistoryService.instance;
  }

  get params() {
    return this.historyParams;
  }

  get hasMoreData() {
    return this.hasMore;
  }

  get isLoading() {
    return this.historyLoading.value;
  }

  get isInitialLoading() {
    return this.isInitialLoad.value;
  }

  async loadHistoryData(isLoadMore = false) {
    if (!this.hasMore) return false;
    try {
      this.historyLoading.value = true;
      // 如果是初始加载，设置标志位
      if (!isLoadMore) {
        this.isInitialLoad.value = true;
      }

      const data = await API.requests.getHistortChatById({
        pageInfo: {
          pageNumber: this.historyParams.pageNumber,
          pageSize: this.historyParams.pageSize,
          needTotal: true,
        },
        params: {
          sessionId: this.state.historySessionId
        }
      });

      this.historyLoading.value = false;
      if (Number(data.code) !== 0 || !data?.data?.content?.length) {
        this.hasMore = false;
        this.isInitialLoad.value = false;
        return false;
      }

      this.historyParams.total = data.data.pageInfo.pages;
      const list = data.data.content;
      const totalCount = data.data.pageInfo.total;
      list.forEach((item: any, index: number) => {
        item.index = index + 1 + this.historyParams.pageSize*(this.historyParams.pageNumber-1);
      });
      // 直接翻转数组
      const sortedList = list.reverse().filter((item: any) => (item.messageType === 0 || item.messageType === 1));
      
      const formattedList = formatMessageList(sortedList, {
        assistantInfo: this.state.assistantInfo
      });
      // TODO:  临时解决历史数据，处理 citations 为空的情况
      formattedList.forEach((item: any) => {
        if(item.componentKey === "AiRequestCard"){
          if(typeof item.staticData.citations === 'string' && item.staticData.citations) {
            if(item.staticData.citations === '[]'){
              item.staticData.citations = '';
            }else{
              try {
                let citationsJson = JSON.parse(item.staticData.citations)
                if(citationsJson?.length) {
                  item.staticData.citations = {
                    fileNumber: citationsJson[0].number,
                    fileUrl: citationsJson[0].fileUrl,
                    name: citationsJson[0].name,
                    size: citationsJson[0].fileSize                ,
                  };
                }
              } catch (error) {
                console.log('error', error);
              }
            }
          }
        }else if(item.componentKey === 'AiDataCard') {
          item.staticData.requestParams.sessionId = this.state.historySessionId;
        }
      });

      if (isLoadMore) {
        // 保存当前滚动位置和容器高度
        const messageBox = document.getElementById('scroll-content');
        const scrollPosition = messageBox ? messageBox.scrollTop : 0;
        const oldHeight = messageBox ? messageBox.scrollHeight : 0;
        
        this.state.allCardData = [...formattedList, ...this.state.allCardData];
        
        // 等待 DOM 更新后恢复滚动位置
        setTimeout(() => {
          if (messageBox) {
            // 计算新增内容的高度
            const newHeight = messageBox.scrollHeight;
            const heightDiff = newHeight - oldHeight;
            // 调整滚动位置，加上新增内容的高度
            messageBox.scrollTop = scrollPosition + heightDiff;
          }
        }, 0);
      } else {
        this.state.allCardData = formattedList;
      }

      this.historyParams.pageNumber++;
      this.hasMore = this.state.allCardData.length < totalCount;
      
      // 如果是初始加载，等待 DOM 更新后重置标志位
      if (!isLoadMore) {
        setTimeout(() => {
          this.isInitialLoad.value = false;
        }, 100);
      }
      
      return this.hasMore;
    } catch (error) {
      console.error('加载历史数据失败:', error);
      this.historyLoading.value = false;
      this.isInitialLoad.value = false;
      return false;
    }
  }

  reset(fromDigital?:boolean) {
    this.historyParams.pageNumber = 1;
    this.historyParams.pageSize = 10;
    if(fromDigital) {
      this.historyParams.pageSize = 200;
    }
    this.historyParams.total = 0;
    this.hasMore = true;
    this.historyLoading.value = false;
    this.isInitialLoad.value = true;
  }
}

export { HistoryService }; 