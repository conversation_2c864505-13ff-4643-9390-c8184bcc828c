<template>
  <div class="personal-card-wrapper">
    <CardShell class="personal-card" v-for="(item) in data.userList" :key="item.id" @click="callPersonal(item)">
      <div class="personal-info">
        <div class="header">
          <!-- TODO: 头像, 先用div占位 -->
          <img class="icon" :src="item.avatarUrl" alt="">
          <!-- <div class="icon"></div> -->
          <div class="name ellipsis">{{item.name}}</div>
          <div class="phone-number" v-if="item.telNumber">
            <i class="iconfont ai-icon-dadianhua-xian icon-font"></i>
            <span class="number">{{item.telNumber}}</span>
          </div>
        </div>
        <div class="department ellipsis font-setting">{{item.deptName}}</div>
        <div class="post ellipsis font-setting">{{item.postName}}</div>
      </div>
    </CardShell>
  </div>
</template>
<script lang="ts" setup>
import { callUp } from '@/plugins/app-plugin';
import CardShell from '@/components/card-shell/index.vue';
const props = defineProps({
  data: {
    type: Object,
    default: ()=>({}),
  }
});
function callPersonal(item: any) {
  if(!item.telNumber) {
    return;
  }
  const params = {
    phoneNumber: item.telNumber,
  }
  callUp(params, (data:any)=>{
    console.log('callPersonal', data);
  });
}
</script>

<style lang="scss" scoped>
.personal-card {
  width: 100%;
  .personal-info {
    background: #F7F7F7;
    padding: 8px;
    border-radius: 8px;
    line-height: 22px;
    font-size: 14px;
    .header {
      display: flex;
      align-items: center;
      margin-bottom: 4px;
      .icon {
        width: 22px;
        height: 22px;
        margin-right: 8px;
        border-radius: 50%;
      }
      .name {
        font-weight: 600;
        flex: 1;
      }
      .phone-number {
        height: 20px;
        background: #EDF2FC;
        color: #4379FF;
        font-size: 12px;
        font-size: var(--theme-font-size0, 12px);
        line-height: 20px;
        line-height: var(--theme-line-height0, 20px);
        padding: 0 4px;
        margin-left: 16px;
        line-height: 20px;
        box-sizing: border-box;
        border-radius: 4px;
      }
      .number {
        margin-left: 3px;
      }
      .icon-font {
        font-size: 14px
      }
    }
    .department {
      margin-bottom: 4px;
    }
    .font-setting {
      color: rgba(0, 0, 0, 0.4);
      color: var(--theme-font-color3, rgba(0, 0, 0, 0.4));
    }
  }

}
</style>