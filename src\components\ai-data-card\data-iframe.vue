<template>
  <div class="data-iframe">
    <div class="iframe-setting" :style="{height:iframeHeight()}" v-html="content"></div>
  </div>
</template>

<script setup lang="ts">
 const props = defineProps({
  content: {
    type: String,
    default: ''
  }
 })
 function iframeHeight() {
  return `${window.innerHeight*0.6}px`
 }
</script>

<style lang="scss" scoped>
  .data-iframe {
    width: 100%;
    height: 100%;
    // overflow: hidden;
    .iframe-setting {
      width: 100%;
      // min-height: 400px;
      height: 440px;
      border: none;
    }
  }
</style>