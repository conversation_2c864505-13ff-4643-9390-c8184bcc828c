<template>
  <div class="recommend-question">
    <div v-if="showTitle" class="title">{{ title }}</div>
    <div @click="recommendQuestion(data)" class="recommend-question-list" v-for="data in recommendData"
      :key="data.index">
      <div class="recommend-question-item">{{ data.subject }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import API from '@/api/index';
import StreamManager from '@/api/stream/manager';
interface RecommendDataItem {
  index: string;
  subject: string;
}
const props = defineProps({
  recommendData: {
    type: Array<RecommendDataItem>,
    default: () => ([]),
  },
  id: {
    type: [Number, String],
    default: () => (''),
  },
  showTitle: {
    type: Boolean,
    default: () => (true),
  },
});


let title = ref('你可以继续跟我说');
function recommendQuestion(data: RecommendDataItem) {
  if (props.id) {
    const currentStream = StreamManager.bindObj[props.id];
    const agentInfo = currentStream.option?.agentInfo;
    const citations = currentStream.option?.params?.citations;
    API.store.action.setState('selectedAgentInfo', agentInfo);
    API.store.action.setStaticData('uploadFileInfo', citations?.[0] || null);
    API.action.sendMessage(data.subject, citations);
  } else {
    API.action.sendMessage(data.subject);
  }
}

</script>

<style lang="scss" scoped>
.recommend-question {
  max-width: 80%;

  .title {
    color: rgba(0, 0, 0, 0.4);
    color: var(--theme-font-color3, rgba(0, 0, 0, 0.4));
    font-size: 12px;
    font-size: var(--theme-font-size0, 12px);
    line-height: 20px;
    line-height: var(--theme-line-height0, 20px);
    margin-bottom: 8px;
  }

  .recommend-question-item {
    display: inline-block;
    padding: 8px 16px;
    color: rgba(0, 0, 0, 1);
    color: var(--theme-font-color0, rgba(0, 0, 0, 1));
    font-size: 16px;
    font-size: var(--theme-font-size2, 16px);
    line-height: 24px;
    line-height: var(--theme-line-height2, 24px);
    background: rgba(255, 255, 255, 0.6);
    border-radius: 20px;
    margin-bottom: 8px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    box-sizing: border-box;
    max-width: 100%;
  }
}
</style>
