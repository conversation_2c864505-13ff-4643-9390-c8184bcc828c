<template>
  <div id="chart-container" class="data-chart">
    <div ref="chart" class="data-chart-inner"></div>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import * as echarts from 'echarts';
import { dataToChart } from './chart-utils/data-transformation';
import { ChartTableType } from '@/types';

const { data, chartType, isPreview } = defineProps<{
  data: ChartTableType;
  chartType: string;
  isPreview: boolean;
}>();

const chart = ref<HTMLElement | null>(null);
let chartDom: echarts.ECharts | null = null;
onMounted(() => {
  initChart();
});
watch(()=>chartType,(newV)=>{
  initChart(newV);
});
function initChart(type = '') {
  const dataLength = data?.data?.length;
  let initChartType = type || chartType || '';
  if (chartDom) {
    chartDom.clear();
  }
  const element = chart.value;
  chartDom = echarts.init(element);
  let option: echarts.EChartsOption = dataToChart(
    data,
    initChartType,
    isPreview
  ) as echarts.EChartsOption;
  chartDom.setOption(option);

  // 监听数据缩放事件 缩放时旋转标签 只有柱状图或折线图
  if (type !== 'pie_chart') {
    chartDom.on('dataZoom', (event: unknown) => {
      const zoomEvent = event as { start: number; end: number };
      // 获取当前 dataZoom 的开始和结束位置（百分比）
      const start = zoomEvent.start;
      const end = zoomEvent.end;
      const total = 100; // 总长度百分比
      const currentLength = end - start; // 当前长度百分比
      let rotateAngle = 0; // 默认不旋转

      // 根据需要调整旋转角度，例如，当缩放比例小于某个阈值时旋转标签
      if (currentLength / total >= 0.5 && dataLength > 3) {
        // 例如，当缩放比例小于50%时旋转标签45度
        rotateAngle = 45; // 设置旋转角度为45度
      } else {
        rotateAngle = 0; // 其他情况下不旋转
      }

      // 更新图表的配置项以改变 axisLabel 的 rotate 属性
      chartDom?.setOption({
        xAxis: {
          axisLabel: {
            rotate: rotateAngle, // 应用新的旋转角度
          },
        },
      });
    });
  }
}
</script>
<style lang="scss">
.data-chart {
  width: 100%;
  height: 260px;
  position: relative; /* 确保内部元素能够正确定位 */
  display: flex;
  flex-direction: column;
  .data-right {
    display: flex;
    // justify-content: space-between;
    align-items: center;
    z-index: 1;
    width: 72px;
    height: 20px;

    .icon-font {
      &:hover {
        color: #1890ff;
      }
    }
  }
  .data-chart-inner {
    width: 100%;
    height: 100%;
  }
  #chart-container {
    flex: 1;
  }
}
</style>
