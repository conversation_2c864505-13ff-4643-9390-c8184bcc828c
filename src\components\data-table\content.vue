<template>
  <div class="data-table" ref="scrollContainer">
    <div class="data-body" @scroll="handleScroll" @click="handleClick">
      <table class="custom-table">
        <thead ref="tableBodyHeader">
          <tr>
            <td :key="item" v-for="item in columns" class="column-name" :title="item">
              <span>{{ item }}</span>
            </td>
          </tr>
        </thead>

        <tbody>
          <tr :key="datas[0]" v-for="datas in tableData">
            <td :key="el" v-for="el in datas" :title="el">
              <span>{{ el }}</span>
            </td>
          </tr>
        </tbody>
      </table>
      <div v-if="showBottomBlur" class="hide-preCode-bottom-box"></div>
      <div v-if="showRightBlur" class="hide-preCode-right-box"></div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted, computed, defineEmits } from 'vue';
import { openWebView } from '@/plugins/app-plugin';
import { ChartTableType } from '@/types';

const { data, isPreview } = defineProps<{
  data: ChartTableType;
  isPreview: boolean;
}>();

let tableData = ref<string[][]>([]);
let scrollContainer = ref<HTMLElement | null>(null);
const tableBodyHeader = ref<HTMLElement | null>(null);
const _$emit = defineEmits(['preview']);

const columns = computed(() => {
  return data?.column_names || [];
});

/**
 * 显示表格底部模糊
 */
const showBottomBlur = computed(() => {
  return data?.data?.length >= 5 && !isPreview;
});

/**
 * 显示表格右侧模糊
 */
const showRightBlur = computed(() => {
  if (tableBodyHeader.value) {
    const clientWidth = tableBodyHeader.value?.clientWidth || 0;
    return document.body.clientWidth - 56 <= clientWidth && !isPreview;
  }
  return false;
});

function handleClick() {
  const dataType = 'table';
  if (!isPreview) {
    _$emit('preview');
    const params = {
      openType: 'normal',
      url: `/pages/preview/preview.html?type=${dataType}&openType=normal`,
      type: dataType,
      screenOrientation: 'landscape', // 横屏
      webviewBg: '',
      webviewBgRgb: "#FFFFFF"
    };
    openWebView(params, data);
  }
}

onMounted(() => {
  tableData.value = data.data;
});
function handleScroll() {
  // TODO: 滚动加载数据
}
</script>
<style lang="scss">
.data-table {
  width: 100%;
  position: relative;
  overflow: auto;
  // overflow-x: auto;
  /* 确保内部元素能够正确定位 */
  .custom-table {
    border-collapse: collapse;
    /* 合并边框 */
  }

  .data-body {
    max-height: 240px;
    max-width: 100%;
    overflow: hidden;

    position: relative;

    .hide-preCode-bottom-box {
      width: 100%;
      height: 50px;
      background-image: linear-gradient(-180deg, rgba(255, 255, 255, 0) 0%, #fff 100%);
      position: absolute;
      left: 0;
      bottom: 0;
      z-index: 10;
    }
    .hide-preCode-right-box {
      height: 100%;
      width: 50px; // 模糊区域宽度
      position: absolute;
      top: 0;
      right: 0;
      z-index: 10;

      background-image: linear-gradient(to right, rgba(255, 255, 255, 0) 0%, #fff 100%);
    }
  }
  .custom-table,
  .custom-table th,
  .custom-table td {
    border: 1px solid #bdc5d6;
    white-space: normal;
  }
  .custom-table {
    width: 100%;
    .column-name {
      background-color: #f3f6ff;
      color: #333;
    }
    th,
    td {
      min-width: 68px;
      /* 设置边线颜色为蓝色 */
      padding: 8px 12px;
      height: 39px;
      box-sizing: border-box;
      text-align: center;
      span {
        word-break: keep-all;
        white-space: nowrap;
      }
    }
    tbody {
      background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, #ffffff 100%) !important;
    }
  }
}
</style>
