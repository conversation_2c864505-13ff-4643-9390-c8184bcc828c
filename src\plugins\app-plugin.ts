import nativeSdk from '@/api/sdk';
import { setLocalStorage, getDeviceInfo, getQueryString } from '@/utils/common';
import voice from '@/api/voice';
import store from '@/api/store';
import { BASEURL } from '@/const/const';
import { computed } from 'vue';
const { isApp } = getDeviceInfo();
type dataType = string | object
// 获取系统ui TODO: getSystemUI 名字不好，这个插件也能设置systemUI
export const getSystemUI = (actionName: string, callback?: Function) => {
  nativeSdk.appPlugin.SystemUiPlugin(
    {},
    actionName,
    (data: dataType) => {
      callback && callback(data);
    },
    (err: string) => {
      console.log(err);
    }
  );
};
let currWebviewBg: string | null = null;
export const setSystemUI = (params: any, actionName: string) => {
  if (actionName === 'setWebviewBg') {
    if (currWebviewBg === params.webviewBg) {
      return;
    }
    currWebviewBg = params.webviewBg;
  }
  nativeSdk.appPlugin.SystemUiPlugin(
    params,
    actionName,
    (data: dataType) => {
      // callback(data);
    },
    (err: string) => {
      console.log(err);
    }
  );
};
// 恢复输入框状态
export const setInputState = (params: object) => {
  nativeSdk.appPlugin.InputPlugin(
    params,
    'canInput',
    (data: dataType) => { },
    (err: string) => { }
  );
};

// 语音播报
export const voiceAnnouncements = (params = {}, actionName: string, callback?: Function) => {
  nativeSdk.appPlugin.SpeechPlugin(
    params,
    actionName,
    (data: dataType) => {
      if (data) {
        console.log(data, 'voice');
        callback && callback(data);
      }
    },
    (err: string) => {
      console.log(err, 'err voice');
    }
  );
};

/**
 * 原生confirm弹窗
 * @param {*} params
 * @param {*} actionName
 * @param {*} callback
 */
export const showDialog = (params: object, actionName: string, callback?: Function) => {
  nativeSdk.appPlugin.DialogPlugin(
    params,
    actionName,
    (data: string) => {
      callback && callback(data);
    },
    (err: string) => {
      console.log(err);
    }
  );
};
/**
 * 显示隐藏原生输入框
 * @param {*} params {visible: true/false}
 * @param {*} callback
 */
export const showOrHideInput = (params: object, callback?: Function) => {
  nativeSdk.appPlugin.InputPlugin(
    params,
    'visible',
    (data: string | object) => {
      callback && callback(data);
    },
    (err: string) => {
      console.log(err);
    }
  );
};

/**
 * 初始化语音播报
 */
export const initSpeech = (success?: Function, error?: Function) => {
  console.log('initSpeech');
  nativeSdk.appPlugin.SpeechPlugin({}, 'initSpeech', success, error);
};

export const releaseSpeech = () => {
  nativeSdk.appPlugin.SpeechPlugin({}, 'release', (data: string | object) => { });
};

/**
 * 打开webview页面
 * @param {*} params url: 跳转link, type: 页面类型 可作为数据缓存的key, openType: 打开类型, webviewBg: webview背景色，screenOrientation: 横竖屏， layoutHeaderHeight: 头部高度
 * @param {*} data 要带到新开页面的数据
 */
export const openWebView = (params: any, data: dataType = '') => {
  store.action.changePlayState(false)
  voice.stop();
  // TODO 后面区分打开类型
  if (data) {
    setLocalStorage(params.type, data);
  }
  const baseUrl = computed(() => {
    return import.meta.env.PROD ? '/comi' : '';
  });
  // 1->2 从API.store.state获取高度，2->3 xxx 直接从url地址上获取
  const layoutHeaderHeight = store.state.systemStyle.top || getQueryString('layoutHeaderHeight')
  if(params.notNeedBaseUrl) {
    params.url = location.origin + baseUrl.value + params.url;
  }else {
    params.url = location.origin + baseUrl.value + params.url + '&layoutHeaderHeight=' + layoutHeaderHeight;
  }
  // 带输入框的webview页面
  if (params.openType === 'input') {
    releaseSpeech();
    nativeSdk.appPlugin.WebPagePlugin(
      params,
      'openInputWebPage',
      (data: dataType) => { },
      (err: string) => { }
    );
  } else {
    // 不带输入框的webview页面
    nativeSdk.appPlugin.WebPagePlugin(
      params,
      'openWebPage',
      (data: dataType) => { },
      (err: string) => {
        console.log(err);
      }
    );
  }
  if (!isApp) {
    window.open(params?.url);
  }
};

// 关闭webview页面
export const closeWebView = (openType: string, callback?: Function) => {
  // TODO 后面区分关闭类型
  if (openType === 'input') {
    voice.stop()
    nativeSdk.appPlugin.WebPagePlugin(
      {},
      'closeInputWebPage',
      (data: dataType) => { },
      (err: string) => { }
    );
  } else {
    nativeSdk.appPlugin.WebPagePlugin(
      {},
      'closeWebPage',
      (data: dataType) => { },
      (err: string) => {
        console.log(err);
      }
    );
  }
};

export const getToken = (params = {}, callback?: Function) => {
  nativeSdk.loginPlugin.getToken(params, 'getToken', (data: dataType) => {
    callback && callback(data);
  }, (err: string) => {

  })
}

export const refreshToken = (params = {}, callback?: Function) => {
  nativeSdk.loginPlugin.refreshToken(params, 'refreshToken', (data: dataType) => {
    callback && callback(data);
  }, (err: string) => {

  })
}

export const logoutPlugin = (params = {}, callback?: Function) => {
  nativeSdk.loginPlugin.logout(params, 'logout', (data: dataType) => {
    callback && callback(data);
  }, (err: string) => {

  })
}

export const mainWebPageSlide = (params = {}, callback?: Function) => {
  nativeSdk.appPlugin.WebPagePlugin(
    params,
    'mainWebPageSlide',
    (data: dataType) => {
      callback && callback(data);
    },
    (err: string) => {
      console.log(err);
    }
  );
}

// 设置历史数据 id
export const setHistorySessionID = (params = {}, callback?: Function) => {
  nativeSdk.appPlugin.SystemUiPlugin(
    params,
    'setHistorySessionId',
    (data: dataType) => {
      callback && callback(data);
    },
    (err: string) => {
      console.log(err);
    }
  );
}

export const initWebPageParams = (params = {}, callback?: Function) => {
  nativeSdk.appPlugin.SystemUiPlugin(
    params,
    'initWebPageParams',
    (data: dataType) => {
      callback && callback(data);
    },
    (err: string) => {
      console.log(err);
    }
  );
}

// APP 体验模式次数超出限制插件
export const appExeededLimit = (params = {}, callback?: Function) => {
  nativeSdk.appPlugin.SystemUiPlugin(
    params,
    'experienceModeQuotaExceeded',
    (data: dataType) => {
      callback && callback(data);
    },
    (err: string) => {
      console.log(err);
    }
  );
}

/**
 * 获取历史会话ID
 */
export const getHistorySessionId = (params = {}, callback?: Function) => {
  nativeSdk.data.DataPlugin(
    params,
    'get',
    (data: dataType) => {
      callback && callback(data);
    },
    (err: string) => {
      console.log(err);
    }
  );
}
export const cleanInputInfo = (params = {}, callback?: Function) => {
  nativeSdk.appPlugin.InputPlugin(
    params,
    'clearInfo',
    (data: dataType) => {
      callback && callback(data);
    },
    (err: string) => {
      console.log(err);
    }
  );
}
export const callUp = (params = {}, callback?: Function) =>{
  nativeSdk.appPlugin.SystemUiPlugin(
    params,
    'call',
    (data: dataType) => {
      callback && callback(data);
    },
    (err: string) => {
      console.log(err);
    }
  );
}

export const downloadFile = (params = {}, callback?: Function) =>{
  nativeSdk.appPlugin.SystemUiPlugin(
    params,
    'browserDownload',
    (data: dataType) => {
      callback && callback(data);
    },
    (err: string) => {
      console.log(err);
    }
  );
}
