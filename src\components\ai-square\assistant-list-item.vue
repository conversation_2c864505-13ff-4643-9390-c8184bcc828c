<template>
  <div class="assistant-list-item" @click="jumpToAssistant">
    <img v-if="item.iconUrl" :src="getAgentIconUrl(item)" alt="" class="icon" />
    <div v-else class="name">{{ item?.name?.slice(0, 2) }}</div>
    <div class="content">
      <div class="title">
        <span v-for="(part, index) in processedText" :key="index">
          <span v-if="part.isHighlight" class="highlight">{{ part.text }}</span>
          <span v-else>{{ part.text }}</span>
        </span>
      </div>
      <div class="description">{{ item.introduce }}</div>
      <div class="line" v-if="!isLast"></div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { openWebView } from '@/plugins/app-plugin';
import { computed } from 'vue';
import { AssistantInfoType } from '@/types';
import { getAgentIconUrl } from '@/utils/common';
const { item, keyword } = defineProps<{
  item: AssistantInfoType;
  isLast: boolean;
  keyword: string;
}>();
console.log('item', item);
/**
 * 根据搜索关键词切割字符串，用于高亮显示
 */
const processedText = computed(() => {
  if (!keyword) {
    return [
      {
        text: item.name,
        isHighlight: false,
      },
    ];
  }
  const parts = item.name.split(new RegExp(`(${keyword})`));
  return parts.filter(Boolean).map((part: string) => ({
    text: part,
    isHighlight: part === keyword,
  }));
});

/**
 * 跳转到助手页面
 */
function jumpToAssistant() {
  let jumpParams = encodeURIComponent(
    JSON.stringify({
      iconUrl: item.iconUrl,
      introduce: item.introduce,
      name: item.name,
    })
  );
  const url = `/pages/index/index.html?assistantId=${item.id}&params=${jumpParams}&fromHistory=false&isSingle=0&openType=input&newBackground=1`;
  const params = {
    url,
    screenOrientation: 'portrait',
    webviewBg: 'webviewLight',
    openType: 'input',
    webviewBgRgb: '#EDF2FC',
  };
  console.log('params', params);
  openWebView(params);
}
</script>

<style lang="scss" scoped>
.assistant-list-item {
  // height: 82px;
  display: flex;
  margin-bottom: 12px;

  .icon {
    width: 48px;
    height: 48px;
    margin-right: 12px;
    border-radius: 50%;
    background-color: #f5f5f5;
  }
  .name {
    width: 48px;
    height: 48px;
    line-height: 48px;
    margin-right: 12px;
    border-radius: 50%;
    background-color: #4379ff;
    background-color: var(--theme-color5, #4379ff);
    color: #fff;
    text-align: center;
  }
  .content {
    flex: 1;
    min-width: 1px;
    font-family: PingFang SC;
    letter-spacing: 0%;
    .title {
      font-weight: 600;
      font-size: 16px;
      line-height: 24px;
      text-overflow: ellipsis;
      overflow: hidden;
      word-break: keep-all;
      white-space: nowrap;
      padding-right: 12px;
      .highlight {
        color: #0052d9; /* 阿里系蓝色 */
        // color: var(--theme-color6, #0052d9);
      }
    }
    .description {
      margin-top: 2px;
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
      color: #00000066;
      display: -webkit-box;
      text-overflow: ellipsis;
      overflow: hidden;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
      word-break: break-all;
      padding-right: 12px;
    }
    .line {
      height: 0.5px;
      width: 100%;
      background: #d8dadf;
      margin-top: 12px;
    }
  }
}
</style>
