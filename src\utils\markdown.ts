import markdownit from 'markdown-it';
// import markdownItContainer from 'markdown-it-container';
// import * as echarts from 'echarts';
import { createApp } from 'vue';
import TextLoading from '../components/ui/ai-text-loading/index.vue';

const md = markdownit({
  typographer: true,
  html: true, // 启用HTML支持
  linkify: true,
  breaks: true
});

let tmpDom: any = document.createDocumentFragment();
let loadingDom = createApp(TextLoading, { inline: true }).mount(tmpDom);
let cursor: any = null;
// ### 自定义插件 光标
function plugin(md: any, options: any) {
  md.core.ruler.after('replacements', 'cursor', (state: any) => {
    if (state.env && state.env.isEnd) {
      return;
    }

    if (!cursor) {
      cursor = new state.Token('html_inline', 'span', 0);
      cursor.content = loadingDom.$el.outerHTML;
    }
    // 如果遇到table标记，加到后面
    if (state.tokens.length && state.tokens[state.tokens.length - 1].type == "table_close") {
      state.tokens.push(cursor);
    } else {
      let index = -1;
      for (let i = state.tokens.length - 1; i >= 0; i--) {
        if (state.tokens[i].type.indexOf('_close') === -1) {
          index = i;
          break;
        }
      }
      const item = state.tokens[index];
      if (item) {
        if (item.tag && item.tag !== 'text') {
          item.attrs = [['class', options.class || 'curour']];
        } else {
          state.tokens.splice(index + 1, 0, cursor);
        }
      }
    }
    console.log('cursor2', state.tokens);
  });
}

md.use(plugin, {
  class: 'cursor',
});
export function markdownRender(input: string, isEnd: boolean) {
  return md.render(input, { isEnd });
}
