<template>
  <div class="personal-center" :style="personalWrap">
    <div v-show="saveLoading" class="transparent-mask"></div>
    <LayoutHeader>我的</LayoutHeader>
    <div class="body-container">
      <div v-show="showOperate">
        <div class="header-container">
          <img v-show="personalInfo.avatar" class="header-icon" :src="personalInfo.avatar">
          <div class="name">{{personalInfo.nickname}}</div>
          <div class="font-color role">{{personalInfo.postName}}</div>
        </div>
        <div class="item-container" >
          <div class="item" v-for="item in personalFnItem" :key="item.id" @click="handleClick(item)">
            <div class="item-title">{{item.title}}</div>
            <span class="iconfont font-color" :class="item.icon"></span>
          </div>
          <div class="recommend-switch">
            <div class="text">助手推荐问题</div>
            <div class="switch">
              <input @change="switchChange" v-model="recommendSwitch" type="checkbox" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <LayoutFooter :buttonText="buttonText" @buttonAction="loginOut" class="padding-setting"></LayoutFooter>
    <KnowledgeDrawer></KnowledgeDrawer>
  </div>
</template>

<script setup lang="ts">
  import LayoutHeader from '@/components/layout/layout-header.vue';
  import LayoutFooter from '@/components/layout/layout-footer.vue';
  import {reactive, ref} from 'vue';
  import {getDeviceInfo, getLocalStorage, setLocalStorage} from '@/utils/common';
  import { showDialog, openWebView, logoutPlugin } from '@/plugins/app-plugin';
  import requests from '@/api/requests';
  import { aiConfirm } from '@/plugins/ai-confirm';
  import { aiToast } from '@/plugins/ai-toast';
  import defaultHeader from '@/assets/images/default-header.png';
  import KnowledgeDrawer from '@/components/knowledge-drawer/index.vue';
  const { isApp } = getDeviceInfo();
  const personalWrap = reactive({
    height: '100vh',
    width: '100%',
    background: isApp ? 'transparent' : '#edf2fc',
  });
  const personalInfo = reactive({
    avatar: '',
    nickname: '',
    postName: ''
  });
  const personalFnItem = [
    {
      title: '帮助反馈',
      icon: 'ai-icon-you',
      id: 'help',
      path: '/pages/feedback-help/feedback-help.html?openType=normal&type=feedbackHelp',
    }
  ]
  const personalSetting = getLocalStorage('personalSetting');
  const buttonText = '退出登录';
  let showOperate = ref(false);
  let recommendSwitch = '0';
  // let recommendSwitch = ref(personalSetting['recommend_question']);
  let saveLoading = ref(false);
  function loginOut() {
    // TODO: 退出登录
    if (!isApp) {
      // h5对话确认
      aiConfirm({
        content: '是否确定退出登录',
        onConfirm: () => {
          // clean();
        },
        onCancel: () => {
          console.log('cancel');
        },
      });
  } else {
    showDialog(
      {
        dialogName: 'defaultDialog',
        message: '是否确定退出登录',
        // title: ''
      },
      'showDialog',
      (data: string)  => {
        if (data) {
          const result = JSON.parse(data);
          if (result.confirmResult === 'sure' ) {
            logoutPlugin({}, (data: any) => {
              console.log(data);
            });
          }
        }
      },
    );
  }
  }
  getUserInfo();

  function getUserInfo() {
    requests.getCurrentUser().then(data => {
      if(Number(data?.code) === 0) {
        const {avatar, nickname, postName} = data.data;
        if(avatar) {
          personalInfo.avatar = 'data:image/jpg;base64, ' + avatar;
        }else {
          
          personalInfo.avatar = defaultHeader;
        }
        personalInfo.nickname = nickname;
        personalInfo.postName = postName;
        showOperate.value = true;
      }
    });
  }

  function handleClick(item: {path: string}) {
    const params = {
      url: item.path,
      screenOrientation: 'portrait',
      openType: 'normal',
      webviewBg: '',
      webviewBgRgb: "#EDF2FC"
    }
    openWebView(params);
  }

  function switchChange() {
    saveLoading.value = true;
    const params = {
      // setItem: 'recommendSwitch',
      'recommend_question': recommendSwitch.value ? '1' : '0',
      // setValue: recommendSwitch.value,
    }
    requests.saveOrUpdateSetting(params).then(data => {
      saveLoading.value = false;
      if(Number(data?.code) === 0) {
        personalSetting.recommendSwitch = recommendSwitch.value;
        setLocalStorage("personalSetting", personalSetting);
      }else if(data?.message){
        aiToast({
          content: data.message,
        });
      }
    }, err=>{
      saveLoading.value = false;
    });
  }

</script>

<style lang="scss" scoped>
  .personal-center {
    display: flex;
    flex-direction: column;
    position: relative;
    .body-container {
      flex: 1;
      text-align: center;
      padding: 0 12px;
      box-sizing: border-box;
      overflow: auto;
      .header-container {
        display: flex;
        // justify-content: center;
        margin-top: 32px;
        flex-direction: column;
        align-items: center;
      }
      .header-icon {
        width: 96px;
        height: 96px;
        border-radius: 50%;
        background-color: #538DE7;
      }
      .name {
        color: rgba(0, 0, 0, 1);
        color: var(--theme-font-color0, rgba(0, 0, 0, 1));
        margin: 12px 0 2px;
        font-size: 16px;
        font-size: var(--theme-font-size2, 16px);
      }
      .role {
        font-size: 14px;
      }
      .font-color {
        color: rgba(0, 0, 0, 0.4);
        color: var(--theme-font-color3, rgba(0, 0, 0, 0.4));
      }
      .item-container {
        margin-top: 32px;
        background: #fff;
        border-radius: 12px;
        padding: 0 12px;
      }
      .item {
        display: flex;
        height: 48px;
        align-items: center;
      }
      .item-title {
        flex: 1 ;
        text-align: left;
        margin-right: 8px;
      }
      .recommend-switch {
        // margin-top: 32px;
        height: 48px;
        display: flex;
        align-items: center;
        // box-sizing: border-box;
        border-top: 1px solid #e3e3e3;
        .text {
          flex: 1;
          text-align: left;
          font-size: 16px;
          font-size: var(--theme-font-size2, 16px);
          line-height: 24px;
          line-height: var(--theme-line-height2, 24px);
        }
        .switch {
          height: 24px;
          margin-left: 8px;
        }
        input[type="checkbox"] {
          appearance: none;
          width: 40px;
          height: 100%;
          position: relative;
          border-radius: 16px;
          cursor: pointer;
          background-color: #D1D1D1;
        }

        input[type="checkbox"]:before {
          content: "";
          position: absolute;
          width: 20px;
          height: 20px;
          background: white;
          left: 2px;
          top: 2px;
          border-radius: 50%;
          transition: left cubic-bezier(0.3, 1.5, 0.7, 1) 0.3s;
          box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.25);
        }
        input[type="checkbox"]:checked:before {
          left: 18px;
        }
        input[type="checkbox"]:checked {
          background-color: #4379FF;
        }
      }
    }
    .padding-setting {
      padding-left: 12px;
      padding-right: 12px;
    }
    .transparent-mask {
      position: absolute;
      left: 0;
      top: 0;
      right: 0;
      bottom: 0;
      background-color: transparent;
    }
  }
</style>

<style>
  body,html {
    overflow: hidden !important;
  }
</style>