import recommendQuestionsJson from '../../public/recommend-questions.json';
import axios from 'axios';
const getRootUrl = () => {
    if (import.meta.env.DEV) {
        // 使用本地数据
        return '';
    } else {
        // 使用线上数据
        return '/comi/app/public';
    }
}
// 获取推荐问题
export const getRecommendQuestions = async (): Promise<any> => {
    console.log('getRecommendQuestions');
    if (!getRootUrl()) {
        return recommendQuestionsJson;
    }
    const url = `${getRootUrl()}/public/recommend-questions.json?t=${new Date().getTime()}`
    const response = await axios.get(url);
    return response.data
}
