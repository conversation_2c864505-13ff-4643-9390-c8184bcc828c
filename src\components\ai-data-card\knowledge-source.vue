<template>
  <div class="knowledge-source">
    <div class="header">
      <div class="title">参考{{ knowledgeLength }}个知识源</div>
      <div class="more-container" @click="handleMoreClick">
        <span class="more-text">全部</span>
        <span class="iconfont ai-icon-you1"></span>
      </div>
    </div>
    <!-- <transition name="fade"> -->
    <div class="content">
      <div
        class="doc-item"
        v-for="doc in currentShowKnowledge"
        :key="doc.id"
        @click="handleKnowledgeClick(doc)"
      >
        <span class="iconfont doc-icon" :class="doc.iconClass"></span> 
        <span class="name">{{ doc.title }}</span>
        <span class="same">相似性: 
          <span class="same-number">{{ doc.similarity }}</span>
          <!-- <span class="same-number">{{ doc.similarity }}%</span> -->
      </span>
      </div>
    </div>
    <!-- </transition> -->
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { BaseType, KnowledgeDataItem } from '@/types/api';
import {showOrHideInput} from '@/plugins/app-plugin';
// import { getDeviceInfo } from '@/utils/common';
import {showKnowledgeDrawer} from '@/plugins/knowledge-drawer'
import {handleKnowledgeClick} from '@/utils/handle-knowledge';
import {ICON_MAP} from '@/const/const';

const knowledgeLength = ref(0);
const props = defineProps({
  knowledgeData: {
    type: Array<KnowledgeDataItem>,
    default: () => [],
  },
});
console.log(props.knowledgeData, "propsprops");
// const iconMap: BaseType = {
//   doc: 'doc doc-color',
//   docx: 'docx docx-color',
//   pdf: 'pdf pdf-color',
//   wps: 'wps wps-color',
//   xlsx: 'xls xlsx-color',
//   txt: 'txt txt-color',
//   news: 'news news-color',
//   ppt: 'ppt ppt-color',
//   bullet: 'iconfont ai-icon-danweigonggao bullet-color',
//   collaboration: 'ai-icon-xietong',
//   documentCenter: 'ai-icon-wendang',
//   form: 'ai-icon-biaodan',
//   edoc: 'ai-icon-gongwen1',
//   cultureBuild: 'ai-icon-wenhuajianshe',
//   addressBook: 'ai-icon-tongxunlu',
//   // questionAndAnswer: '',问答,
//   application: 'ai-icon-yingyong',
//   internalmail: 'ai-icon-xietongyouxiang',
//   reportForms: 'ai-icon-baobiao',
//   timeSchedule: 'ai-icon-shijiananpai',
//   // todoListThird: '', 第三方待办
// };
const collaborationType: BaseType = {
  1: 'collaboration', //协同
  2: 'form', //表单
  3: 'documentCenter', //文档中心的文件
  4: 'edoc', //公文，
  7: 'cultureBuild' , //'bullet'公告，需要显示为文化建设
  8: 'cultureBuild', //'news'新闻，需要显示为文化建设
  9: 'cultureBuild', // 'bbs'讨论
  10: 'cultureBuild', //'inquiry' 调查
  52: 'application', //应用
  // 40: 'cultureBuild' , //'show'大show
  62: 'addressBook', // 通讯录
  127: 'internalmail', // 内部邮箱
  70: 'vreport', // 报表中心
}
const currentShowKnowledge = computed(()=>{
  const allKnowledge = props.knowledgeData[0]; //从all里面取
  const knowledgeArr = allKnowledge?.knowledgeArr || [];
  knowledgeLength.value = allKnowledge.number;
  return allKnowledge.number > 3 ? knowledgeArr.slice(0, 3) : knowledgeArr;
});


function iconClass(doc: any) {
  const iconName = doc.type || doc.name;
  let docType = doc.metadata?.appType;
  if(docType === 2) {
    docType = 1;
  }
  let type = collaborationType[docType] || iconName.split('.').pop();
  const iconType = ICON_MAP[type as keyof typeof ICON_MAP];
  return iconType ? iconType : 'moren-xin default-color';
}

// async function handleClickEvent(item: any) {
//   if (!item.metadata) {
//     const params = {
//       responseType: 'blob',
//     };
//     const {data} = await requests.getShortToken();
//     if (isApp) {
//       const url = `${location.origin}/comi/ai-manager/repository/export/download/file/${item.id}?token=${data}`;
//       window.location.href = url;
//     } else {
//       requests.downloadFile(item.id, params).then(res => {
//         const blob = new Blob([res]);
//         const url = URL.createObjectURL(blob);
//         // downloadFile({url: url});
//         const link = document.createElement('a');
//         link.download = item.name;
//         link.href = url;
//         document.body.appendChild(link);
//         link.click();
//         URL.revokeObjectURL(url);
//         document.body.removeChild(link);
//         aiToast({
//           content: '下载完成',
//         });
//       });
//     }
//   } else {
//     jumpToTarget(item);
//   }
// }
// function jumpToTarget(item: any) {
//   let jumpParams: any = {
//     id: item.id,
//     name: item.metadata.title,
//     icon: '',
//     imgSrc: null,
//     parentId: '',
//     target: 'newWindow',
//     href: '',
//     appId: item.metadata.appType,
//     openApi: '',
//     params: {
//       id: item.metadata.entityId,
//       comeFrom: 0,
//     },
//     properties: {
//       branch: false,
//     },
//   };
//   if(item.metadata.appType === 2) {
//     jumpParams.appId = 1;
//   }
//   switch (jumpParams.appId) {
//     case 1: //协同
//       jumpParams.openApi = 'jumpToColSummary';
//       break;
//     case 3: //文档
//       jumpParams.openApi = 'openApp';
//       jumpParams.params = {
//         option: {
//           id: item.metadata.entityId
//         }
//       }
//       break;
//     case 4: //公文
//       jumpParams.openApi = 'openApp';
//       break;
//     case 8: //新闻
//       jumpParams.openApi = 'jumpToNews';
//       break;
//     case 7: //公告
//       jumpParams.openApi = 'jumpToBulletin';
//       break;
//     case 9: //讨论
//       jumpParams.openApi = 'openApp';
//       jumpParams.params.type = 'message';
//       break;
//     case 10: //调查
//       jumpParams.openApi = 'openApp';
//       jumpParams.params.type = 'message';
//       break;
//   }
//   const params = {
//     url: `/pages/native/native.html?openType=normal&type=center`,
//     screenOrientation: 'portrait',
//     openType: 'normal',
//     webviewBg: 'webviewLight',
//     webviewBgRgb: "#FFFFFF",
//     showTitle: true,
//     adaptBottomSafe: true
//   }
//   if(jumpParams.appId === 4){
//     requests.getEdocAffairId(item.metadata.entityId).then(data => {
//       if(Number(data.code) === 0) {
//         const result = data.data;
//         jumpParams.params.option = {
//           affairId: result.linkId,
//           summaryId: item.metadata.entityId,
//           openFrom: 'glwd'
//         }
//         jumpAction(jumpParams, params);
//       }
//     });
//   }else{
//     jumpAction(jumpParams, params);
//   }
// }

// function jumpAction(jumpParams:object, params:object) {
//   localStorage.setItem('jumpParams', JSON.stringify(jumpParams));
//   localStorage.setItem('jumpPage', 'true');
//   openWebView(params);
// }

function handleMoreClick() { 
  showOrHideInput({visible: false});
  showKnowledgeDrawer({
    knowledgeData: props.knowledgeData,
    cancel: function () {
      showOrHideInput({visible: true});
    }
  });
}
</script>

<style lang="scss" scoped>
.knowledge-source {
  margin-top: 8px;
  // border-top: 1px solid #e4e4e4;
  padding: 8px;
  font-size: 14px;
  font-size: var(--theme-font-size1, 14px);
  line-height: 22px;
  line-height: var(--theme-line-height1, 22px);
  border-radius: 4px;
  background: #F6F6F8;
  .header {
    display: flex;
    align-items: center;
    .title {
      color: rgba(0, 0, 0, 0.6);
      color: var(--theme-font-color2, rgba(0, 0, 0, 0.6));
      flex: 1;
    }
    // .ai-icon-shang,
    // .ai-icon-xia {
    //   // font-size: 12px;
    //   color: rgba(0, 0, 0, 0.6);
    //   color: var(--theme-font-color2, rgba(0, 0, 0, 0.6));
    // }
    .more-container {
      color: #4379FF;
      height: 22px;
      display: flex;
      align-items: center;
    }
    .ai-icon-you1 {
      margin-left: 4px;
      font-size: 14px;
    }
  }
  .content {
    // margin-top: 8px;
    background-color: #f7f8f8;
    padding: 8px 0;
    border-radius: 8px;
    .doc-item {
      display: flex;
      // align-items: baseline;
      margin-bottom: 8px;
      line-height: 20px;
      &:last-child {
        margin-bottom: 0;
      }
    }
    .name {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      color: rgba(0, 0, 0, 1);
      color: var(--theme-font-color0, rgba(0, 0, 0, 1));
      font-weight: 600;
    }
    .same {
      margin-left: 4px;
      font-size: 12px;
      font-size: var(--theme-font-size0, 12px);
      color: rgba(0, 0, 0, 0.4);
      color: var(--theme-font-color3, rgba(0, 0, 0, 0.4));
    }
    .same-number {
      margin-left: 4px;
    }
    .doc-icon {
      display: inline-block;
      width: 16px;
      height: 16px;
      margin-right: 4px;
      font-size: 16px;
      color: #4379FF;
    }
    .ppt-color {
      color: #ff9900;
    }
    .pdf-color {
      color: #ff4141;
    }
    .news-color,
    .bullet-color,
    .collaboration-color,
    .docx-color,
    .default-color {
      color: #4379ff;
    }
    .doc-color {
      color: #297ffb;
    }
    .xlsx-color {
      color: #61B109;
    }
  }
  // .fade-enter-active,
  // .fade-leave-active {
  //   animation: showOrHide 0.2s ease-in-out;
  // }
  // @keyframes showOrHide {
  //   0% {
  //     transform: translate(0, 100%, 0);
  //   }
  //   100% {
  //     transform: translate(0, 0, 0);
  //   }

  // }
}
</style>
