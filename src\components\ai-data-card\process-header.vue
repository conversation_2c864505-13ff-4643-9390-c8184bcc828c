<template>
  <div class="process-header">
    <AiStateLoading v-if="process.loading" :inline="true" class="loading-position"></AiStateLoading>
    <!-- 完成图标 -->
    <span v-else class="iconfont complete-icon ai-icon-duihao-yuanxing-mianxing"></span>
    <span class="progress-name">{{ process.title }}</span>
    <span v-if="process.content" @click="expendOrCollapse(process)">
      <span v-if="process.expand" class="iconfont ai-icon-shang"></span>
      <span v-else class="iconfont ai-icon-xia"></span>
    </span>
  </div>
</template>

<script setup lang="ts">
  import AiStateLoading from '@/components/ui/ai-state/loading.vue';
  import { ProcessDataItem} from '@/types/api'
  const props = defineProps({
    process: {
      type: Object,
      default: () => ({}),
    },
  });
  const _$emit = defineEmits(['expendOrCollapse']);
  function expendOrCollapse(process: ProcessDataItem) {
    _$emit('expendOrCollapse', process);
  }
</script>

<style lang="scss" scoped>
.process-header {
  display: flex;
  align-items: center;
  // margin-bottom: 8px;
  .progress-name {
    margin-right: 4px;
    flex: 1;
  }
  .complete-icon {
    display: inline-block;
    margin-right: 4px;
    font-size: 16px;
    color: #4379ff;
  }
  .ai-icon-shang,
  .ai-icon-xia {
    color: rgba(0, 0, 0, 0.4);
    color: var(--theme-font-color3, rgba(0, 0, 0, 0.4));
    cursor: pointer;
  }
  .loading-position {
    margin-right: 4px;
  }
}
</style>
