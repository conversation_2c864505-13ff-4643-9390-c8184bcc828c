<template>
  <div class="show-time-card">
    <span class="time-text">{{ customFormat(staticData.time) }}</span>
  </div>
</template>
<script setup lang="ts">
import { customFormat } from '../../utils/common';

type StaticData = {
  time: number; // 假设 time 是字符串类型
};

const props = defineProps({
  staticData: {
    type: Object as () => StaticData,
    default: () => ({ time: '' }), // 提供默认值
  },
});
</script>

<style lang="scss" scoped>
.show-time-card {
  margin-bottom: 16px;
  text-align: center;
  font-size: 14px;
  font-size: var(--theme-font-size1, 14px);
  .time-text {
    color: rgab(0, 0, 0, 0.4);
    color: var(--theme-font-color3, rgab(0, 0, 0, 0.4));
  }
}
</style>
