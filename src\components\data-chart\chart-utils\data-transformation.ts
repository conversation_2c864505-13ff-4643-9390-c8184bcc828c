import { ChartTableType } from '@/types';
import { CUSTOM_COLOR_10, CUSTOM_COLOR_20 } from './const';
import { calculateCharLength, mergeDeep } from '@/utils/common';
import { GridComponentOption, SeriesOption } from 'echarts';
type MultipleDataItem = {
  value: any;
  show: any;
}
type DataItem = {
  type: string;
  isMultiple?: boolean
  multipleData?: any[]
  data: any[][];
  column_names: string[]
  xAxisDataList?: any[]
  options?: any;
  max?: number
  title?: string
}

type SeriesType = {
  type: string
} & SeriesOption



/**
 * 处理饼图数据，只展示前五legend
 * @param {*} arr 
 * @returns 
 */
function genData(arr: any[]) {
  arr.forEach(function (item, index: number) {
    if (index < 5) {
      item.label = {
        show: true,
        overflow: 'break',
      };
      item.labelLine = {
        show: true,
      };
    } else {
      item.label = {
        show: false,
      };
      item.labelLine = {
        show: false,
      };
    }
  });
  return arr;
}
// 图例组件公共样式
const legendStyle = {
  itemWidth: 8,
  itemHeight: 8,
  padding: [0, 16],
  textStyle: { lineHeight: 20 },
  itemGap: 12,
  icon: 'rect',
  type: 'scroll',
  bottom: 0,
};
const dataZoomStyle = {};
const tooltipStyle = {
  backgroundColor: 'rgba(0, 0, 0, 0.8)',
  borderWidth: 0,
  textStyle: {
    color: '#fff',
  },
};

/**
 * 将数据转为饼图
 * @param {*} data 
 * @param {*} type 
 * @param {*} isPreview 
 * @returns 
 */
export function dataToPieChart(data: DataItem, type: string, isPreview: boolean) {
  // 数据排序处理
  if (data?.column_names?.length >= 3) {
    data.data = data.data.sort((a, b) => b[2].value - a[2].value);
  } else {
    data.data = data.data.sort((a, b) => b[1].value - a[1].value);
  }
  let option = {
    color: data?.data?.length <= 10 ? CUSTOM_COLOR_10 : CUSTOM_COLOR_20, // 自定颜色
    legend: {
      data: [],
      ...legendStyle,
    },
    tooltip: {
      trigger: 'item',
      formatter: function (params: { name: string, data: any, color: string }) {
        let result = '';
        result += `<div style="display: flex; align-items: center;">
                      <span style="display: inline-block; width: 10px; height: 10px; background: ${params.color}; margin-right: 5px;"></span>
                      ${params.name}: ${params.data.show}<br>
                  </div>`;
        return result;
      },
      extraCssText: 'max-width:120px; white-space:normal;word-break:break-all;',
      ...tooltipStyle,
    },
    toolbox: {
      show: true,
      sise: 9,
      feature: {
        myTool1: {},
        myTool2: {},
        myTool3: {},
        myTool4: {},
      },
    },
  } as any;
  option.series = data.column_names.slice(1).map(el => {
    return {
      name: el,
      type: 'pie',
      center: ['50%', '50%'],
      radius: ['40%', '70%'],
      data: [],
    };
  });
  // 遍历数据
  data.data.forEach((dataList: any[], index: number) => {
    if (dataList) {
      let lastIndex = dataList.length - 1;
      let name = '';
      for (let i = 0; i < lastIndex; i++) {
        let item = dataList[i].show || '';
        if (!name) {
          name += item;
        } else {
          name = name + '_' + item;
        }
      }
      option.legend.data.push(name);
      option.series[0].data.push({
        ...dataList[lastIndex],
        name: name,
      });
      option.series[0].data = genData(option.series[0].data);
    }
  });
  option.grid = {
    top: '10px', // 上方边距
    bottom: '10px', // 下方边距
    left: '16px', // 左边距
    right: '16px', // 右边距
    containLabel: true,
  };
  return option;
}

/**
 * 格式化x轴显示数据
 * @param {*} data 原始数据
 * @param {*} dataLength 整个x轴数据长度
 * @returns 
 */
const formatXAxisData = (data: MultipleDataItem, dataLength: number) => {
  const { value, show } = data;
  const length = calculateCharLength(value);
  let newVal = '';
  if (dataLength <= 3) {
    newVal = length > 5 ? value.substring(0, 5) + '\n' + value.substring(5, 10) + '…' : value;
  } else {
    newVal = length > 5 ? value.slice(0, 5) + '…' : value;
  }
  return {
    value: newVal,
    show: show,
  };
};

/**
 * 缩放滑块位置
 * @param {*} dataLength 图表数据长度
 * @returns 返回滑块初始化位置
 */
const changeDataZoomSliderPosition = (dataLength: number) => {
  if (dataLength <= 3) {
    return {
      start: 0,
      end: 100,
    };
  } else {
    return {
      start: 0,
      end: 50,
    };
  }
};

/**
 * 书韩剧转为line图表数据
 * @param {*} data 原始数据
 * @param {*} type 图表类型
 * @param {*} isPreview 是否预览
 * @returns 
 */
export function dataToLineChart(data: DataItem, type: string, isPreview: boolean) {
  const dataLength = data.data.length;
  // 柱状
  let commonStyle = {
    barMaxWidth: '20px', // 柱形最大宽度
    barMinWidth: '8px', // 柱形最小宽度
    barGap: '50%', // 柱形间距为宽度的50%
    itemStyle: {
      borderRadius: [2, 2, 0, 0], // 上方圆角为2
    },
  };
  // 自定义颜色
  let initColor = CUSTOM_COLOR_10;
  if (data?.data?.length > 10) {
    initColor = CUSTOM_COLOR_20;
  }
  // 多条柱状图样式处理
  if (data.column_names?.length >= 3) {
    commonStyle.barGap = '25%';
  }

  let splitNumber = 5;
  const dataZoomConfig = changeDataZoomSliderPosition(dataLength);

  // x轴标签是否旋转
  let axisLabelRotate = 0;
  // 多柱状图数据量大于6旋转
  if (data.isMultiple && dataLength > 6) {
    axisLabelRotate = 45;
  }
  // 单柱状图数据量单3
  if (!data.isMultiple && dataLength > 4) {
    axisLabelRotate = 45;
  }

  let option = {
    color: initColor,
    legend: {
      ...legendStyle,
    },
    xAxis: {
      type: 'category',
      data: [] as MultipleDataItem[],
      axisTick: {
        alignWithLabel: true, // 保证刻度和标签对齐
      },
      axisLabel: {
        rotate: axisLabelRotate,
      },
    },
    yAxis: {
      type: 'value',
      axisLine: { show: true },
      splitNumber: splitNumber,
      axisLabel: {
        show: true,
        formatter: function (value: number) {
          // 万 亿
          if (value) {
            if (value >= 100000000) {
              return (value / 100000000).toFixed(1) + '亿';
            } else if (value >= 10000) {
              return (value / 10000).toFixed(1) + '万';
            }
          }
          return value;
        },
      },
    },
    tooltip: {
      trigger: 'axis',
      formatter: function (params: {
        seriesName: string;
        color: string;
        value: string | number;
      }[]) {
        let result = '';
        params.forEach(function (item) {
          // 使用 ■ 代替默认的圆形图例
          result += `<div style="display: flex; align-items: center;">
                <span style="display: inline-block; width: 10px; height: 10px; background: ${item.color}; margin-right: 5px;"></span>
                ${item.seriesName}: ${item.value}<br>
            </div>`;
        });
        return result;
      },
      position: function (pos: number[], params: any, dom: HTMLElement, rect: any, size: any) {
        var tooltipWidth = dom.offsetWidth; // 获取 tooltip 的宽度
        var tooltipHeight = dom.offsetHeight; // 获取 tooltip 的高度

        var obj = {
          left: 0,
          right: 0,
          top: 0,
          bottom: 0,
        };

        // 计算鼠标到图表边缘的距离
        var distanceToRight = size.viewSize[0] - pos[0];
        var distanceToLeft = pos[0];

        // 根据距离决定 tooltip 的左右位置
        if (distanceToRight > tooltipWidth + 5) {
          obj.left = pos[0] + 5; // 鼠标右侧有足够的空间显示 tooltip
        } else if (distanceToLeft > tooltipWidth + 5) {
          obj.right = size.viewSize[0] - pos[0] + 5; // 鼠标左侧有足够的空间显示 tooltip
        } else {
          // 如果两边空间都不够，可以考虑固定位置或调整 tooltip 的大小
          obj.left = pos[0] + 5; // 默认显示在右侧
        }

        // 计算鼠标到图表顶部的距离
        var distanceToBottom = size.viewSize[1] - pos[1];
        var distanceToTop = pos[1];

        // 根据距离决定 tooltip 的上下位置
        if (distanceToBottom > tooltipHeight + 5) {
          obj.top = pos[1] + 5; // 鼠标下方有足够的空间显示 tooltip
        } else if (distanceToTop > tooltipHeight + 5) {
          obj.bottom = size.viewSize[1] - pos[1] + 5; // 鼠标上方有足够的空间显示 tooltip
        } else {
          // 如果上下空间都不够，可以考虑固定位置或调整 tooltip 的大小
          obj.top = pos[1] + 5; // 默认显示在下方
        }

        return obj;
      },
      axisPointer: {
        type: 'shadow',
      },
      ...tooltipStyle,
    },
    dataZoom: [
      {
        type: 'slider',
        show: true,
        height: 12,
        // left: '16px',
        width: '100%',
        backgroundColor: '#D1E0FF', // 背景颜色
        fillerColor: '#86AEFF', // 选中区域填充颜色
        // bottom: '10px',
        bottom: 30,
        ...dataZoomConfig,
      },
      {
        type: 'inside', // 支持鼠标滚轮缩放
        xAxisIndex: 0,
      },
    ],
    toolbox: {
      show: true,
      feature: {
        myTool1: {},
        myTool2: {},
        myTool3: {},
        myTool4: {},
      },
    },
    series: [] as SeriesType[],
    grid: {} as GridComponentOption
  };
  let seriesType: 'line' | 'bar' = type === 'line_chart' ? 'line' : 'bar';
  if (data.isMultiple) {
    option.series = [];
    data?.multipleData?.forEach(mDataList => {
      let series: SeriesType = {
        name: mDataList[0][0].show,
        type: seriesType,
        areaStyle: {},
        data: mDataList
          .map((el: { value: number, show: string }[]) => el[2])
          .map((item: { value: number, show: string }, index: number) => {
            return {
              value: item.value || index,
              show: item.show || `${index}00%`,
            };
          }),
        ...commonStyle,
      };
      option.series.push(series);
    });
    option.xAxis.data = data?.xAxisDataList?.map(item => {
      return formatXAxisData({
        value: item,
        show: false
      }, dataLength);
    }) || [];
  } else {
    option.series = data.column_names.slice(1).map(el => {
      return {
        name: el,
        type: seriesType,
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: '#4379FF', // 0% 处的颜色
              },
              {
                offset: 1,
                color: 'rgba(85,111,253,0.00)', // 100% 处的颜色
              },
            ],
            global: false,
          },
        },
        data: [],
        ...commonStyle,
      };
    });
    // 遍历数据
    data.data.forEach(dataList => {
      if (dataList) {
        dataList.forEach((el, index) => {
          // 首列
          if (index === 0) {
            option.xAxis.data.push(formatXAxisData(el, dataLength));
          } else {
            (option.series[index - 1].data as any[]).push(el);
          }
        });
      }
    });
  }

  option.grid = {
    top: 10, // 上方边距
    bottom: 45, // 下方边距
    right: 0,
    left: 26,
    containLabel: true,
  };
  // 兼容后端返回图表配置
  try {
    const customOptions = data.options;
    console.log(customOptions, 'customOptions');
    if (customOptions?.yAxis?.axisLabel?.formatter.indexOf('function') > -1) {
      customOptions.yAxis.axisLabel.formatter = eval(customOptions.yAxis.axisLabel.formatter);
    }
    if (customOptions?.tooltip?.formatter.indexOf('function') > -1) {
      customOptions.tooltip.formatter = eval(customOptions.tooltip.formatter);
    }
    // 合并后端返回的自定义echarts配置
    console.log(customOptions, 'customOptions');
    option = mergeDeep(option, customOptions);
  } catch (error) {
    console.log(error);
  }
  return option;
}
/**
 * 将百分号转为数字
 * @param {*} percentageString 
 * @returns 
 */
function percentageToNumber(percentageString: string) {
  // 去掉百分号
  const numberString = percentageString.replace('%', '');
  // 将字符串转换为数字并除以100
  const number = parseFloat(numberString) / 100;
  return number;
}
/**
 * 格式化原始数据
 * @param {*} inputData 
 * @returns 
 */
function dataFormat(inputData: DataItem) {
  let sourceData = inputData.data;
  const percentageRegex = /^\d+(\.\d+)?%$/;
  let max = 0;
  // 维度为3  多条柱状图
  let isMultiple = false;
  if (inputData.column_names.length === 3) {
    isMultiple = true;
  }
  inputData.isMultiple = isMultiple;
  let xAxisDataMap: any = {};
  let recodeMap: any = {};
  // 数据分析
  inputData.data = sourceData.map(datas => {
    let result: any[] = [];
    datas.forEach((el, index) => {
      let show = el;
      let value = el;
      // 复选需要收集第二维度为x周
      if (isMultiple) {
        let showKey = show + '';
        if (index === 1 && !xAxisDataMap[showKey]) {
          xAxisDataMap[showKey] = true;
        }
      }
      if (percentageRegex.test(el)) {
        value = percentageToNumber(show);
      }
      // 最大值
      if (typeof el === 'number') {
        max = Math.max(max, value);
      }
      result.push({
        value,
        show,
      });
    });
    if (isMultiple) {
      let key = datas[0] + '';
      if (!recodeMap[key]) {
        recodeMap[key] = [];
      }
      recodeMap[key].push(result);
    }
    return result;
  });
  // 补全
  if (isMultiple) {
    let xAxisDataList = Object.keys(xAxisDataMap).sort();
    let resList: any[] = [];
    Object.keys(recodeMap).forEach(key => {
      let datas = recodeMap[key];
      let obj: { [key: string]: any } = {};
      datas.forEach((el: any) => {
        obj[el[1].show] = el;
      });
      let tmpList: any[] = [];
      xAxisDataList.forEach(xAxis => {
        if (obj[xAxis]) {
          tmpList.push(obj[xAxis]);
        } else {
          // 补充缺失数据
          tmpList.push([
            { show: key, value: key },
            { show: xAxis, value: xAxis },
            { show: 0, value: 0 },
          ]);
        }
      });
      resList.push(tmpList);
    });
    inputData.multipleData = resList;
    inputData.xAxisDataList = xAxisDataList;
  }
  inputData.max = max;
  // inputData.data[0][1]={value: 10000}
  return inputData;
}

export function dataToChart(data: DataItem, type: string, isPreview: boolean) {
  // const data1 = [
  //   ['AI技术部', 'AI技术部', '100%'],
  //   ['HR COE', 'HR COE', '80%'],
  //   ['HR SSC', 'HR SSC', '60%'],
  //   ['V5AI应用开发部', 'V5AI应用开发部', '20%'],
  //   ['V5产品管理及运营部', 'V5产品管理及运营部', '30%'],
  // ];
  // const data2 = [
  //   ['AI技术部', '100%'],
  //   ['HR COE', '80%'],
  //   ['HR SSC', '60%'],
  //   ['V5AI应用开发部', '20%'],
  //   ['V5产品管理及运营部', '30%'],
  //   ['123', '50%'],
  //   ['V5基础应用开发部', '70%'],
  //   ['V5平台部', '70%'],
  // ];
  // const name1 = ['所属部门', '访问次数', '工作时长'];
  // const name2 = ['所属部门', '工作时长'];
  // const testData1 = true;
  // data = data || {
  //   type: 'bar_chart',
  //   data: testData1 ? data1 : data2,
  //   column_names: testData1 ? name1 : name2,
  //   data_desc: '查询结果匹配用户图表意图图',
  //   title: '',
  // };
  type = type || data.type;
  data = dataFormat(data);
  switch (type) {
    case 'bar_chart':
    case 'line_chart':
      return dataToLineChart(data, type, isPreview);
    case 'pie_chart':
      return dataToPieChart(data, type, isPreview);
    default:
      break;
  }
}
