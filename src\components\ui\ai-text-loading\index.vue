<template>
  <div class="ai-loading" :class="{ 'ai-loading-inline': inline }">
    <span class="dot"></span>
    <span class="dot"></span>
    <span class="dot"></span>
  </div>
</template>

<script setup lang="ts">
const { inline } = defineProps({
  inline: {
    type: Boolean,
    default: false,
  },
});
</script>

<style lang="scss" scoped>
.ai-loading {
  .dot {
    display: inline-block;
    width: 4px;
    height: 4px;
    background-color: #4379ff;
    opacity: 0.4;
    margin-right: 4px;
    border-radius: 2px;
    animation: loading 1.2s infinite ease-in-out;
    &:first-child {
      animation-delay: 0;
    }
    &:nth-child(2) {
      animation-delay: 0.4s;
    }
    &:last-child {
      animation-delay: 0.8s;
    }
  }
}
.ai-loading-inline {
  display: inline;
  position: relative;
  top: -3px;
  margin-left: 8px;
}
@keyframes loading {
  0% {
    opacity: 0.4;
    scale: 1;
  }
  50% {
    opacity: 1;
    scale: 1.5;
  }
  100% {
    opacity: 0.4;
    scale: 1;
  }
}
</style>
