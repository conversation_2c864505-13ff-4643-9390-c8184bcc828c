@font-face {
  font-family: "doc-iconfont"; /* Project id 550467 */
  src: url('doc-iconfont.eot?t=1745826090523'); /* IE9 */
  src: url('doc-iconfont.eot?t=1745826090523#iefix') format('embedded-opentype'), /* IE6-IE8 */
       url('doc-iconfont.woff2?t=1745826090523') format('woff2'),
       url('doc-iconfont.woff?t=1745826090523') format('woff'),
       url('doc-iconfont.ttf?t=1745826090523') format('truetype'),
       url('doc-iconfont.svg?t=1745826090523#iconfont') format('svg');
}

.docIconfont {
  font-family: "doc-iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.markdown:before {
  content: "\e634";
}

.docx:before {
  content: "\e635";
}

.moren-xin:before {
  content: "\e60c";
}

.Frame:before {
  content: "\e708";
}

.ofd:before {
  content: "\e967";
}

.suoding:before {
  content: "\e657";
}

.info:before {
  content: "\e913";
}

.zhi_chat:before {
  content: "\e915";
}

.report:before {
  content: "\e6cc";
}

.syz:before {
  content: "\e8f6";
}

.ddz:before {
  content: "\e8f7";
}

.defaultICON:before {
  content: "\e8b8";
}

.km:before {
  content: "\e8b7";
}

.associated-document:before {
  content: "\e7bb";
}

.ppt:before {
  content: "\e73c";
}

.recard:before {
  content: "\e73d";
}

.link:before {
  content: "\e73e";
}

.approval:before {
  content: "\e73f";
}

.meet:before {
  content: "\e740";
}

.video:before {
  content: "\e741";
}

.cal:before {
  content: "\e742";
}

.emptyfile:before {
  content: "\e743";
}

.img:before {
  content: "\e744";
}

.folder:before {
  content: "\e745";
}

.txt:before {
  content: "\e746";
}

.doc:before {
  content: "\e747";
}

.relSys:before {
  content: "\e748";
}

.message:before {
  content: "\e749";
}

.rar:before {
  content: "\e74a";
}

.news:before {
  content: "\e74b";
}

.music:before {
  content: "\e74c";
}

.confirm:before {
  content: "\e74d";
}

.synergy:before {
  content: "\e74e";
}

.sms:before {
  content: "\e74f";
}

.confirmto:before {
  content: "\e750";
}

.cvs:before {
  content: "\e751";
}

.et:before {
  content: "\e752";
}

.flag:before {
  content: "\e753";
}

.exe:before {
  content: "\e754";
}

.minvideo:before {
  content: "\e755";
}

.pdf:before {
  content: "\e756";
}

.pie:before {
  content: "\e757";
}

.wps:before {
  content: "\e758";
}

.squares:before {
  content: "\e759";
}

.html:before {
  content: "\e75a";
}

.xls:before {
  content: "\e75b";
}

.xml:before {
  content: "\e7bc";
}

.shareimg:before {
  content: "\e72a";
}

.shareFolder:before {
  content: "\e72b";
}

.shareUnknown:before {
  content: "\e72c";
}

.sharexls:before {
  content: "\e72d";
}

.sharevideo:before {
  content: "\e72e";
}

.sharedoc:before {
  content: "\e72f";
}

.sharerar:before {
  content: "\e730";
}

.sharehtml:before {
  content: "\e731";
}

.shareexe:before {
  content: "\e732";
}

.sharepdf:before {
  content: "\e733";
}

.shareppt:before {
  content: "\e734";
}

.sharetxt:before {
  content: "\e735";
}

