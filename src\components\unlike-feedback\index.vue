<template>
  <div class="unlike-feedback">
    <AiDrawer :visible="visible" :drawerConfig="drawerConfig" @closeDrawer="closeDrawer">
      <template #content>
        <div class="content">
          <div class="phrase">
            <div
              v-for="phrase in commonPhrases"
              :key="phrase.id"
              :class="{ 'checked-phrase': phrase.checked }"
              @click.stop="selectPhrase(phrase)"
              class="phrase-item"
            >
              {{ phrase.content }}
            </div>
          </div>
          <textarea
            v-model="feedbackMessage"
            class="feedback-message"
            placeholder="如果你愿意的话，欢迎说说你的想法，CoMi会努力做的更好！"
          ></textarea>
        </div>
      </template>
      <template #footer>
        <LayoutFooter
          :buttonText="buttonText"
          :type="buttonType"
          :disabled="disabled"
          @buttonAction="submitAction"
        ></LayoutFooter>
      </template>
    </AiDrawer>
  </div>
</template>

<script setup lang="ts">
import AiDrawer from '@/components/ui/ai-drawer/index.vue';
import LayoutFooter from '@/components/layout/layout-footer.vue';
import {ref, reactive, computed } from 'vue';
type CommonPhrasesItem = {
  id: string;
  content: string;
  checked:  boolean;
}

const _$emit = defineEmits(['confirm', 'close', 'update:isShowDrawer']);
const buttonText = '提交';
const buttonType = 'primary';
let feedbackMessage = ref('');
let visible = ref(true);
const drawerConfig = reactive({
  title: '很抱歉，CoMi让你有了不好的感受',
});
const commonPhrases = reactive<CommonPhrasesItem[]>([
  {
    id: '0',
    content: '没有帮助',
    checked: false,
  },
  {
    id: '1',
    content: '信息虚假不真实',
    checked: false,
  },
  {
    id: '2',
    content: '有害/不安全',
    checked: false,
  },
  {
    id: '3',
    content: '结果不理想',
    checked: false,
  },
  {
    id: '4',
    content: '没有理解我的问题',
    checked: false,
  },
  {
    id: '5',
    content: '没有完成任务',
    checked: false,
  },
]);

const disabled = computed(()=>{
  if(feedbackMessage.value || commonPhrases.some(item=>item.checked)) {
    return false;
  }else {
    return true;
  }
})
function selectPhrase(item: CommonPhrasesItem) {
  item.checked = !item.checked;
}
function closeDrawer() {
  visible.value = false;
  _$emit('close');
  _$emit('update:isShowDrawer', false);
}

function submitAction() {
  let message = '';
  let feedbackStr = feedbackMessage.value;
  commonPhrases.forEach(item => {
    if(item.checked) {
      if(message) {
        message += ',' + item.content;
      }else {
        message = item.content;
      }
    }
  });
  if(message && feedbackStr) {
    message += ',' + feedbackStr;
  }else if(!message && feedbackStr){
    message = feedbackStr;
  }
  const feedbackData = {
    feedbackMessage: message.slice(0, 500),
    type: 1, //类型，点踩
  };
  visible.value = false;
  _$emit('confirm', feedbackData);
  _$emit('update:isShowDrawer', false);
}

// function animationEnd() {
//   _$emit('update:isShowDrawer', false);
// }
</script>

<style lang="scss" scoped>
.unlike-feedback {
  .content {
    height: 100%;
    display: flex;
    flex-wrap: wrap;
    flex-direction: column;
    .phrase-item {
      border: 1px solid #e3e3e3;
      border-radius: 4px;
      padding: 5px 8px;
      color: rgba(0, 0, 0, 0.6);
      color: var(--theme-font-color2, rgba(0, 0, 0, 0.6));
      font-size: 14px;
      font-size: var(--theme-font-size2, 14px);
      // margin-right: 8px;
      // margin-bottom: 8px;
      height: 32px;
      line-height: 20px;
      box-sizing: border-box;
      display: inline-block;
    }
    .feedback-message {
      margin: 8px 0 16px 0;
      height: 148px;
      width: 100%;
      padding: 8px;
      box-sizing: border-box;
      border: none;
      border-radius: 8px;
      background: #f7f7f7;
      resize: none;
      overflow: auto;
      outline: 0;
      -webkit-appearance: none;
      -webkit-user-select: text;
      font-size: 14px;
    }
    .checked-phrase {
      border-color: #AAC6FF;
      background: #EDF2FC;
      color: #4379FF;
    }
  }
  .phrase {
    display: flex;
    flex-wrap: wrap;
    flex-direction: row;
    gap: 8px;
  }
}
</style>
