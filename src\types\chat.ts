import { AiRe<PERSON><PERSON><PERSON>, AiData<PERSON><PERSON>, RequestDataType, AssistantInfo as BaseAssistantInfo, CardContentType } from './api';

export interface ChatMessage {
  id: string;
  index: number;
  content: string;
  messageType: number;
  messageTime: string;
  cardData?: any[];
  citationList?: any[];
  assistant?: ExtendedAssistantInfo;
  citationsJson?: string;
  assistantCode?: string;
  assistantId?: string;
  chatSessionId?: string;
  sessionType?: string;
  input?: string;
  hitKnowledgeRunSteps?: null | any[];
  knowledgeData?: any[];
  needMarkBlueRunSteps?: null | any[];
}

export interface HistoryParams {
  pageNumber: number;
  pageSize: number;
  total: number;
}

export interface ExtendedAssistantInfo extends BaseAssistantInfo {
  introduce: string;
  prologue: string;
  guideQuestions?: string[];
  id: string;
  code: string;
}

export interface ChatContext {
  content: string;
  isCompleted: boolean;
  isPlay: boolean;
  isUnlike: boolean;
  timestamp: string;
}

export interface ExtendedCardContent extends CardContentType {
  context: ChatContext;
  isCard: boolean;
}

export interface ChatResponse extends RequestDataType {
  data: {
    content: ChatMessage[];
    pageInfo: {
      pages: number;
    };
  };
} 