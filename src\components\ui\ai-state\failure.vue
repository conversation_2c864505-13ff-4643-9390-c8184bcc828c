<template>
  <div @click="failureClick" class="ai-state-fail">!</div>
</template>

<script setup lang="ts">
import { defineEmits } from 'vue';

defineOptions({
  name: 'AiStateFail'
});

const _$emit = defineEmits(['failureClick']);
const failureClick = () => _$emit('failureClick');
</script>

<style lang="scss" scoped>
.ai-state-fail {
  width: 16px;
  height: 16px;
  background-color: red;
  border-radius: 50%;
  color: #fff;
  color: var(--theme-font-color5, #fff);
  text-align: center;
  font-size: 14px;
  font-size: var(--theme-font-size1, 14px);
  line-height: 16px;
}
</style>
