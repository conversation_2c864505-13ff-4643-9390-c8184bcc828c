(module.exports = {
  "printWidth": 100,
  "overrides": [
    {
      "files": ".prettierrc.js",
      "options": { "parser": "json" }
    }
  ],
  "tabWidth": 2,
  "useTabs": false,
  "semi": true,
  "singleQuote": true,
  "proseWrap": "preserve",
  "arrowParens": "avoid",
  "bracketSpacing": true,
  "endOfLine": "auto",
  "htmlWhitespaceSensitivity": "ignore",
  "ignorePath": ".prettierignore",
  "jsxBracketSameLine": false,
  "jsxSingleQuote": false,
  "prettier.parser": "babylon",
  "requireConfig": false,
  "stylelintIntegration": false,
  "trailingComma": "es5",
  "prettier.tslintIntegration": false
})
