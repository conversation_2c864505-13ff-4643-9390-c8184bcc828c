<template>
  <div class="bi-card-wrapper">
    <ComiExternalBiCard
      :data="cardData"
      ref="biCardRef"
      :card-options="biCardOptions"
     @update:card-options="cardOptionsChange"
    ></ComiExternalBiCard>
  </div>
</template>
<script setup lang="ts">
import { ref, inject, defineExpose } from 'vue';
import type { Ref } from 'vue';
import { openWebView } from '@/plugins/app-plugin';
const props = defineProps({
  cardData: {
    type: Object,
    default: () => {},
  },
});


const biCardRef = ref<any>(null);

// 定义要暴露的方法
const getMarkdownContent = () => {
  return biCardRef.value.getMarkDown() || '';
}

defineExpose({
  getMarkdownContent
});

const typeSourceEnum = {
  App: 'app',
  AppDialog: 'appDialog',
}

const biCardOptions = ref({
  status: false,
  lookSql: false,
  canLookSql: true,
  typeSource: typeSourceEnum.App
})

const cardOptionsChange = (key: any, value: any) => {
  if(key === 'status' && value) {
    jumpToPreview();
  }
}

function jumpToPreview() {
  const params = {
    openType: 'normal',
    url: `/pages/bi-preview/bi-preview.html?type=biData&openType=normal`,
    type: 'biData',
    screenOrientation: 'landscape', // 横屏
    webviewBg: '',
    webviewBgRgb: "#FFFFFF"
  };
  openWebView(params, props.cardData);
}


</script>
<style scoped lang="scss">
.bi-card-wrapper {
  line-height: normal;
}
</style>
