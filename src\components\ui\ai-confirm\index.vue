<template>
  <Transition name="fade" appear @after-leave="afterLeave">
    <div v-if="show" class="ai-confirm-overlay">
      <div class="ai-confrim-content">
        <div v-if="title" class="ai-confirm-title">
          {{ title }}
        </div>
        <div class="ai-confirm-body">
          <div>{{ content }}</div>
          <!-- <div class="need-tips">
          <span class="iconfont" v-if="showTips" @click="changeShowTips" :class="[isShowTips ? 'ai-icon-duihao-yuanxing-mianxing':'ai-icon-weixuanzhong']"></span>
          <span>不再提示</span>
        </div> -->
        </div>
        <div class="ai-confirm-footer">
          <span @click="Cancel" class="confirm-canel">取消</span>
          <span @click="OK" class="confirm-ok">确定</span>
        </div>
      </div>
    </div>
  </Transition>
</template>

<script setup lang="ts">
import { defineEmits, nextTick, onMounted, ref } from 'vue';

const { title, content } = defineProps({
  title: {
    type: String,
    default: '',
  },
  content: {
    type: String,
    default: '确定要执行此操作吗?',
  },
  showTips: {
    type: Boolean,
    default: true,
  },
});

const show = ref(true);
const isShowTips = ref(true);
let isCancel = false;
const _$emit = defineEmits(['confirm', 'cancel', 'update:visible']);
const Cancel = () => {
  _$emit('cancel');
  show.value = false;
};
const OK = () => {
  _$emit('confirm');
  show.value = false;
};
function afterLeave() {
  _$emit('update:visible', false);
}
</script>

<style lang="scss">
.fade-enter-from,
.fade-leave-to,
.fade-enter-from .ai-confrim-content,
.fade-leave-to .ai-confrim-content {
  opacity: 0;
}
.fade-leave-to .ai-confrim-content {
  transform: scale(0);
}

.fade-enter-to,
.fade-leave-from,
.fade-enter-to .ai-confrim-content,
.fade-leave-from .ai-confrim-content {
  opacity: 1;
}

.fade-leave-from .ai-confrim-content {
  transform: scale(1);
}
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease;
}
.fade-enter-active .ai-confrim-content,
.fade-leave-active .ai-confrim-content {
  transition: opacity 0.2s ease, transform 0.2s ease-in-out;
}
.ai-confirm-overlay {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  .ai-confrim-content {
    background-color: #fff;
    width: 80%;
    min-height: 100px;
    border-radius: 8px;

    .ai-confirm-title {
      color: #333;
      color: var(--theme-font-color2, #333);
      margin-bottom: 10px;
    }
    .ai-confirm-body {
      font-size: 16px;
      font-size: var(--theme-font-size2, 16px);
      font-weight: 600;
      text-align: center;
      padding: 30px;
      .need-tips {
        margin-top: 16px;
        color: rgba(0, 0, 0, 0.6);
        color: var(--theme-font-color2, rgba(0, 0, 0, 0.6));
        font-size: 14px;
        font-size: var(--theme-font-size1, 14px);
      }
    }

    .ai-confirm-footer {
      display: flex;
      height: 56px;
      position: relative;
      align-items: center;
      justify-content: center;
    }

    .ai-confirm-footer::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 1px;
      background-color: #e4e4e4;
      transform: scaleY(0.5);
      transform-origin: top;
    }

    .confirm-canel,
    .confirm-ok {
      flex: 1;
      text-align: center;
      position: relative;
    }

    .confirm-ok {
      color: var(--theme-color5);
      &::before {
        content: '';
        position: absolute;
        top: -4px;
        left: 0;
        width: 1px;
        height: 32px;
        background-color: #e4e4e4;
        transform: scaleY(0.5);
      }
    }
  }
}
</style>
