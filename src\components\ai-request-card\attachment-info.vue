<template>
  <div class="attachment-info">
    <div class="container">
      <div class="header docIconfont icon-size" :class="iconClass">
      </div>
      <div class="content">
        <div class="title ellipsis">{{citations.name}}</div>
        <div class="att-info">
          {{fileType}} , {{fileSize}}
        </div>
      </div>
    </div>
    
  </div>
</template>
<script setup lang="ts">
import { FileType, BaseType } from '@/types/api';
import { ref, computed } from 'vue';
import { defineOptions } from 'vue';

defineOptions({
  name: 'AttachmentInfo'
});

const props = defineProps({
  citations : {
    type: Object,
    default: () => ({})
  }
});
// xls、xlsx、csv、docx、txt、pdf、md、markdown
const iconMap: BaseType = {
  // doc: 'doc doc-color',
  pdf: 'pdf pdf-color',
  // wps: 'wps wps-color',
  txt: 'txt txt-color',
  docx: 'docx docx-color',
  markdown: 'markdown markdown-color',
  xlsx: 'xls xls-color',
  xls: 'xls xls-color',
  csv: 'cvs csv-color',
  md: 'markdown markdown-color',
};
// const fileItem = ref<FileType | null>(null);
const fileType = ref<string>('');
const fileSize = ref<string>('');
const iconClass = ref<string>('');

initFile();
function initFile() {
  // 目前只会有一个文件
  fileType.value = props.citations.name.split('.').pop() || '';
  fileSize.value = caculateSize(props.citations.size);
  iconClass.value = iconMap[fileType.value] ? iconMap[fileType.value] : 'defaultICON default-color'
}
//目前限制MB
function caculateSize(fileSize: number) {
  if(fileSize < 1024) {
    return `${fileSize}B`;
  } else if(fileSize < 1024 * 1024) {
    return `${(fileSize/1024).toFixed(2)}KB`;
  } else {
    return `${(fileSize/(1024*1024)).toFixed(2)}MB`;

  }
}

</script>

<style lang="scss" scoped>
.attachment-info {
  // background-color: #fff;
  display: flex;
  box-sizing: border-box;
  align-items: center;
  justify-content: flex-end;
  box-sizing: border-box;
  .container {
    display: flex;
    align-items: flex-start;
    background-color: #fff;
    border-radius: 8px;
    padding: 8px;
    box-sizing: border-box;
    // max-width: 80%;
    width: 100%;
    .header {
      height: 28px;
      width: 28px;
      margin-top: 2px;
    }
    .content {
      // margin-left: 5px;
      font-size: 14px;
      font-size: var(--theme-font-size1, 14px);
      // background-color: #f5f5f5;
      padding: 0 4px;
      flex: 1;
      min-width: 5px;
    }
    .att-info {
      display: flex;
      color: rgba(0, 0, 0, 0.6);
      color: var(  --theme-font-color2, rgba(0, 0, 0, 0.6));
      font-size: 10px;
      font-size: var(--theme-font-size0, 10px);
      line-height: 18px;
      // color: #00000099;
      // line-height: var(--theme-line-height1, 18px);
    }
    .type {
      // flex: 1;
    }
    .icon-size {
      font-size: 28px
    }
    .title {
      font-size: 12px;
      font-size: var(--theme-font-size0, 12px);
      font-weight: 600;
    }
  }
  .default-color {
    color: #333;
  }
  // .xls-color {
  //   color: #00a65a;
  // }
  .doc-color {
    color: #297FFB;
  }
  .pdf-color {
    color: #ff4141;
  }
  .txt-color {
    color: #586A95;
  }
  .markdown-color {
    color: #4A4E5A;
  }
  .xls-color {
    color: #61B109;
  }
  .docx-color {
    color: #4379FF;
  }
  .csv-color {
    color: #3DA6FF;
  }
}
</style>
