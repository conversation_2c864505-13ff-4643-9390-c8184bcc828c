<template>
  <div class="data-header">
    <div class="data-left">
      <div class="title" :title="realTitle">
        {{ realTitle }}
      </div>
    </div>
    <div class="data-right preview-btn" v-if="hasContent">
      <div class="content iconfont ai-icon-fangda" @click.stop="jumpToPreview" />
      <div class="tooltip" v-if="visible">
        <div class="title">
          <span>点击放大，查看全部数据</span>
          <button class="ok-btn" @click="hidePreviewTip">好的</button>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { computed, ref, onMounted, defineExpose } from 'vue';
import { openWebView } from '@/plugins/app-plugin';
import { getLocalStorage, setLocalStorage } from '@/utils/common';
import { ChartTableType } from '@/types';
import API from '@/api'
const { dataType, data } = defineProps<{
  dataType: string;
  data: ChartTableType;
}>();
const isDigtalChat = API.store.staticData.isDigtalChat;
const realTitle = computed(() => {
  return data?.title;
});
// 是否有内容，数字人内容也不显示
const hasContent = computed(() => {
  return data?.data?.length > 0 && !isDigtalChat;
});
let visible = ref(false);

const previewTip = getLocalStorage('previewTip');
if (!previewTip) {
  visible.value = true;
} else {
  visible.value = false;
}
/**
 * 隐藏预览提示 -- 暴露方法给外部调用
 */
defineExpose({ hidePreviewTip });

onMounted(() => {
  if (visible.value) {
    setTimeout(() => {
      hidePreviewTip();
    }, 5000);
  }
});
// 隐藏预览提示
function hidePreviewTip() {
  visible.value = false;
  setLocalStorage('previewTip', true);
}

/**
 * 图标放大预览
 */
function jumpToPreview() {
  if (visible.value) {
    hidePreviewTip();
  }
  const params = {
    openType: 'normal',
    url: `/pages/preview/preview.html?type=${dataType}&openType=normal`,
    type: dataType,
    screenOrientation: 'landscape', // 横屏
    webviewBg: '',
    webviewBgRgb: "#FFFFFF"
  };
  openWebView(params, data);
}
</script>
<style lang="scss" scoped>
.data-header {
  display: flex;
  margin-bottom: 10px;
  // height: 24px;
  align-items: center;
  justify-content: space-between;

  .data-left {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    color: #000000;
    width: calc(100% - 40px);
    .back-icon {
      margin-right: 12px;
    }
  }

  .title {
    font-family: 'PingFang SC';
    font-weight: 600;
    font-size: 16px;
    line-height: 22px;
    letter-spacing: 0px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    word-break: keep-all;
  }
  .preview-btn {
    position: relative;
    margin-left: 24px;
    // padding-right: 12px;
    content {
      font-size: 16px;
    }

    .tooltip {
      position: absolute;
      right: -15px;
      top: 34px;
      z-index: 999;
      width: 242px;
      gap: 16px;
      padding: 12px 16px;
      border-radius: 6px;
      background: #000000cc;
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .ok-btn {
        margin-left: 16px;
        padding: 2px 8px;
        background: #ffffff;
        color: #4379ff;
        width: 40px;
        height: 24px;
        border-radius: 6px;
        font-size: 12px;
        border: none;
        gap: 10px;
      }
      &:before {
        content: '';
        width: 0px;
        height: 0px;
        border-left: 8px solid transparent;
        border-right: 8px solid transparent;
        border-bottom: 8px solid #000000cc;
        position: absolute;
        top: -8px;
        right: 18px;
      }
    }

    .iconfont {
      color: #00000066;
    }
  }
}
</style>
