import { voiceAnnouncements } from '@/plugins/app-plugin';
import Queue from './queue';
import removeMd from 'remove-markdown';
import store from '../store';
// 去掉转义字符
const formatString = (str: string) => {
  const regex = /[\n\r\t\\\"\'\\]+/g;
  return str.replace(regex, '');
};

function play() {
  // 弹出第一个播放
  let item: any = Queue.dequeue();
  // 记录当前播放卡片， 用于语音开关切换判断
  Play.currentItem = item;
  if (item) {
    if (item.loaded) {
    } else {
      const innerText = store.card.getCardToText(item.card.data.cardData);
      console.log(innerText, 'innerText');
      const formatText = formatString(removeMd(innerText));
      console.log('formatText', formatText);
      const params = {
        content: formatText,
        tips: Play.tips
      };
      voiceAnnouncements(params, 'speechContent', () => {
        play();
      });
    }
  } else {
    Play.isRunning = false;
    Play.callback && Play.callback();
  }
}
export default class Play {
  static isRunning: boolean = false;
  static currentItem: any = null;
  static isPaused: boolean = false;
  static callback: null | Function = null;
  static timer: any = null;
  static tips: string;
  // 暂停
  static pause = function () {
    const voiceParams = {
      content: ''
    };
    return new Promise((resolve, reject) => {
      if (Play.isRunning) {
        Play.isRunning = true;
        // 继续播放
        voiceAnnouncements(voiceParams, 'resumeSpeech', (data: any) => {
          resolve(undefined);
        });
        if (!Play.isPaused) {
          Play.isPaused = true;
          voiceAnnouncements(voiceParams, 'pauseSpeech', (data: any) => {
            resolve(undefined);
          });
          // 设置一个定时器，超过60s就终止播放
          Play.timer = setTimeout(() => {
            Play.stop();
          }, 60000); // 每60秒检查一次
        } else {
          if (Play.timer) {
            clearTimeout(Play.timer);
          }
          Play.isPaused = false;
          // 继续播放
          voiceAnnouncements(voiceParams, 'resumeSpeech', (data: any) => {
            resolve(undefined);
          });
        }
      }
    });
  };
  // 停止
  static stop = function () {
    const voiceParams = {
      content: ''
    };
    if (Play.isRunning) {
      store.action.changePlayState(false)
      Play.isRunning = false;
      // 当前播放的对象
      Play.currentItem = null;
      Play.isPaused = false;
      return new Promise((resolve, reject) => {
        // TODO停止播放
        voiceAnnouncements(voiceParams, 'stopSpeech', (data: any) => {
          Play.callback && Play.callback();
          resolve(undefined);
        });
      });
    }
  };
  // 播放
  static run(callback: Function, tips: string = '') {
    if (Play.isRunning) return;
    Play.callback = callback;
    Play.isPaused = false;
    Play.isRunning = true;
    Play.tips = tips;
    play();
  }
}
