<template>
  <div class="introduce-header">
    <!-- 头像，暂时用div占位 -->
    <!-- <img src="" /> -->
    <div class="header-icon-box" v-show="true">
      <img 
        class="header-icon" 
        :src="headerIconUrl" 
      />
    </div>
    <div class="title">
      <div>{{title}}</div>
      <img class="name-logo" src="../../assets/images/comi-name.png" />
    </div>
    <div class="content">{{content}}</div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref, computed } from "vue";

const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  content: {
    type: String,
    default: ''
  }
});

import headerIcon from '@/assets/images/header-icon.gif';
const headerIconUrl = computed(() => {
  return headerIcon + '?t=' + Date.now();
});
</script>

<style lang="scss" scoped>
  .introduce-header {
    margin-top: 24px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

   
    .header-icon-box{
        height: 72px;
        width: 72px;
        border-radius: 50%;
        margin-bottom: 19px;
        overflow: hidden;
      .header-icon {
        width: 100%;
        height: 100%;
        transform: scale(1.43);
      }
    }

    .title {
      display: flex;
      font-size: 24px;
      font-size: var(--theme-font-size5, 24px);
      line-height: 32px;
      line-height: var(--theme-line-height5, 32px);
      color: rgba(0, 0, 0, 1);
      color: var(--theme-font-color0, rgba(0, 0, 0, 1));
      font-weight: 600;
      margin-bottom: 8px;
    }

    .name-logo {
      margin-left: 10px;
      height: 32px;
      // background-image: url("../../assets/images/comi-name.png");
    }

    .content {
      font-weight: 400;
      font-size: 14px;
      font-size: var(--theme-font-size1, 14px);
      line-height: 20px;
      line-height: var(--theme-line-height0, 20px);
      text-align: center;
      color: #333;
      color: var(--theme-font-color2, #333);
    }
  }
</style>