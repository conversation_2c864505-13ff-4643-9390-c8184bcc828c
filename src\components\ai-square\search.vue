<template>
  <div class="search-container">
    <div class="search-header">
      <div class="iconfont ai-icon-zuo back-btn" @click="closeSearch"></div>
      <div class="search-input-wrap">
        <div class="iconfont ai-icon-m3-search search-icon"></div>
        <input
          type="text"
          class="search-input"
          placeholder="搜索智能体"
          v-model="searchValue"
          @input="handleSearchInput"
          ref="searchInput"
        />
        <div
          v-show="showClearIcon"
          class="iconfont ai-icon-cha-yuanxing-mianxing clear-icon"
          @click="clear"
        ></div>
      </div>
    </div>
    <div class="search-content">
      <template v-if="!loading">
        <template v-if="results.length">
          <AssistantListItem
            v-for="(item, index) in results"
            :key="item.id"
            :item="item"
            :isLast="index === results.length - 1"
            :keyword="searchValue"
          />
        </template>
        <Empty v-if="showEmpty" text="还没有这个智能体" />
      </template>
      <Loader v-else />
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted, computed, defineEmits } from 'vue';
import { debounce } from '@/utils/common';
import API from '@/api';
import AssistantListItem from './assistant-list-item.vue';
import Empty from './empty.vue';
import Loader from './loader.vue';
import { AssistantInfoType } from '@/types';

const searchValue = ref<string>('');
const showClearIcon = ref<boolean>(false);
const results = ref<AssistantInfoType[]>([]);
const allAssistants = ref<AssistantInfoType[]>([]);
const loading = ref<boolean>(false);
const hasSearch = ref<boolean>(false);
const searchInput = ref<HTMLInputElement | null>(null);
const _$emit = defineEmits(['closeSearch']);

const showEmpty = computed(() => {
  return hasSearch.value && results.value.length === 0;
});

onMounted(() => {
  // 页面加载默认聚焦输入框唤起键盘
  if (searchInput.value) {
    searchInput.value.focus();
  }
  getAllassistants();
});

/**
 * 根据输入内容请求搜索结果
 */
function debouncedSearch() {
  const value = searchValue.value.trim();
  try {
    if (!value) {
      return;
    }
    loading.value = true;
    const params = {
      labelId: '',
      keyword: value,
    };
    API.requests.getAssistants(params).then(res => {
      loading.value = false;
      hasSearch.value = true;
      if (Number(res?.code) === 0 && res?.data?.content?.length) {
        results.value = res.data.content;
      } else {
        results.value = [];
      }
    });
  } catch (error) {
    loading.value = false;
    console.error('搜索失败:', error);
    results.value = [];
  } finally {
  }
}

// 获取所有助手列表
function getAllassistants() {
  const params = {
    keyword: '',
    labelId: '',
  };
  API.requests.getAssistants(params).then(res => {
    if (Number(res?.code) === 0 && res?.data?.content?.length) {
      allAssistants.value = res.data.content;
    } else {
      allAssistants.value = [];
    }
  });
}

/**
 * 监听输入变化
 */
function handleSearchInput() {
  // console.log(searchValue, 123);
  if (searchValue.value) {
    showClearIcon.value = true;
    hasSearch.value = true;
    const filteredData = allAssistants.value.filter(item => {
      return item.name.includes(searchValue.value) || item.introduce.includes(searchValue.value);
    });
    results.value = filteredData;
  } else {
    hasSearch.value = false;
    showClearIcon.value = false;
    results.value = [];
  }
}
/**
 * 清空搜索条件
 */
function clear() {
  searchValue.value = '';
  showClearIcon.value = false;
  results.value = [];
  hasSearch.value = false;
}

/**
 * 关闭搜索页面
 */
function closeSearch() {
  _$emit('closeSearch', false);
}
</script>
<style lang="scss" scoped>
.search-container {
  padding: 0 12px;
  display: flex;
  flex-direction: column;
  min-height: 1px;
  padding: 0 12px 12px 12px;
  height: 100%;
  .search-header {
    display: flex;
    height: 44px;
    align-items: center;
    justify-content: flex-start;
    .back-btn {
      width: 20px;
      height: 20px;
      color: #333;
    }

    .search-input-wrap {
      display: flex;
      flex: 1;
      height: 32px;
      padding: 0 8px;
      border-radius: 999px;
      align-items: center;
      justify-content: flex-start;
      background-color: #fff;

      .search-input {
        width: 100%;
        border: none;
        margin: 0 5px;
        height: 32px;
      }

      .clear-icon,
      .search-icon {
        color: #00000066;
      }
    }
  }
  .search-content {
    margin-top: 12px;
    overflow: auto;
    flex: 1;
  }
}
</style>
