<template>
  <div>
    <div class="data-shell">
      <AiStateLoading class="text-margin" v-if="data.isLoading" :inline="true" />
      <span v-if="!data.isHistory" :class="{'interrupt-text': !data.isLoading}">{{data.content}}</span>
      <span v-if="data.isHistory" class="interrupt-text">已停止回答</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import AiStateLoading from '@/components/ui/ai-state/loading.vue';
const props = defineProps({
  data: {
    type: Object,
    default: () => ({})
  }
});
</script>

<style lang="scss" scoped>
.data-shell {
  user-select: none;
  background: #fff;
  font-size: 16px;
  font-size: var(--theme-font-size2, 16px);
  line-height: 24px;
  line-height: var(--theme-line-height2, 24px);
  padding: 8px 16px;
  border-radius: 2px 12px 12px 12px;
  display: flex;
  color: rgba(0, 0, 0, 0.6);
  color: var(--theme-font-color2, 0, 0, 0, 0.6);
  align-items: center;
  width: fit-content;
  .interrupt-text {
    color: rgba(0, 0, 0, 0.4);
    color: var(--theme-font-color3, rgba(0, 0, 0, 0.4));
  }
  .text-margin {
    margin-right: 4px;
  }
}
</style>