// let list = require('../../../eg1.json');
import list from '../../../eg1.json';
let sleep = function() {
    return new Promise(resolve => setTimeout(resolve, 10));
};
export async function fetchEventSource(url:string, option:any){
    for (let index = 0; index < list.length; index++) {
        const element = list[index];
        await sleep();
        option.onmessage(element)
    }
    option.onclose();
}